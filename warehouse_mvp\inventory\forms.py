from django import forms
from .models import Inventory

class InventoryForm(forms.ModelForm):
    """库存设置表单（简化版，只包含库存数量）"""

    class Meta:
        model = Inventory
        fields = ['quantity']
        widgets = {
            'quantity': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '0',
                'placeholder': '请输入库存数量'
                # 不设置max属性，避免显示"最低: 10 | 最高: 1000"提示
            })
        }
        labels = {
            'quantity': '当前库存'
        }
        help_texts = {
            'quantity': '当前实际库存数量，不能为负数'
        }

    def clean_quantity(self):
        """验证当前库存"""
        quantity = self.cleaned_data.get('quantity')
        if quantity is not None and quantity < 0:
            raise forms.ValidationError('库存数量不能为负数。')
        return quantity


class InventoryFullForm(forms.ModelForm):
    """完整的库存设置表单（包含所有字段）"""

    class Meta:
        model = Inventory
        fields = ['quantity', 'min_stock', 'max_stock']
        widgets = {
            'quantity': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '0',
                'placeholder': '请输入库存数量'
            }),
            'min_stock': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '0',
                'placeholder': '请输入最低库存'
            }),
            'max_stock': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '0',
                'placeholder': '请输入最高库存'
            })
        }
        labels = {
            'quantity': '当前库存',
            'min_stock': '最低库存',
            'max_stock': '最高库存'
        }
        help_texts = {
            'quantity': '当前实际库存数量',
            'min_stock': '库存预警阈值',
            'max_stock': '最大库存容量'
        }

    def clean(self):
        """表单验证"""
        cleaned_data = super().clean()
        quantity = cleaned_data.get('quantity')
        min_stock = cleaned_data.get('min_stock')
        max_stock = cleaned_data.get('max_stock')

        if quantity is not None and quantity < 0:
            raise forms.ValidationError('库存数量不能为负数。')

        if min_stock is not None and min_stock < 0:
            raise forms.ValidationError('最低库存不能为负数。')

        if max_stock is not None and max_stock < 0:
            raise forms.ValidationError('最高库存不能为负数。')

        if min_stock is not None and max_stock is not None and min_stock > max_stock:
            raise forms.ValidationError('最低库存不能大于最高库存。')

        return cleaned_data
