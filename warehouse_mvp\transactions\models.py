from django.db import models
from django.contrib.auth.models import User
from products.models import Product

# Create your models here.

class Transaction(models.Model):
    """进出库记录模型"""
    TRANSACTION_TYPES = [
        ('IN', '入库'),
        ('OUT', '出库'),
    ]

    product = models.ForeignKey(Product, on_delete=models.CASCADE, verbose_name='商品')
    transaction_type = models.CharField(max_length=3, choices=TRANSACTION_TYPES, verbose_name='交易类型')
    quantity = models.PositiveIntegerField(verbose_name='数量')
    stock_before = models.PositiveIntegerField(default=0, verbose_name='操作前库存')
    stock_after = models.PositiveIntegerField(default=0, verbose_name='操作后库存')
    reason = models.CharField(max_length=200, verbose_name='原因')
    operator = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='操作员')
    notes = models.TextField(blank=True, verbose_name='备注')

    # 审计字段
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='created_transactions',
        verbose_name='创建人'
    )
    updated_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='updated_transactions',
        verbose_name='最后修改人'
    )

    class Meta:
        verbose_name = '进出库记录'
        verbose_name_plural = '进出库记录'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.product.name} - {self.get_transaction_type_display()} {self.quantity}"

    @property
    def is_inbound(self):
        """是否为入库"""
        return self.transaction_type == 'IN'

    @property
    def is_outbound(self):
        """是否为出库"""
        return self.transaction_type == 'OUT'
