<!-- 进出库记录模态框表单 -->
<form method="post" id="transactionModalForm" action="{{ action_url }}">
    {% csrf_token %}
    
    {% if form.non_field_errors %}
        <div class="alert alert-danger">
            {{ form.non_field_errors }}
        </div>
    {% endif %}

    <div class="row">
        <div class="col-md-6">
            <div class="mb-3">
                <label for="{{ form.product.id_for_label }}" class="form-label">
                    {{ form.product.label }} <span class="text-danger">*</span>
                </label>
                {{ form.product }}
                {% if form.product.help_text %}
                    <div class="form-text">
                        <i class="bi bi-info-circle"></i>
                        {{ form.product.help_text }}
                    </div>
                {% endif %}
                {% if form.product.errors %}
                    <div class="text-danger small">{{ form.product.errors.0 }}</div>
                {% endif %}
            </div>
        </div>
        <div class="col-md-6">
            <div class="mb-3">
                <label for="{{ form.transaction_type.id_for_label }}" class="form-label">
                    {{ form.transaction_type.label }} <span class="text-danger">*</span>
                </label>
                {{ form.transaction_type }}
                {% if form.transaction_type.errors %}
                    <div class="text-danger small">{{ form.transaction_type.errors.0 }}</div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- 库存信息显示区域 -->
    <div id="stockInfoModal" class="alert alert-info" style="display: none;">
        <h6><i class="bi bi-info-circle"></i> 当前库存信息</h6>
        <div class="row">
            <div class="col-md-3">
                <small class="text-muted">当前库存</small>
                <div class="fw-bold" id="currentStockModal">-</div>
            </div>
            <div class="col-md-3">
                <small class="text-muted">最低库存</small>
                <div id="minStockModal">-</div>
            </div>
            <div class="col-md-3">
                <small class="text-muted">最高库存</small>
                <div id="maxStockModal">-</div>
            </div>
            <div class="col-md-3">
                <small class="text-muted">库存状态</small>
                <div id="stockStatusModal">-</div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="mb-3">
                <label for="{{ form.quantity.id_for_label }}" class="form-label">
                    {{ form.quantity.label }} <span class="text-danger">*</span>
                </label>
                {{ form.quantity }}
                {% if form.quantity.errors %}
                    <div class="text-danger small">{{ form.quantity.errors.0 }}</div>
                {% endif %}
            </div>
        </div>
        <div class="col-md-6">
            <div class="mb-3">
                <label for="{{ form.reason.id_for_label }}" class="form-label">
                    {{ form.reason.label }} <span class="text-danger">*</span>
                </label>
                {{ form.reason }}
                {% if form.reason.errors %}
                    <div class="text-danger small">{{ form.reason.errors.0 }}</div>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="mb-3">
        <label for="{{ form.notes.id_for_label }}" class="form-label">
            {{ form.notes.label }}
        </label>
        {{ form.notes }}
        {% if form.notes.errors %}
            <div class="text-danger small">{{ form.notes.errors.0 }}</div>
        {% endif %}
    </div>
</form>

<script>
// 模态框中的库存信息获取功能
function initializeModalStockInfo() {
    const productSelect = document.getElementById('id_product');
    const stockInfo = document.getElementById('stockInfoModal');
    
    if (!productSelect || !stockInfo) {
        return;
    }
    
    // 监听商品选择变化
    productSelect.addEventListener('change', function() {
        const productId = this.value;
        if (productId) {
            // 获取库存信息
            fetch(`/transactions/get-stock/?product_id=${productId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('currentStockModal').textContent = data.current_stock;
                        document.getElementById('minStockModal').textContent = data.min_stock;
                        document.getElementById('maxStockModal').textContent = data.max_stock;
                        document.getElementById('stockStatusModal').textContent = data.status;
                        
                        // 根据库存状态设置样式
                        const currentStockEl = document.getElementById('currentStockModal');
                        currentStockEl.className = data.is_low_stock ? 'fw-bold text-danger' : 'fw-bold text-success';
                        
                        stockInfo.style.display = 'block';
                    } else {
                        stockInfo.style.display = 'none';
                        console.warn('获取库存信息失败：' + data.error);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    stockInfo.style.display = 'none';
                });
        } else {
            stockInfo.style.display = 'none';
        }
    });
}

// 当模态框内容加载完成后初始化
setTimeout(initializeModalStockInfo, 100);
</script>
