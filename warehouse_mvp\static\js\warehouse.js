/**
 * 仓库管理系统自定义JavaScript
 * 基于Bootstrap 5的交互增强
 * 作者：AI Assistant
 * 创建时间：2025-01-29
 * 版本：v2.0 - 修复搜索功能
 */

// 等待DOM加载完成
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 仓库管理系统前端脚本已加载 - v2.0 搜索功能已修复！');

    // 显示页面加载器
    showPageLoader();

    // 初始化所有功能
    initializeTooltips();
    initializeAnimations();
    initializeFormEnhancements();
    initializeTableEnhancements();
    initializeAdvancedAnimations();
    initializeRippleEffects();
    initializeRealTimeSearch(); // 启用AJAX搜索
    initializeAsyncTable();
    initializeAsyncForms();

    // 临时强制重置布局以修复空白问题
    setTimeout(() => {
        if (window.WarehouseJS && window.WarehouseJS.forceResetLayout) {
            window.WarehouseJS.forceResetLayout();
        }
        initializeSidebar();
    }, 50);

    initializeEditModal();
    // initializeProductManagement(); // 商品状态功能已移除
    initializeSPANavigation(); // 启用SPA导航

    // 页面加载完成后隐藏加载器
    window.addEventListener('load', function() {
        hidePageLoader();
        initializeScrollAnimations();
    });

    // 初始化用户菜单优化
    initializeUserMenuOptimization();

    // 添加窗口resize事件监听器，处理图表缩放
    let resizeTimeout;
    window.addEventListener('resize', function() {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(() => {
            if (window.WarehouseJS && window.WarehouseJS.ChartManager) {
                console.log('🔄 窗口大小改变，重新调整图表');
                window.WarehouseJS.ChartManager.resizeAllCharts();
            }
        }, 250);
    });
});

/**
 * 初始化Bootstrap工具提示
 */
function initializeTooltips() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

/**
 * 显示页面加载器
 */
function showPageLoader() {
    const loader = document.createElement('div');
    loader.className = 'page-loader';
    loader.innerHTML = `
        <div class="loader-content">
            <div class="loader-spinner"></div>
            <div class="loader-text">仓库管理系统</div>
            <div class="loader-subtext">正在加载中...</div>
        </div>
    `;
    document.body.appendChild(loader);
}

/**
 * 隐藏页面加载器
 */
function hidePageLoader() {
    const loader = document.querySelector('.page-loader');
    if (loader) {
        loader.classList.add('hidden');
        setTimeout(() => {
            loader.remove();
            // 显示页面内容
            const pageContent = document.querySelector('.container, main, .content');
            if (pageContent) {
                pageContent.classList.add('page-content', 'loaded');
            }
        }, 500);
    }
}

/**
 * 初始化页面动画效果
 */
function initializeAnimations() {
    // 为卡片添加淡入动画
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        card.classList.add('animate-in');

        setTimeout(() => {
            card.classList.add('visible');
        }, index * 150);
    });

    // 按钮悬浮效果已移除
}

/**
 * 初始化高级动画效果
 */
function initializeAdvancedAnimations() {
    // 装饰性动画已移除，保留函数结构以避免调用错误
}

/**
 * 初始化滚动动画
 */
function initializeScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('visible');
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);

    // 观察所有需要动画的元素
    const animateElements = document.querySelectorAll('.animate-in:not(.visible)');
    animateElements.forEach(element => {
        observer.observe(element);
    });
}

/**
 * 表单增强功能
 */
function initializeFormEnhancements() {
    // 为所有表单添加提交时的加载状态
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn && !submitBtn.classList.contains('loading')) {
                // 添加加载状态
                submitBtn.classList.add('loading');
                submitBtn.disabled = true;

                // 保存原始文本
                if (!submitBtn.dataset.originalText) {
                    submitBtn.dataset.originalText = submitBtn.innerHTML;
                }

                // 如果表单验证失败，恢复按钮状态
                setTimeout(() => {
                    if (!form.checkValidity()) {
                        restoreButtonState(submitBtn);
                    }
                }, 100);
            }
        });
    });

    // 输入框功能增强（移除聚焦动画）
    const inputs = document.querySelectorAll('.form-control, .form-select');
    inputs.forEach(input => {
        // 移除聚焦动画效果，只保留基本功能
        // 不再添加focused类，避免CSS动画

        // 实时验证
        input.addEventListener('input', function() {
            validateInput(this);
        });

        // 键盘事件增强
        input.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && this.type !== 'textarea') {
                const form = this.closest('form');
                const submitBtn = form.querySelector('button[type="submit"]');
                if (submitBtn && form.checkValidity()) {
                    submitBtn.click();
                }
            }
        });
    });

    // 复选框和单选框增强
    const checkInputs = document.querySelectorAll('.form-check-input');
    checkInputs.forEach(input => {
        input.addEventListener('change', function() {
            const label = this.nextElementSibling;
            if (this.checked) {
                label.style.color = 'var(--primary-color)';
                label.style.fontWeight = '600';
            } else {
                label.style.color = '';
                label.style.fontWeight = '';
            }
        });
    });

    // 文件上传增强
    const fileInputs = document.querySelectorAll('input[type="file"]');
    fileInputs.forEach(input => {
        input.addEventListener('change', function() {
            const fileName = this.files[0]?.name || '未选择文件';
            const label = this.parentElement.querySelector('.file-label');
            if (label) {
                label.textContent = fileName;
            }
        });
    });
}

/**
 * 恢复按钮状态
 */
function restoreButtonState(button) {
    button.classList.remove('loading');
    button.disabled = false;
    if (button.dataset.originalText) {
        button.innerHTML = button.dataset.originalText;
    }
}

/**
 * 输入验证（带动画效果）
 */
function validateInput(input) {
    const value = input.value.trim();
    const isRequired = input.hasAttribute('required');
    const type = input.type;

    // 清除之前的验证状态
    input.classList.remove('is-valid', 'is-invalid', 'shake');

    // 基本验证
    if (isRequired && !value) {
        input.classList.add('is-invalid');
        addValidationAnimation(input, false);
        return false;
    }

    // 类型验证
    if (value) {
        switch (type) {
            case 'email':
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(value)) {
                    input.classList.add('is-invalid');
                    addValidationAnimation(input, false);
                    return false;
                }
                break;
            case 'tel':
                const phoneRegex = /^[\d\s\-\+\(\)]+$/;
                if (!phoneRegex.test(value)) {
                    input.classList.add('is-invalid');
                    addValidationAnimation(input, false);
                    return false;
                }
                break;
            case 'number':
                if (isNaN(value)) {
                    input.classList.add('is-invalid');
                    addValidationAnimation(input, false);
                    return false;
                }
                break;
        }
    }

    // 如果通过验证
    if (value || !isRequired) {
        input.classList.add('is-valid');
        addValidationAnimation(input, true);
    }

    return true;
}

/**
 * 添加验证状态（移除动画效果）
 */
function addValidationAnimation(input, isValid) {
    // 移除所有动画效果，只保留验证状态
    // 不再添加任何transform、scale或shake动画
    // 验证状态通过Bootstrap的is-valid/is-invalid类来显示
}

/**
 * 清除现有的消息提示
 */
function clearExistingMessages() {
    const existingAlerts = document.querySelectorAll('.alert.message-alert');
    existingAlerts.forEach(alert => {
        alert.remove();
    });
    console.log(`🗑️ 已清除 ${existingAlerts.length} 个现有消息`);
}

/**
 * 显示动画消息（带去重机制）
 */
function showAnimatedMessage(message, type = 'info', duration = 5000) {
    // 检查是否已存在相同的消息
    const existingAlerts = document.querySelectorAll('.alert.message-alert');
    for (let alert of existingAlerts) {
        const alertText = alert.textContent.trim().replace(/×$/, ''); // 移除关闭按钮的×符号
        if (alertText === message.trim()) {
            console.log('🔄 消息已存在，跳过重复显示:', message);
            return; // 如果消息已存在，直接返回
        }
    }

    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show message-alert`;
    alertDiv.setAttribute('data-message', message.trim()); // 添加数据属性用于去重
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // 插入到页面顶部
    const container = document.querySelector('.container');
    if (container) {
        container.insertBefore(alertDiv, container.firstChild);

        console.log('✅ 显示消息:', message);

        // 自动隐藏
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.remove();
                        console.log('🗑️ 消息已移除:', message);
                    }
                }, 500);
            }
        }, duration);
    }
}

/**
 * 添加点击波纹效果
 */
function addRippleEffect(element, event) {
    const ripple = document.createElement('span');
    const rect = element.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    const x = event.clientX - rect.left - size / 2;
    const y = event.clientY - rect.top - size / 2;

    ripple.style.width = ripple.style.height = size + 'px';
    ripple.style.left = x + 'px';
    ripple.style.top = y + 'px';
    ripple.classList.add('ripple');

    element.appendChild(ripple);

    setTimeout(() => {
        ripple.remove();
    }, 600);
}

/**
 * 初始化波纹效果
 */
function initializeRippleEffects() {
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(button => {
        button.style.position = 'relative';
        button.style.overflow = 'hidden';

        button.addEventListener('click', function(e) {
            addRippleEffect(this, e);
        });
    });
}

/**
 * 初始化侧边栏功能
 */
function initializeSidebar() {
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.getElementById('main-content');
    const toggleButton = document.getElementById('sidebar-toggle');

    if (!sidebar || !mainContent || !toggleButton) {
        return;
    }

    // 从localStorage获取侧边栏状态
    let sidebarIsCollapsed = localStorage.getItem('warehouse.sidebarIsCollapsed');
    if (sidebarIsCollapsed === null) {
        sidebarIsCollapsed = 'false';
    }

    // 应用初始状态
    applySidebarState(sidebarIsCollapsed === 'true');

    // 绑定切换按钮事件
    toggleButton.addEventListener('click', function() {
        toggleSidebar();
    });

    // 响应式处理
    handleResponsiveSidebar();

    // 初始化各种事件处理
    initializeResizeHandler();
    initializeSidebarKeyboardEvents();
    initializeTouchEvents();
    initializeSidebarObserver();

    // 初始化缩放检测
    detectAndHandleZoom();

    // 移动端遮罩层点击关闭
    const overlay = createSidebarOverlay();
    overlay.addEventListener('click', function() {
        if (window.innerWidth <= 768) {
            hideSidebarMobile();
        }
    });
}

/**
 * 切换侧边栏显示/隐藏
 */
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');

    if (window.innerWidth <= 768) {
        // 移动端逻辑
        if (sidebar.classList.contains('show')) {
            hideSidebarMobile();
        } else {
            showSidebarMobile();
        }
    } else {
        // 桌面端和平板端逻辑
        const isCollapsed = sidebar.classList.contains('collapsed');
        if (isCollapsed) {
            showSidebar();
        } else {
            hideSidebar();
        }
    }
}

/**
 * 显示侧边栏
 */
function showSidebar() {
    const isCollapsed = false;
    applySidebarState(isCollapsed);
    localStorage.setItem('warehouse.sidebarIsCollapsed', 'false');
}

/**
 * 隐藏侧边栏
 */
function hideSidebar() {
    const isCollapsed = true;
    applySidebarState(isCollapsed);
    localStorage.setItem('warehouse.sidebarIsCollapsed', 'true');
}

/**
 * 应用侧边栏状态
 */
function applySidebarState(isCollapsed) {
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.getElementById('main-content');
    const toggleButton = document.getElementById('sidebar-toggle');
    const overlay = document.querySelector('.sidebar-overlay');

    if (isCollapsed) {
        sidebar.classList.add('collapsed');
        mainContent.classList.add('shifted');
        toggleButton.classList.add('shifted');
        if (overlay) overlay.classList.remove('show');
    } else {
        sidebar.classList.remove('collapsed');
        mainContent.classList.remove('shifted');
        toggleButton.classList.remove('shifted');
        if (window.innerWidth <= 768 && overlay) {
            overlay.classList.add('show');
        }
    }

    // 设置aria属性以提高可访问性
    sidebar.setAttribute('aria-expanded', !isCollapsed);
}

/**
 * 处理响应式侧边栏
 */
function handleResponsiveSidebar() {
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.getElementById('main-content');
    const toggleButton = document.getElementById('sidebar-toggle');
    const overlay = document.querySelector('.sidebar-overlay');

    if (!sidebar || !mainContent || !toggleButton) {
        return;
    }

    const screenWidth = window.innerWidth;
    const devicePixelRatio = window.devicePixelRatio || 1;

    // 考虑浏览器缩放的实际屏幕宽度
    const effectiveWidth = screenWidth * devicePixelRatio;

    // 清除所有状态类
    sidebar.classList.remove('show', 'collapsed');
    mainContent.classList.remove('shifted');
    toggleButton.classList.remove('shifted');
    if (overlay) overlay.classList.remove('show');

    if (screenWidth < 768) {
        // 移动端：完全隐藏，通过按钮切换
        mainContent.classList.add('shifted');
        toggleButton.classList.add('shifted');
        // 移动端不应用localStorage状态
    } else if (screenWidth < 992) {
        // 平板端：可折叠模式
        const isCollapsed = localStorage.getItem('warehouse.sidebarIsCollapsed') === 'true';

        if (isCollapsed) {
            // 紧凑模式：显示图标侧边栏
            sidebar.classList.remove('show');
            mainContent.classList.add('compact-mode');
            mainContent.classList.remove('shifted');
        } else {
            // 完整模式：显示完整侧边栏
            sidebar.classList.add('show');
            mainContent.classList.add('shifted');
            mainContent.classList.remove('compact-mode');
        }

        toggleButton.classList.add('shifted');
    } else {
        // 桌面端：默认显示，支持折叠
        const isCollapsed = localStorage.getItem('warehouse.sidebarIsCollapsed') === 'true';

        if (isCollapsed) {
            sidebar.classList.add('collapsed');
            mainContent.classList.add('shifted');
            toggleButton.classList.add('shifted');
        } else {
            // 桌面端正常显示
            mainContent.classList.remove('shifted', 'compact-mode');
            toggleButton.classList.remove('shifted');
            // 移除所有内联样式，让CSS控制
            mainContent.style.marginLeft = '';
            mainContent.style.width = '';
            mainContent.style.maxWidth = '';
            console.log('🖥️ 桌面端正常布局已应用');
        }
    }

    // 设置aria属性
    const isVisible = !sidebar.classList.contains('collapsed') &&
                     (screenWidth >= 992 || sidebar.classList.contains('show'));
    sidebar.setAttribute('aria-expanded', isVisible);

    // 处理浏览器缩放导致的布局问题
    requestAnimationFrame(() => {
        adjustLayoutForZoom();
    });
}

/**
 * 调整布局以适应浏览器缩放
 */
function adjustLayoutForZoom() {
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.getElementById('main-content');

    if (!sidebar || !mainContent) return;

    // 移除所有内联样式，让CSS控制布局
    mainContent.style.marginLeft = '';
    mainContent.style.width = '';
    mainContent.style.maxWidth = '';

    // 强制重新计算布局
    mainContent.offsetHeight;
}

/**
 * 创建侧边栏遮罩层
 */
function createSidebarOverlay() {
    let overlay = document.querySelector('.sidebar-overlay');

    if (!overlay) {
        overlay = document.createElement('div');
        overlay.className = 'sidebar-overlay';
        document.body.appendChild(overlay);
    }

    return overlay;
}

/**
 * 移动端显示侧边栏
 */
function showSidebarMobile() {
    const sidebar = document.getElementById('sidebar');
    const overlay = document.querySelector('.sidebar-overlay');

    if (window.innerWidth <= 768) {
        sidebar.classList.add('show');
        if (overlay) overlay.classList.add('show');

        // 禁用body滚动
        document.body.style.overflow = 'hidden';
    }
}

/**
 * 移动端隐藏侧边栏
 */
function hideSidebarMobile() {
    const sidebar = document.getElementById('sidebar');
    const overlay = document.querySelector('.sidebar-overlay');

    sidebar.classList.remove('show');
    if (overlay) overlay.classList.remove('show');

    // 恢复body滚动
    document.body.style.overflow = '';
}

/**
 * 键盘事件处理
 */
function initializeSidebarKeyboardEvents() {
    document.addEventListener('keydown', function(e) {
        // ESC键关闭移动端侧边栏
        if (e.key === 'Escape' && window.innerWidth <= 768) {
            const sidebar = document.getElementById('sidebar');
            if (sidebar && sidebar.classList.contains('show')) {
                hideSidebarMobile();
            }
        }

        // Ctrl+B 切换侧边栏（桌面端）
        if (e.ctrlKey && e.key === 'b' && window.innerWidth > 768) {
            e.preventDefault();
            toggleSidebar();
        }
    });
}

/**
 * 触摸事件支持
 */
function initializeTouchEvents() {
    const sidebar = document.getElementById('sidebar');
    if (!sidebar) return;

    let startX = 0;
    let startY = 0;
    let isSwipeGesture = false;

    // 触摸开始
    sidebar.addEventListener('touchstart', function(e) {
        startX = e.touches[0].clientX;
        startY = e.touches[0].clientY;
        isSwipeGesture = false;
    }, { passive: true });

    // 触摸移动
    sidebar.addEventListener('touchmove', function(e) {
        if (!isSwipeGesture) {
            const deltaX = Math.abs(e.touches[0].clientX - startX);
            const deltaY = Math.abs(e.touches[0].clientY - startY);

            // 判断是否为水平滑动手势
            if (deltaX > deltaY && deltaX > 10) {
                isSwipeGesture = true;
            }
        }
    }, { passive: true });

    // 触摸结束
    sidebar.addEventListener('touchend', function(e) {
        if (isSwipeGesture && window.innerWidth <= 768) {
            const endX = e.changedTouches[0].clientX;
            const deltaX = endX - startX;

            // 向左滑动关闭侧边栏
            if (deltaX < -50 && sidebar.classList.contains('show')) {
                hideSidebarMobile();
            }
        }
    }, { passive: true });
}

/**
 * 性能优化：使用Intersection Observer监听可见性
 */
function initializeSidebarObserver() {
    const sidebar = document.getElementById('sidebar');
    if (!sidebar || !window.IntersectionObserver) return;

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                // 侧边栏可见时启用动画
                sidebar.style.willChange = 'transform';
            } else {
                // 侧边栏不可见时禁用动画以节省性能
                sidebar.style.willChange = 'auto';
            }
        });
    }, {
        threshold: 0.1
    });

    observer.observe(sidebar);
}

/**
 * 防抖处理窗口大小变化
 */
function initializeResizeHandler() {
    let resizeTimer;
    let lastWidth = window.innerWidth;
    let lastHeight = window.innerHeight;
    let lastDevicePixelRatio = window.devicePixelRatio || 1;

    function handleResize() {
        const currentWidth = window.innerWidth;
        const currentHeight = window.innerHeight;
        const currentDevicePixelRatio = window.devicePixelRatio || 1;

        // 检查是否是有意义的尺寸变化
        const widthChanged = Math.abs(currentWidth - lastWidth) > 10;
        const heightChanged = Math.abs(currentHeight - lastHeight) > 10;
        const zoomChanged = Math.abs(currentDevicePixelRatio - lastDevicePixelRatio) > 0.1;

        if (widthChanged || heightChanged || zoomChanged) {
            handleResponsiveSidebar();

            // 更新记录的值
            lastWidth = currentWidth;
            lastHeight = currentHeight;
            lastDevicePixelRatio = currentDevicePixelRatio;
        }
    }

    window.addEventListener('resize', function() {
        clearTimeout(resizeTimer);
        resizeTimer = setTimeout(handleResize, 150);
    });

    // 监听浏览器缩放变化
    window.addEventListener('orientationchange', function() {
        setTimeout(() => {
            handleResponsiveSidebar();
        }, 300);
    });

    // 监听视觉视口变化（支持的浏览器）
    if (window.visualViewport) {
        window.visualViewport.addEventListener('resize', function() {
            clearTimeout(resizeTimer);
            resizeTimer = setTimeout(handleResize, 100);
        });
    }
}

/**
 * 检测并处理浏览器缩放
 */
function detectAndHandleZoom() {
    const devicePixelRatio = window.devicePixelRatio || 1;
    const zoomLevel = Math.round(devicePixelRatio * 100);

    // 为不同缩放级别添加CSS类
    document.body.classList.remove('zoom-75', 'zoom-100', 'zoom-125', 'zoom-150', 'zoom-200');

    if (zoomLevel <= 80) {
        document.body.classList.add('zoom-75');
    } else if (zoomLevel <= 110) {
        document.body.classList.add('zoom-100');
    } else if (zoomLevel <= 140) {
        document.body.classList.add('zoom-125');
    } else if (zoomLevel <= 175) {
        document.body.classList.add('zoom-150');
    } else {
        document.body.classList.add('zoom-200');
    }

    // 调整布局
    adjustLayoutForZoom();
}

// 添加波纹效果样式
const rippleStyle = document.createElement('style');
rippleStyle.textContent = `
    .ripple {
        position: absolute;
        border-radius: 50%;
        background-color: rgba(255, 255, 255, 0.6);
        transform: scale(0);
        animation: ripple-animation 0.6s linear;
        pointer-events: none;
    }

    @keyframes ripple-animation {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
`;
document.head.appendChild(rippleStyle);

/**
 * 表单提交增强
 */
function enhanceFormSubmission(form) {
    const submitBtn = form.querySelector('button[type="submit"]');
    if (!submitBtn) return;

    // 添加提交动画
    submitBtn.addEventListener('click', function(e) {
        if (!form.checkValidity()) {
            e.preventDefault();

            // 高亮显示第一个无效字段
            const firstInvalid = form.querySelector('.form-control:invalid, .form-select:invalid');
            if (firstInvalid) {
                firstInvalid.focus();
                firstInvalid.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }

            // 显示错误提示
            showErrorMessage('请检查表单中的错误信息');
        }
    });
}

/**
 * 表格增强功能
 */
function initializeTableEnhancements() {
    // 为表格行添加点击效果
    const tableRows = document.querySelectorAll('.table tbody tr');
    tableRows.forEach(row => {
        row.addEventListener('click', function(e) {
            // 如果点击的是按钮或链接，不执行行选择
            if (e.target.closest('a, button')) {
                return;
            }
            
            // 移除其他行的选中状态
            tableRows.forEach(r => r.classList.remove('table-active'));
            
            // 添加当前行的选中状态
            this.classList.add('table-active');
        });
    });
}

/**
 * 显示成功消息
 */
function showSuccessMessage(message) {
    showMessage(message, 'success');
}

/**
 * 显示错误消息
 */
function showErrorMessage(message) {
    showMessage(message, 'danger');
}

/**
 * 显示消息通知
 */
function showMessage(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    // 插入到页面顶部
    const container = document.querySelector('.container');
    if (container) {
        container.insertBefore(alertDiv, container.firstChild);
        
        // 自动隐藏
        setTimeout(() => {
            alertDiv.remove();
        }, 5000);
    }
}

/**
 * AJAX请求封装（增强版）
 */
function makeAjaxRequest(url, options = {}) {
    const defaultOptions = {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    };

    // 添加CSRF令牌
    const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]');
    if (csrfToken) {
        defaultOptions.headers['X-CSRFToken'] = csrfToken.value;
    }

    const finalOptions = { ...defaultOptions, ...options };

    // 显示加载指示器
    showNetworkIndicator(true);

    return fetch(url, finalOptions)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .catch(error => {
            console.error('AJAX请求失败:', error);
            showAnimatedMessage('请求失败，请稍后重试', 'danger');
            throw error;
        })
        .finally(() => {
            // 隐藏加载指示器
            showNetworkIndicator(false);
        });
}

/**
 * 网络状态指示器
 */
function showNetworkIndicator(show) {
    let indicator = document.getElementById('networkIndicator');

    if (show && !indicator) {
        indicator = document.createElement('div');
        indicator.id = 'networkIndicator';
        indicator.className = 'network-indicator';
        indicator.innerHTML = `
            <div class="network-spinner"></div>
            <span>加载中...</span>
        `;
        document.body.appendChild(indicator);
    } else if (!show && indicator) {
        indicator.remove();
    }
}

/**
 * 实时搜索功能
 */
function initializeRealTimeSearch() {
    const searchInputs = document.querySelectorAll('input[name="search"]');

    searchInputs.forEach(input => {
        let searchTimeout;
        const suggestionsContainer = createSuggestionsContainer(input);

        // 阻止搜索表单的默认提交行为
        const searchForm = input.closest('form');
        if (searchForm) {
            searchForm.addEventListener('submit', function(e) {
                e.preventDefault(); // 阻止表单默认提交
                const currentPath = window.location.pathname;
                const query = input.value.trim();

                if (currentPath.includes('inventory') || currentPath.includes('products')) {
                    // 在支持AJAX搜索的页面执行AJAX搜索
                    executeAjaxSearch(query);
                    hideSuggestions(suggestionsContainer);
                } else {
                    // 在其他页面执行页面搜索
                    executePageSearch(query);
                }
            });
        }

        input.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            const query = this.value.trim();
            const currentPath = window.location.pathname;

            // 在支持AJAX搜索的页面直接执行AJAX搜索
            if (currentPath.includes('inventory') || currentPath.includes('products')) {
                searchTimeout = setTimeout(() => {
                    executeAjaxSearch(query);
                    hideSuggestions(suggestionsContainer);
                }, 300);
            } else {
                // 在其他页面显示搜索建议
                if (query.length >= 2) {
                    searchTimeout = setTimeout(() => {
                        performSearch(query, suggestionsContainer, input);
                    }, 300);
                } else {
                    hideSuggestions(suggestionsContainer);
                }
            }
        });

        // 处理回车键提交
        input.addEventListener('keydown', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault(); // 阻止表单默认提交
                const currentPath = window.location.pathname;

                if (currentPath.includes('inventory') || currentPath.includes('products')) {
                    // 在支持AJAX搜索的页面执行AJAX搜索
                    executeAjaxSearch(this.value.trim());
                    hideSuggestions(suggestionsContainer);
                } else {
                    // 在其他页面执行页面搜索
                    executePageSearch(this.value.trim());
                }
            }
        });

        // 点击外部隐藏建议
        document.addEventListener('click', function(e) {
            if (!input.contains(e.target) && !suggestionsContainer.contains(e.target)) {
                hideSuggestions(suggestionsContainer);
            }
        });
    });
}

/**
 * 创建搜索建议容器
 */
function createSuggestionsContainer(input) {
    const container = document.createElement('div');
    container.className = 'search-suggestions';
    container.style.display = 'none';
    input.parentElement.style.position = 'relative';
    input.parentElement.appendChild(container);
    return container;
}

/**
 * 执行搜索 - 使用AJAX局部刷新
 */
function performSearch(query, container, input) {
    const currentPath = window.location.pathname;

    // 如果查询为空，执行空搜索来显示所有结果
    if (!query.trim()) {
        hideSuggestions(container);
        if (currentPath.includes('inventory')) {
            executeAjaxSearch('');
        }
        return;
    }

    // 简单的建议功能 - 显示搜索提示
    const suggestions = [
        { name: `搜索 "${query}"`, action: () => executeAjaxSearch(query) }
    ];

    showSuggestions(suggestions, container, input);
}

/**
 * 执行AJAX搜索
 */
function executeAjaxSearch(query, page = 1) {
    const currentPath = window.location.pathname;

    // 检查是否支持AJAX搜索
    if (!currentPath.includes('inventory') && !currentPath.includes('products')) {
        // 对于不支持的页面，使用页面重定向
        executePageSearch(query);
        return;
    }

    // 构建AJAX请求URL和参数
    let searchUrl, params;

    if (currentPath.includes('inventory')) {
        // 库存页面的搜索
        // 优先从DOM元素中读取当前选择的筛选和排序参数
        const statusSelect = document.getElementById('status');
        const orderBySelect = document.getElementById('order_by');

        const status = statusSelect ? statusSelect.value : '';
        const orderBy = orderBySelect ? orderBySelect.value : '-updated_at';

        searchUrl = '/inventory/api/search/';
        params = new URLSearchParams({
            search: query,
            status: status,
            order_by: orderBy,
            page: page
        });
    } else if (currentPath.includes('products')) {
        // 商品页面的搜索
        searchUrl = '/products/api/search/';
        params = new URLSearchParams({
            search: query,
            page: page
        });
    }

    // 显示加载状态
    showLoadingState();

    // 发送AJAX请求
    fetch(`${searchUrl}?${params}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 根据页面类型更新不同的容器
                if (currentPath.includes('inventory')) {
                    // 检查是否需要更新完整表格（权限可能发生变化）
                    const currentTable = document.getElementById('inventory-table');
                    const currentCheckboxColumn = currentTable ? currentTable.querySelector('.checkbox-column') : null;
                    const hasDeletePermission = data.full_table_html && data.full_table_html.includes('checkbox-column');

                    // 如果权限状态发生变化，更新完整表格
                    if ((currentCheckboxColumn && !hasDeletePermission) || (!currentCheckboxColumn && hasDeletePermission)) {
                        // 权限状态变化，需要更新完整表格
                        const tableContainer = document.querySelector('.unified-table-container');
                        if (tableContainer && data.full_table_html) {
                            tableContainer.innerHTML = data.full_table_html;
                        }
                    } else {
                        // 权限状态未变化，只更新表格内容
                        const tableBody = document.getElementById('inventory-table-body');
                        if (tableBody) {
                            tableBody.innerHTML = data.table_html;
                        }
                    }

                    // 更新库存分页
                    const paginationContainer = document.getElementById('inventory-pagination-container');
                    if (paginationContainer) {
                        paginationContainer.innerHTML = data.pagination_html;
                    }

                    // 重新初始化库存编辑功能
                    initializeInventoryEdit();

                    // 重新绑定分页事件
                    bindPaginationEvents();

                    // 重新绑定库存页面事件
                    if (window.rebindInventoryEvents) {
                        window.rebindInventoryEvents();
                    }

                    // 重新绑定筛选器和排序器事件
                    if (window.bindInventoryFilters) {
                        window.bindInventoryFilters();
                    }

                    // 重新初始化批量操作功能
                    if (window.updateBatchDeleteButtonInventory) {
                        window.updateBatchDeleteButtonInventory();
                    }

                    // 强制重新应用表格样式
                    if (window.reapplyTableStyles) {
                        window.reapplyTableStyles();
                    }
                } else if (currentPath.includes('products')) {
                    // 更新商品表格内容
                    const tableBody = document.getElementById('product-table-body');
                    if (tableBody) {
                        tableBody.innerHTML = data.table_html;
                    }

                    // 更新商品分页
                    const paginationContainer = document.getElementById('product-pagination-container');
                    if (paginationContainer) {
                        paginationContainer.innerHTML = data.pagination_html;
                    }

                    // 重新初始化编辑模态框功能
                    initializeEditModal();
                }

                // 绑定分页点击事件
                bindPaginationEvents();

                console.log(`搜索完成：找到 ${data.total_count} 条记录`);
            } else {
                console.error('搜索失败:', data.error);
                showErrorMessage(data.error, currentPath);
            }
        })
        .catch(error => {
            console.error('搜索请求失败:', error);
            showErrorMessage('搜索请求失败，请稍后重试', currentPath);
        })
        .finally(() => {
            hideLoadingState(currentPath);
        });
}

/**
 * 执行页面搜索（用于非库存页面）
 */
function executePageSearch(query) {
    const currentPath = window.location.pathname;
    let searchUrl = '';

    // 根据当前页面确定搜索URL
    if (currentPath.includes('products')) {
        searchUrl = '/products/';
    } else {
        return;
    }

    // 构建搜索URL并跳转
    const url = new URL(searchUrl, window.location.origin);
    url.searchParams.set('search', query);

    window.location.href = url.toString();
}

/**
 * 显示加载状态（非侵入式）
 */
function showLoadingState(currentPath = window.location.pathname) {
    let tableContainer;

    if (currentPath.includes('inventory')) {
        tableContainer = document.querySelector('#inventory-table');
    } else if (currentPath.includes('products')) {
        tableContainer = document.querySelector('#product-table');
    }

    if (tableContainer) {
        // 移除已存在的加载覆盖层
        const existingOverlay = tableContainer.querySelector('.table-loading-overlay');
        if (existingOverlay) {
            existingOverlay.remove();
        }

        // 创建加载覆盖层
        const loadingOverlay = document.createElement('div');
        loadingOverlay.className = 'table-loading-overlay';
        loadingOverlay.innerHTML = `
            <div class="loading-content">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <p class="mt-2 text-muted mb-0">正在加载数据...</p>
            </div>
        `;

        // 设置覆盖层样式
        loadingOverlay.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
            backdrop-filter: blur(1px);
        `;

        // 确保容器有相对定位
        tableContainer.style.position = 'relative';

        // 添加覆盖层
        tableContainer.appendChild(loadingOverlay);

        console.log('🔄 显示非侵入式加载状态');
    }
}

/**
 * 隐藏加载状态
 */
function hideLoadingState(currentPath = window.location.pathname) {
    let tableContainer;

    if (currentPath.includes('inventory')) {
        tableContainer = document.querySelector('#inventory-table');
    } else if (currentPath.includes('products')) {
        tableContainer = document.querySelector('#product-table');
    }

    if (tableContainer) {
        // 移除加载覆盖层
        const loadingOverlay = tableContainer.querySelector('.table-loading-overlay');
        if (loadingOverlay) {
            loadingOverlay.remove();
            console.log('✅ 隐藏加载状态');
        }
    }
}

/**
 * 显示错误消息（非侵入式）
 */
function showErrorMessage(message, currentPath = window.location.pathname) {
    // 首先隐藏加载状态
    hideLoadingState(currentPath);

    // 显示顶部错误提示
    showAnimatedMessage(message, 'danger');

    console.error('❌ 显示错误消息:', message);
}

/**
 * 绑定分页点击事件
 */
function bindPaginationEvents() {
    const paginationLinks = document.querySelectorAll('.ajax-page-link');
    paginationLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            const page = this.getAttribute('data-page');
            const searchInput = document.querySelector('input[name="search"]');
            const query = searchInput ? searchInput.value : '';

            // 执行搜索
            executeAjaxSearch(query, page);
        });
    });
}

/**
 * 显示搜索建议
 */
function showSuggestions(results, container, input) {
    container.innerHTML = '';

    results.forEach(item => {
        const suggestion = document.createElement('div');
        suggestion.className = 'search-suggestion-item';
        suggestion.innerHTML = `
            <div class="suggestion-title">${item.name}</div>
            <div class="suggestion-meta">${item.meta || ''}</div>
        `;

        suggestion.addEventListener('click', function() {
            hideSuggestions(container);
            // 如果有自定义动作，执行它；否则提交表单
            if (item.action && typeof item.action === 'function') {
                item.action();
            } else {
                input.value = item.name;
                input.closest('form').submit();
            }
        });

        container.appendChild(suggestion);
    });

    container.style.display = 'block';
}

/**
 * 隐藏搜索建议
 */
function hideSuggestions(container) {
    container.style.display = 'none';
}

/**
 * 异步表格加载
 */
function initializeAsyncTable() {
    const tables = document.querySelectorAll('.async-table');

    tables.forEach(table => {
        const loadUrl = table.dataset.loadUrl;
        if (loadUrl) {
            loadTableData(table, loadUrl);
        }
    });
}

/**
 * 加载表格数据
 */
function loadTableData(table, url, page = 1) {
    const tbody = table.querySelector('tbody');
    const loadingRow = createLoadingRow(table);

    // 显示加载状态
    tbody.innerHTML = '';
    tbody.appendChild(loadingRow);

    makeAjaxRequest(`${url}?page=${page}`)
        .then(data => {
            if (data.success) {
                renderTableData(tbody, data.rows);
                updatePagination(table, data.pagination);
            } else {
                showErrorRow(tbody, data.error || '加载数据失败');
            }
        })
        .catch(error => {
            showErrorRow(tbody, '网络错误，请稍后重试');
        });
}

/**
 * 创建加载行
 */
function createLoadingRow(table) {
    const row = document.createElement('tr');
    const colCount = table.querySelectorAll('thead th').length;
    row.innerHTML = `
        <td colspan="${colCount}" class="text-center py-4">
            <div class="loading-spinner"></div>
            <span class="ms-2">加载中...</span>
        </td>
    `;
    return row;
}

/**
 * 渲染表格数据
 */
function renderTableData(tbody, rows) {
    tbody.innerHTML = '';

    if (rows.length === 0) {
        const row = document.createElement('tr');
        const colCount = tbody.closest('table').querySelectorAll('thead th').length;
        row.innerHTML = `
            <td colspan="${colCount}" class="text-center py-4 text-muted">
                <i class="bi bi-inbox"></i> 暂无数据
            </td>
        `;
        tbody.appendChild(row);
        return;
    }

    rows.forEach(rowData => {
        const row = document.createElement('tr');
        row.innerHTML = rowData.html;
        tbody.appendChild(row);
    });
}

/**
 * 显示错误行
 */
function showErrorRow(tbody, message) {
    const row = document.createElement('tr');
    const colCount = tbody.closest('table').querySelectorAll('thead th').length;
    row.innerHTML = `
        <td colspan="${colCount}" class="text-center py-4 text-danger">
            <i class="bi bi-exclamation-triangle"></i> ${message}
        </td>
    `;
    tbody.innerHTML = '';
    tbody.appendChild(row);
}

/**
 * 异步表单提交
 */
function initializeAsyncForms() {
    const forms = document.querySelectorAll('.async-form');

    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            submitFormAsync(this);
        });
    });
}

/**
 * 异步提交表单
 */
function submitFormAsync(form) {
    const formData = new FormData(form);
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;

    // 显示提交状态
    submitBtn.innerHTML = '<span class="loading-spinner"></span> 提交中...';
    submitBtn.disabled = true;

    makeAjaxRequest(form.action, {
        method: form.method || 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(data => {
        if (data.success) {
            showAnimatedMessage(data.message || '操作成功', 'success');

            // 如果有重定向URL，则跳转
            if (data.redirect_url) {
                setTimeout(() => {
                    window.location.href = data.redirect_url;
                }, 1000);
            } else {
                // 重置表单
                form.reset();
            }
        } else {
            showAnimatedMessage(data.error || '操作失败', 'danger');

            // 显示字段错误
            if (data.errors) {
                showFormErrors(form, data.errors);
            }
        }
    })
    .catch(error => {
        showAnimatedMessage('提交失败，请稍后重试', 'danger');
    })
    .finally(() => {
        // 恢复按钮状态
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
}

/**
 * 显示表单错误
 */
function showFormErrors(form, errors) {
    // 清除之前的错误
    form.querySelectorAll('.invalid-feedback').forEach(el => el.remove());
    form.querySelectorAll('.is-invalid').forEach(el => el.classList.remove('is-invalid'));

    // 显示新错误
    Object.keys(errors).forEach(fieldName => {
        const field = form.querySelector(`[name="${fieldName}"]`);
        if (field) {
            field.classList.add('is-invalid');

            const errorDiv = document.createElement('div');
            errorDiv.className = 'invalid-feedback';
            errorDiv.textContent = errors[fieldName][0];
            field.parentElement.appendChild(errorDiv);
        }
    });
}

/**
 * 格式化数字
 */
function formatNumber(num) {
    return new Intl.NumberFormat('zh-CN').format(num);
}

/**
 * 格式化货币
 */
function formatCurrency(amount) {
    return new Intl.NumberFormat('zh-CN', {
        style: 'currency',
        currency: 'CNY'
    }).format(amount);
}

/**
 * 防抖函数
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * 图表初始化和管理
 */
const ChartManager = {
    charts: new Map(),

    /**
     * 创建饼图
     */
    createPieChart(canvasId, data, options = {}) {
        const ctx = document.getElementById(canvasId);
        if (!ctx) return null;

        const defaultOptions = {
            responsive: true,
            maintainAspectRatio: false,
            devicePixelRatio: window.devicePixelRatio || 1,
            interaction: {
                intersect: false,
            },
            layout: {
                padding: {
                    top: 10,
                    bottom: 10,
                    left: 10,
                    right: 10
                }
            },
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 15,
                        usePointStyle: true,
                        font: {
                            size: Math.max(10, Math.min(14, window.innerWidth / 100))
                        },
                        boxWidth: 12,
                        boxHeight: 12
                    }
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: 'white',
                    bodyColor: 'white',
                    borderColor: 'rgba(255, 255, 255, 0.1)',
                    borderWidth: 1,
                    cornerRadius: 6,
                    displayColors: true,
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = context.parsed;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((value / total) * 100).toFixed(1);
                            return `${label}: ${value} (${percentage}%)`;
                        }
                    }
                }
            },
            animation: {
                duration: 1000,
                easing: 'easeInOutQuart'
            }
        };

        const chart = new Chart(ctx, {
            type: 'pie',
            data: data,
            options: { ...defaultOptions, ...options }
        });

        this.charts.set(canvasId, chart);
        return chart;
    },

    /**
     * 创建柱状图
     */
    createBarChart(canvasId, data, options = {}) {
        const ctx = document.getElementById(canvasId);
        if (!ctx) return null;

        const defaultOptions = {
            responsive: true,
            maintainAspectRatio: false,
            devicePixelRatio: window.devicePixelRatio || 1,
            interaction: {
                intersect: false,
                mode: 'index'
            },
            layout: {
                padding: {
                    top: 10,
                    bottom: 10,
                    left: 10,
                    right: 10
                }
            },
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: 'white',
                    bodyColor: 'white',
                    borderColor: 'rgba(255, 255, 255, 0.1)',
                    borderWidth: 1,
                    cornerRadius: 6
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)',
                        lineWidth: 1
                    },
                    ticks: {
                        font: {
                            size: Math.max(10, Math.min(12, window.innerWidth / 120))
                        }
                    }
                },
                x: {
                    grid: {
                        display: false
                    },
                    ticks: {
                        font: {
                            size: Math.max(10, Math.min(12, window.innerWidth / 120))
                        }
                    }
                }
            },
            animation: {
                duration: 1000,
                easing: 'easeInOutQuart'
            }
        };

        const chart = new Chart(ctx, {
            type: 'bar',
            data: data,
            options: { ...defaultOptions, ...options }
        });

        this.charts.set(canvasId, chart);
        return chart;
    },

    /**
     * 创建线图
     */
    createLineChart(canvasId, data, options = {}) {
        const ctx = document.getElementById(canvasId);
        if (!ctx) return null;

        const defaultOptions = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top'
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            },
            elements: {
                line: {
                    tension: 0.4
                },
                point: {
                    radius: 4,
                    hoverRadius: 6
                }
            }
        };

        const chart = new Chart(ctx, {
            type: 'line',
            data: data,
            options: { ...defaultOptions, ...options }
        });

        this.charts.set(canvasId, chart);
        return chart;
    },

    /**
     * 更新图表数据
     */
    updateChart(canvasId, newData) {
        const chart = this.charts.get(canvasId);
        if (chart) {
            chart.data = newData;
            chart.update();
        }
    },

    /**
     * 销毁图表
     */
    destroyChart(canvasId) {
        const chart = this.charts.get(canvasId);
        if (chart) {
            chart.destroy();
            this.charts.delete(canvasId);
        }
    },

    /**
     * 销毁所有图表
     */
    destroyAllCharts() {
        this.charts.forEach(chart => chart.destroy());
        this.charts.clear();
    },

    /**
     * 重新调整所有图表大小
     */
    resizeAllCharts() {
        this.charts.forEach(chart => {
            if (chart && typeof chart.resize === 'function') {
                chart.resize();
            }
        });
    }
};

/**
 * 初始化统计卡片动画
 */
function initializeStatsCards() {
    // 统计卡片动画已移除，保留函数结构以避免调用错误
}

/**
 * 数字动画
 */
function animateNumber(element, start, end, duration) {
    const startTime = performance.now();
    const difference = end - start;

    function updateNumber(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);

        // 使用缓动函数
        const easeOutQuart = 1 - Math.pow(1 - progress, 4);
        const current = Math.floor(start + difference * easeOutQuart);

        element.textContent = formatNumber(current);

        if (progress < 1) {
            requestAnimationFrame(updateNumber);
        } else {
            element.textContent = formatNumber(end);
        }
    }

    requestAnimationFrame(updateNumber);
}

/**
 * 初始化编辑模态框功能
 */
function initializeEditModal() {
    // 绑定编辑按钮点击事件
    document.addEventListener('click', function(e) {
        if (e.target.closest('.edit-btn')) {
            e.preventDefault();
            const button = e.target.closest('.edit-btn');
            const editUrl = button.dataset.editUrl;
            const itemName = button.dataset.itemName;
            const itemType = button.dataset.itemType;

            openEditModal(editUrl, itemName, itemType);
        }
    });

    // 绑定保存按钮点击事件
    const saveBtn = document.getElementById('saveEditBtn');
    if (saveBtn) {
        saveBtn.addEventListener('click', function() {
            submitEditForm();
        });
    }

    // 绑定模态框事件
    const editModal = document.getElementById('editModal');
    if (editModal) {
        editModal.addEventListener('hidden.bs.modal', function() {
            clearEditModal();
        });

        editModal.addEventListener('shown.bs.modal', function() {
            // 聚焦到第一个输入框
            const firstInput = editModal.querySelector('input:not([type="hidden"]), textarea, select');
            if (firstInput) {
                firstInput.focus();
            }
        });
    }
}

/**
 * 打开编辑模态框
 */
function openEditModal(editUrl, itemName, itemType) {
    const modal = document.getElementById('editModal');
    const modalTitle = document.getElementById('editModalLabel');
    const modalContent = document.getElementById('editModalContent');
    const saveBtn = document.getElementById('saveEditBtn');

    if (!modal || !modalTitle || !modalContent || !saveBtn) {
        showAnimatedMessage('模态框初始化失败', 'danger');
        return;
    }

    // 设置标题
    if (itemName === '新记录' || itemName.includes('新')) {
        // 添加操作
        modalTitle.innerHTML = `<i class="bi bi-plus-circle"></i> ${itemType}`;
    } else {
        // 编辑操作
        modalTitle.innerHTML = `<i class="bi bi-pencil"></i> 编辑${itemType}：${itemName}`;
    }

    // 显示加载状态
    modalContent.innerHTML = `
        <div class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2 text-muted">正在加载表单...</p>
        </div>
    `;

    // 禁用保存按钮
    saveBtn.disabled = true;

    // 显示模态框
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();

    // 加载表单内容
    loadEditForm(editUrl, modalContent, saveBtn);
}

/**
 * 加载编辑表单
 */
function loadEditForm(editUrl, container, saveBtn) {
    makeAjaxRequest(editUrl, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(data => {
        if (data.success && data.form_html) {
            container.innerHTML = data.form_html;
            saveBtn.disabled = false;

            // 根据URL判断是否为添加操作，设置按钮文本
            if (editUrl.includes('/create')) {
                saveBtn.innerHTML = '<i class="bi bi-check-circle"></i> 提交记录';
            } else {
                saveBtn.innerHTML = '<i class="bi bi-check-circle"></i> 保存更改';
            }

            // 初始化表单增强功能
            initializeFormEnhancementsForModal(container);
        } else {
            throw new Error(data.error || '加载表单失败');
        }
    })
    .catch(error => {
        container.innerHTML = `
            <div class="alert alert-danger">
                <i class="bi bi-exclamation-triangle"></i>
                <strong>加载失败：</strong>${error.message}
            </div>
        `;
        saveBtn.disabled = true;
    });
}

/**
 * 为模态框中的表单初始化增强功能（移除聚焦动画）
 */
function initializeFormEnhancementsForModal(container) {
    // 为表单元素添加增强功能，移除聚焦动画
    const inputs = container.querySelectorAll('.form-control, .form-select');
    inputs.forEach(input => {
        // 移除聚焦动画效果，只保留验证功能
        // 不再添加focused类，避免CSS动画

        // 实时验证
        input.addEventListener('input', function() {
            validateInput(this);
        });
    });

    // 复选框增强
    const checkInputs = container.querySelectorAll('.form-check-input');
    checkInputs.forEach(input => {
        input.addEventListener('change', function() {
            const label = this.nextElementSibling;
            if (this.checked) {
                label.style.color = 'var(--primary-color)';
                label.style.fontWeight = '600';
            } else {
                label.style.color = '';
                label.style.fontWeight = '';
            }
        });
    });
}

/**
 * 提交编辑表单
 */
function submitEditForm() {
    const modal = document.getElementById('editModal');
    const form = modal.querySelector('form');
    const saveBtn = document.getElementById('saveEditBtn');

    if (!form) {
        showAnimatedMessage('未找到表单', 'danger');
        return;
    }

    const formData = new FormData(form);
    const submitUrl = form.action || form.dataset.submitUrl;

    if (!submitUrl) {
        showAnimatedMessage('未找到提交地址', 'danger');
        return;
    }

    // 显示提交状态
    const originalText = saveBtn.innerHTML;
    saveBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>保存中...';
    saveBtn.disabled = true;

    makeAjaxRequest(submitUrl, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(data => {
        if (data.success) {
            // 清除可能的重复消息，然后显示新消息
            clearExistingMessages();
            showAnimatedMessage(data.message || '保存成功', 'success');

            // 关闭模态框
            const bsModal = bootstrap.Modal.getInstance(modal);
            if (bsModal) {
                bsModal.hide();

                // 确保backdrop被正确移除
                setTimeout(() => {
                    const backdrop = document.querySelector('.modal-backdrop');
                    if (backdrop) {
                        backdrop.remove();
                    }
                    // 确保body上的modal-open类被移除
                    document.body.classList.remove('modal-open');
                    document.body.style.overflow = '';
                    document.body.style.paddingRight = '';
                }, 300);
            }

            // 检查是否在SPA模式或商品页面，如果是则刷新表格而不是整个页面
            const currentPath = window.location.pathname;
            console.log('🔄 编辑成功，当前路径:', currentPath);

            if (currentPath.includes('/products/')) {
                console.log('📦 在商品页面，尝试刷新表格');
                if (window.WarehouseJS && window.WarehouseJS.refreshProductTable) {
                    // 刷新商品表格
                    setTimeout(() => {
                        try {
                            console.log('🔄 开始刷新商品表格');
                            window.WarehouseJS.refreshProductTable();
                        } catch (error) {
                            console.error('❌ 刷新商品表格失败:', error);
                            // 如果刷新失败，回退到页面刷新
                            window.location.reload();
                        }
                    }, 500);
                } else {
                    console.warn('⚠️ refreshProductTable函数不可用，使用页面刷新');
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                }
            } else if (currentPath.includes('/transactions/')) {
                console.log('📊 在进出库记录页面，刷新图表和页面');
                // 进出库记录页面，先刷新图表，再刷新页面以更新记录列表和统计数据
                if (window.refreshTransactionCharts) {
                    try {
                        window.refreshTransactionCharts();
                    } catch (error) {
                        console.error('❌ 刷新进出库图表失败:', error);
                    }
                }
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else if (data.redirect_url) {
                // 非商品页面，正常跳转
                console.log('🔗 跳转到:', data.redirect_url);
                setTimeout(() => {
                    window.location.href = data.redirect_url;
                }, 1000);
            } else {
                // 其他情况，刷新当前页面
                console.log('🔄 刷新当前页面');
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            }
        } else {
            // 显示表单错误
            if (data.form_html) {
                const modalContent = document.getElementById('editModalContent');
                modalContent.innerHTML = data.form_html;
                initializeFormEnhancementsForModal(modalContent);
            }

            if (data.errors) {
                showFormErrors(form, data.errors);
            }

            showAnimatedMessage(data.error || '保存失败，请检查表单', 'danger');
        }
    })
    .catch(error => {
        showAnimatedMessage('提交失败，请稍后重试', 'danger');
    })
    .finally(() => {
        // 恢复按钮状态
        saveBtn.innerHTML = originalText;
        saveBtn.disabled = false;
    });
}

/**
 * 清空编辑模态框
 */
function clearEditModal() {
    const modalContent = document.getElementById('editModalContent');
    const modalTitle = document.getElementById('editModalLabel');
    const saveBtn = document.getElementById('saveEditBtn');

    if (modalContent) {
        modalContent.innerHTML = `
            <div class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <p class="mt-2 text-muted">正在加载表单...</p>
            </div>
        `;
    }

    if (modalTitle) {
        modalTitle.innerHTML = '<i class="bi bi-pencil"></i> 编辑项目';
    }

    if (saveBtn) {
        saveBtn.disabled = true;
        saveBtn.innerHTML = '<i class="bi bi-check-circle"></i> 保存更改';
    }
}

// 导出全局函数
window.WarehouseJS = {
    showSuccessMessage,
    showErrorMessage,
    showAnimatedMessage,
    makeAjaxRequest,
    formatNumber,
    formatCurrency,
    debounce,
    ChartManager,
    initializeStatsCards,
    animateNumber,
    addRippleEffect,
    addValidationAnimation,
    showPageLoader,
    hidePageLoader,
    showNetworkIndicator,
    initializeRealTimeSearch,
    loadTableData,
    submitFormAsync,
    showFormErrors,
    toggleSidebar,
    showSidebar,
    hideSidebar,
    showSidebarMobile,
    hideSidebarMobile,
    openEditModal,
    submitEditForm,
    clearEditModal,
    initializeEditModal,
    initializeInventoryEdit,
    initializeSPANavigation,
    loadPageContent,
    refreshInventoryCharts,
    // 库存页面相关函数
    toggleSelectAllInventory,
    updateBatchDeleteButtonInventory,
    getSelectedInventoryItems,
    editSelectedInventory,
    editInventory,
    confirmBatchDeleteInventory,
    batchDeleteInventory,
    reapplyTableStyles,
    rebindInventoryEvents,
    bindInventoryFilters,
    handleInventoryFilterChange,
    updateInventoryUrlParams
};

/**
 * 初始化SPA导航功能
 */
function initializeSPANavigation() {
    console.log('🚀 初始化SPA导航功能');

    // 拦截导航链接点击
    const navLinks = document.querySelectorAll('.sidebar-nav a, .nav-link');
    navLinks.forEach(link => {
        // 只处理内部导航链接
        const href = link.getAttribute('href');
        if (href && (href.startsWith('/products/') || href.startsWith('/inventory/') || href.startsWith('/transactions/'))) {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                loadPageContent(href);
            });
        }
    });

    // 监听浏览器前进后退按钮
    window.addEventListener('popstate', function(e) {
        if (e.state && e.state.url) {
            loadPageContent(e.state.url, false);
        }
    });
}

/**
 * 加载页面内容
 */
function loadPageContent(url, pushState = true) {
    console.log(`📄 加载页面内容: ${url}`);

    // 显示加载状态
    showPageLoadingState();

    // 更新导航状态
    updateNavigationState(url);

    // 发送AJAX请求
    fetch(`/api/spa/load-content/?url=${encodeURIComponent(url)}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 更新页面内容
                const mainContent = document.getElementById('main-content');
                if (mainContent) {
                    // 保留侧边栏切换按钮
                    const sidebarToggle = mainContent.querySelector('.sidebar-toggle');
                    const container = mainContent.querySelector('.container');

                    if (container) {
                        // 清除现有的消息提示，避免累积
                        clearExistingMessages();

                        container.innerHTML = data.content;

                        // 执行新加载内容中的脚本
                        executeScriptsInContainer(container);
                    }
                }

                // 更新页面标题
                document.title = data.title;

                // 更新浏览器历史
                if (pushState) {
                    history.pushState({ url: url }, data.title, url);
                }

                // 重新初始化页面功能
                reinitializePageFeatures();

                // 强制初始化图表（作为备用方案）
                setTimeout(() => {
                    if (window.WarehouseJS && window.WarehouseJS.forceInitializeCurrentPageCharts) {
                        window.WarehouseJS.forceInitializeCurrentPageCharts();
                    }
                }, 500);

                console.log(`✅ 页面加载完成: ${data.title}`);
            } else {
                console.error('❌ 页面加载失败:', data.error);
                showPageLoadError(data.error);
            }
        })
        .catch(error => {
            console.error('❌ 页面加载请求失败:', error);
            showPageLoadError('页面加载失败，请稍后重试');
        })
        .finally(() => {
            hidePageLoadingState();
        });
}

/**
 * 显示页面加载状态
 */
function showPageLoadingState() {
    const mainContent = document.getElementById('main-content');
    const container = mainContent?.querySelector('.container');

    if (container) {
        container.innerHTML = `
            <div class="d-flex justify-content-center align-items-center" style="min-height: 400px;">
                <div class="text-center">
                    <div class="spinner-border text-primary mb-3" role="status" style="width: 3rem; height: 3rem;">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="text-muted">正在加载页面...</p>
                </div>
            </div>
        `;
    }
}

/**
 * 隐藏页面加载状态
 */
function hidePageLoadingState() {
    // 加载状态会被新内容替换，所以这里不需要特别处理
}

/**
 * 显示页面加载错误
 */
function showPageLoadError(message) {
    const mainContent = document.getElementById('main-content');
    const container = mainContent?.querySelector('.container');

    if (container) {
        container.innerHTML = `
            <div class="d-flex justify-content-center align-items-center" style="min-height: 400px;">
                <div class="text-center">
                    <i class="bi bi-exclamation-triangle text-danger" style="font-size: 4rem;"></i>
                    <h4 class="text-danger mt-3">页面加载失败</h4>
                    <p class="text-muted">${message}</p>
                    <button class="btn btn-primary" onclick="location.reload()">
                        <i class="bi bi-arrow-clockwise"></i> 重新加载
                    </button>
                </div>
            </div>
        `;
    }
}

/**
 * 更新导航状态
 */
function updateNavigationState(url) {
    // 移除所有导航项的活动状态
    const navItems = document.querySelectorAll('.sidebar-nav .nav-link');
    navItems.forEach(item => {
        item.classList.remove('active');
    });

    // 添加当前页面的活动状态
    const currentNavItem = document.querySelector(`.sidebar-nav a[href="${url}"]`);
    if (currentNavItem) {
        currentNavItem.classList.add('active');
    }
}

/**
 * 执行容器中的脚本
 */
function executeScriptsInContainer(container) {
    const scripts = container.querySelectorAll('script');
    console.log(`🔍 发现 ${scripts.length} 个脚本需要执行`);

    scripts.forEach((script, index) => {
        try {
            if (script.src) {
                // 外部脚本，动态加载
                console.log(`📥 加载外部脚本: ${script.src}`);
                const newScript = document.createElement('script');
                newScript.src = script.src;
                document.head.appendChild(newScript);
            } else {
                // 内联脚本，直接执行
                console.log(`⚡ 执行内联脚本 ${index + 1}`);
                const scriptContent = script.textContent || script.innerHTML;
                if (scriptContent.trim()) {
                    // 使用Function构造器而不是eval，更安全
                    const func = new Function(scriptContent);
                    func();
                    console.log(`✅ 脚本 ${index + 1} 执行成功`);
                }
            }
        } catch (error) {
            console.error(`❌ 脚本 ${index + 1} 执行失败:`, error);
        }
    });
}

/**
 * 重新初始化页面功能
 */
function reinitializePageFeatures() {
    console.log('🔄 开始重新初始化页面功能');

    // 重新初始化搜索功能
    initializeRealTimeSearch();

    // 重新初始化编辑模态框
    initializeEditModal();

    // 重新初始化商品管理功能（如果在商品页面）
    if (window.location.pathname.includes('/products/')) {
        console.log('🔄 重新初始化商品管理功能');
        // initializeProductManagement(); // 商品状态功能已移除

        // 重新初始化批量删除功能
        if (window.updateBatchDeleteButton) {
            window.updateBatchDeleteButton();
        }

        // 强制修复表格布局
        if (window.forceFixTableLayout) {
            setTimeout(() => {
                window.forceFixTableLayout();
            }, 100);
        }
    }

    // 重新初始化库存编辑功能（如果在库存页面）
    if (window.location.pathname.includes('/inventory/')) {
        console.log('🔄 重新初始化库存功能');
        initializeInventoryEdit();
        // 延迟初始化库存页面的图表，确保DOM完全加载
        setTimeout(() => {
            console.log('🔄 SPA重新初始化：准备加载库存图表');
            initializeInventoryCharts();
        }, 300);
    }

    // 重新初始化交易页面功能（如果在交易页面）
    if (window.location.pathname.includes('/transactions/')) {
        console.log('🔄 重新初始化交易功能');
        setTimeout(() => {
            console.log('🔄 SPA重新初始化：准备加载交易图表');
            initializeTransactionCharts();
        }, 300);
    }

    // 重新初始化统计卡片（所有页面通用）
    if (window.WarehouseJS && window.WarehouseJS.initializeStatsCards) {
        window.WarehouseJS.initializeStatsCards();
    }

    // 重新初始化表格功能
    initializeTableEnhancements();

    // 通用图表重新初始化（作为备用方案）
    setTimeout(() => {
        // 检查是否有图表容器但没有图表实例
        const chartContainers = document.querySelectorAll('canvas[id*="Chart"]');
        chartContainers.forEach(canvas => {
            const canvasId = canvas.id;
            if (!WarehouseJS.ChartManager.charts.has(canvasId)) {
                console.log(`🔄 发现未初始化的图表容器: ${canvasId}`);
                // 根据ID判断图表类型并初始化
                if (canvasId.includes('inventory') || canvasId.includes('stock')) {
                    initializeInventoryCharts();
                } else if (canvasId.includes('transaction')) {
                    initializeTransactionCharts();
                }
            }
        });
    }, 500);

    // 重新绑定分页事件
    bindPaginationEvents();

    console.log('🔄 页面功能重新初始化完成');
}

/**
 * 初始化用户菜单优化
 * 解决用户菜单先向下展示再向上展示的问题
 */
function initializeUserMenuOptimization() {
    const userDropdown = document.getElementById('sidebarUserDropdown');
    const userMenu = document.querySelector('.sidebar-footer .user-menu');

    if (!userDropdown || !userMenu) {
        return;
    }

    // 禁用Bootstrap默认的dropdown行为，使用自定义实现
    userDropdown.removeAttribute('data-bs-toggle');

    // 添加自定义点击事件
    userDropdown.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();

        const isOpen = userMenu.classList.contains('show');

        if (isOpen) {
            closeUserMenu();
        } else {
            openUserMenu();
        }
    });

    // 点击外部关闭菜单
    document.addEventListener('click', function(e) {
        if (!userDropdown.contains(e.target) && !userMenu.contains(e.target)) {
            closeUserMenu();
        }
    });

    // ESC键关闭菜单
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && userMenu.classList.contains('show')) {
            closeUserMenu();
        }
    });

    // 优化菜单位置计算
    function calculateMenuPosition() {
        const sidebarFooter = document.querySelector('.sidebar-footer');
        const sidebar = document.getElementById('sidebar');

        if (!sidebarFooter || !sidebar) return;

        const footerRect = sidebarFooter.getBoundingClientRect();
        const sidebarRect = sidebar.getBoundingClientRect();
        const menuHeight = 300; // 预估菜单高度
        const viewportHeight = window.innerHeight;

        // 检查是否有足够空间向上展开
        const spaceAbove = footerRect.top - sidebarRect.top;
        const spaceBelow = viewportHeight - footerRect.bottom;

        if (spaceAbove >= menuHeight) {
            // 向上展开（默认）
            userMenu.style.bottom = '100%';
            userMenu.style.top = 'auto';
            userMenu.classList.remove('dropup');
        } else if (spaceBelow >= menuHeight) {
            // 向下展开
            userMenu.style.bottom = 'auto';
            userMenu.style.top = '100%';
            userMenu.classList.add('dropup');
        } else {
            // 空间不足，使用固定定位
            const maxHeight = Math.max(spaceAbove, spaceBelow) - 20;
            userMenu.style.maxHeight = maxHeight + 'px';
            userMenu.style.overflowY = 'auto';

            if (spaceAbove > spaceBelow) {
                userMenu.style.bottom = '100%';
                userMenu.style.top = 'auto';
            } else {
                userMenu.style.bottom = 'auto';
                userMenu.style.top = '100%';
            }
        }
    }

    // 打开用户菜单
    function openUserMenu() {
        // 先计算位置
        calculateMenuPosition();

        // 添加显示类
        userMenu.classList.add('show');
        userDropdown.setAttribute('aria-expanded', 'true');

        // 添加焦点管理
        const firstMenuItem = userMenu.querySelector('.dropdown-item');
        if (firstMenuItem) {
            setTimeout(() => {
                firstMenuItem.focus();
            }, 100);
        }
    }

    // 关闭用户菜单
    function closeUserMenu() {
        userMenu.classList.remove('show');
        userDropdown.setAttribute('aria-expanded', 'false');

        // 重置样式
        userMenu.style.maxHeight = '';
        userMenu.style.overflowY = '';
    }

    // 监听窗口大小变化
    let resizeTimeout;
    window.addEventListener('resize', function() {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(() => {
            if (userMenu.classList.contains('show')) {
                calculateMenuPosition();
            }
        }, 100);
    });

    // 监听侧边栏状态变化
    const sidebar = document.getElementById('sidebar');
    if (sidebar) {
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                    if (userMenu.classList.contains('show')) {
                        setTimeout(calculateMenuPosition, 100);
                    }
                }
            });
        });

        observer.observe(sidebar, {
            attributes: true,
            attributeFilter: ['class']
        });
    }
}

// 添加到全局导出
window.WarehouseJS.initializeUserMenuOptimization = initializeUserMenuOptimization;

// ==================== 权限管理功能 ====================

/**
 * 权限管理工具类
 */
class PermissionManager {
    constructor() {
        this.apiEndpoints = {
            changeRole: '/users/api/change-role/',
            togglePermission: '/users/api/toggle-permission/',
            searchPermissions: '/users/api/search-permissions/',
            getUserPermissions: '/users/api/user-permissions/',
            getDashboardStats: '/users/api/dashboard-stats/'
        };
        this.csrfToken = this.getCsrfToken();
    }

    /**
     * 获取CSRF Token
     */
    getCsrfToken() {
        const cookies = document.cookie.split(';');
        for (let cookie of cookies) {
            const [name, value] = cookie.trim().split('=');
            if (name === 'csrftoken') {
                return decodeURIComponent(value);
            }
        }
        return null;
    }

    /**
     * 发送API请求
     */
    async apiRequest(url, options = {}) {
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': this.csrfToken
            }
        };

        const mergedOptions = { ...defaultOptions, ...options };

        try {
            const response = await fetch(url, mergedOptions);
            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.error || `HTTP ${response.status}`);
            }

            return data;
        } catch (error) {
            console.error('API请求失败:', error);
            throw error;
        }
    }

    /**
     * 修改用户角色
     */
    async changeUserRole(userId, newRole) {
        return await this.apiRequest(this.apiEndpoints.changeRole, {
            method: 'POST',
            body: JSON.stringify({
                user_id: userId,
                role: newRole
            })
        });
    }

    /**
     * 切换角色权限
     */
    async toggleRolePermission(role, permissionId) {
        return await this.apiRequest(this.apiEndpoints.togglePermission, {
            method: 'POST',
            body: JSON.stringify({
                role: role,
                permission_id: permissionId
            })
        });
    }

    /**
     * 搜索权限
     */
    async searchPermissions(query, category = '') {
        const params = new URLSearchParams({ q: query });
        if (category) params.append('category', category);

        return await this.apiRequest(`${this.apiEndpoints.searchPermissions}?${params}`);
    }

    /**
     * 获取用户权限详情
     */
    async getUserPermissions(userId) {
        return await this.apiRequest(`${this.apiEndpoints.getUserPermissions}?user_id=${userId}`);
    }

    /**
     * 获取仪表板统计数据
     */
    async getDashboardStats() {
        return await this.apiRequest(this.apiEndpoints.getDashboardStats);
    }
}

/**
 * 消息通知工具类
 */
class NotificationManager {
    constructor() {
        this.container = this.createContainer();
    }

    createContainer() {
        let container = document.getElementById('notification-container');
        if (!container) {
            container = document.createElement('div');
            container.id = 'notification-container';
            container.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 9999;
                max-width: 400px;
            `;
            document.body.appendChild(container);
        }
        return container;
    }

    show(message, type = 'info', duration = 5000) {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show`;
        notification.style.cssText = `
            margin-bottom: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            border: none;
            border-radius: 8px;
        `;

        const iconMap = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        };

        notification.innerHTML = `
            <i class="${iconMap[type] || iconMap.info}"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        this.container.appendChild(notification);

        // 自动消失
        if (duration > 0) {
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, duration);
        }

        return notification;
    }

    success(message, duration) {
        return this.show(message, 'success', duration);
    }

    error(message, duration) {
        return this.show(message, 'danger', duration);
    }

    warning(message, duration) {
        return this.show(message, 'warning', duration);
    }

    info(message, duration) {
        return this.show(message, 'info', duration);
    }
}

// 全局实例
window.permissionManager = new PermissionManager();
window.notificationManager = new NotificationManager();

/**
 * 权限矩阵增强功能
 */
function initializePermissionMatrix() {
    const matrixTable = document.getElementById('permissionMatrix');
    if (!matrixTable) return;

    // 添加行和列的高亮效果
    const cells = matrixTable.querySelectorAll('.permission-cell');

    cells.forEach(cell => {
        cell.addEventListener('mouseenter', function() {
            highlightMatrixRowColumn(this, true);
        });

        cell.addEventListener('mouseleave', function() {
            highlightMatrixRowColumn(this, false);
        });
    });

    console.log('权限矩阵增强功能已初始化');
}

/**
 * 高亮矩阵行和列
 */
function highlightMatrixRowColumn(cell, highlight) {
    const row = cell.closest('tr');
    const cellIndex = Array.from(row.children).indexOf(cell);
    const table = cell.closest('table');

    // 高亮行
    if (highlight) {
        row.style.backgroundColor = '#f8f9fa';
    } else {
        row.style.backgroundColor = '';
    }

    // 高亮列
    const rows = table.querySelectorAll('tr');
    rows.forEach(r => {
        const targetCell = r.children[cellIndex];
        if (targetCell) {
            if (highlight) {
                targetCell.style.backgroundColor = '#f8f9fa';
            } else {
                targetCell.style.backgroundColor = '';
            }
        }
    });
}

/**
 * 用户角色管理增强功能
 */
function initializeUserRoleManagement() {
    // 实时搜索增强
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        let searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                searchUsers();
            }, 300);
        });
    }

    // 批量选择功能
    const selectAllCheckbox = document.getElementById('selectAll');
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('.user-checkbox');
            checkboxes.forEach(cb => {
                cb.checked = this.checked;
            });
            updateSelection();
        });
    }

    console.log('用户角色管理增强功能已初始化');
}

/**
 * 权限预览增强功能
 */
function initializePermissionPreview() {
    // 分类折叠/展开动画
    const categoryToggles = document.querySelectorAll('.category-toggle');
    categoryToggles.forEach(toggle => {
        toggle.addEventListener('click', function() {
            const content = this.closest('.category-header').nextElementSibling;
            const icon = this.querySelector('i');

            // 添加动画效果
            if (content.style.display === 'none') {
                content.style.display = 'block';
                content.style.maxHeight = '0';
                content.style.overflow = 'hidden';
                content.style.transition = 'max-height 0.3s ease';

                setTimeout(() => {
                    content.style.maxHeight = content.scrollHeight + 'px';
                }, 10);

                icon.className = 'fas fa-chevron-down';
            } else {
                content.style.maxHeight = '0';
                icon.className = 'fas fa-chevron-right';

                setTimeout(() => {
                    content.style.display = 'none';
                }, 300);
            }
        });
    });

    console.log('权限预览增强功能已初始化');
}

/**
 * 初始化权限管理相关功能
 */
function initializePermissionManagement() {
    initializePermissionMatrix();
    initializeUserRoleManagement();
    initializePermissionPreview();

    console.log('权限管理功能已全部初始化');
}

// 添加到全局导出
window.WarehouseJS.initializePermissionManagement = initializePermissionManagement;

// ==================== 动态菜单控制功能 ====================

/**
 * 菜单管理器类
 */
class MenuManager {
    constructor() {
        this.menuCache = new Map();
        this.permissionCache = new Map();
        this.cacheTimeout = 5 * 60 * 1000; // 5分钟缓存
        this.init();
    }

    init() {
        this.bindEvents();
        this.updateMenuVisibility();
        console.log('菜单管理器已初始化');
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 监听权限变更事件
        document.addEventListener('permissionChanged', (e) => {
            this.clearCache();
            this.updateMenuVisibility();
        });

        // 监听用户角色变更事件
        document.addEventListener('roleChanged', (e) => {
            this.clearCache();
            this.updateMenuVisibility();
        });

        // 页面可见性变化时刷新菜单
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden) {
                this.refreshMenuIfNeeded();
            }
        });
    }

    /**
     * 更新菜单可见性
     */
    async updateMenuVisibility() {
        try {
            const permissions = await this.getUserPermissions();
            const menuItems = document.querySelectorAll('#main-navigation .nav-item');

            menuItems.forEach(item => {
                const link = item.querySelector('.nav-link');
                if (link) {
                    const href = link.getAttribute('href');
                    const isVisible = this.checkMenuPermission(href, permissions);

                    if (isVisible) {
                        item.style.display = '';
                        item.classList.remove('menu-hidden');
                    } else {
                        item.style.display = 'none';
                        item.classList.add('menu-hidden');
                    }
                }
            });

            // 更新用户下拉菜单
            this.updateUserDropdownMenu(permissions);

        } catch (error) {
            console.error('更新菜单可见性失败:', error);
        }
    }

    /**
     * 更新用户下拉菜单
     */
    updateUserDropdownMenu(permissions) {
        const dropdownItems = document.querySelectorAll('.user-menu .dropdown-item');

        dropdownItems.forEach(item => {
            const href = item.getAttribute('href');
            if (href && href.startsWith('/users/')) {
                const isVisible = this.checkMenuPermission(href, permissions);

                const listItem = item.closest('li');
                if (listItem) {
                    if (isVisible) {
                        listItem.style.display = '';
                    } else {
                        listItem.style.display = 'none';
                    }
                }
            }
        });

        // 处理分隔线
        this.updateDropdownDividers();
    }

    /**
     * 更新下拉菜单分隔线
     */
    updateDropdownDividers() {
        const dividers = document.querySelectorAll('.user-menu .dropdown-divider');

        dividers.forEach(divider => {
            const prevVisible = this.findPreviousVisibleItem(divider);
            const nextVisible = this.findNextVisibleItem(divider);

            // 如果前后都没有可见项，隐藏分隔线
            if (!prevVisible || !nextVisible) {
                divider.style.display = 'none';
            } else {
                divider.style.display = '';
            }
        });
    }

    /**
     * 查找前一个可见项
     */
    findPreviousVisibleItem(element) {
        let prev = element.previousElementSibling;
        while (prev) {
            if (prev.style.display !== 'none' && !prev.classList.contains('dropdown-divider')) {
                return prev;
            }
            prev = prev.previousElementSibling;
        }
        return null;
    }

    /**
     * 查找后一个可见项
     */
    findNextVisibleItem(element) {
        let next = element.nextElementSibling;
        while (next) {
            if (next.style.display !== 'none' && !next.classList.contains('dropdown-divider')) {
                return next;
            }
            next = next.nextElementSibling;
        }
        return null;
    }

    /**
     * 检查菜单权限
     */
    checkMenuPermission(href, permissions) {
        if (!href || !permissions) return false;

        // 基本URL映射到权限
        const urlPermissionMap = {
            '/products/': permissions.products?.view,
            '/inventory/': permissions.inventory?.view,
            '/transactions/': permissions.transactions?.view,
            '/users/management/': permissions.users?.view,
            '/users/permissions/': permissions.users?.change,
            '/users/audit-log/': permissions.users?.view_audit,
            '/admin/': permissions.admin?.access
        };

        // 检查精确匹配
        for (const [url, hasPermission] of Object.entries(urlPermissionMap)) {
            if (href.includes(url)) {
                return hasPermission || false;
            }
        }

        // 默认允许访问（如登录、登出等）
        return true;
    }

    /**
     * 获取用户权限
     */
    async getUserPermissions() {
        const cacheKey = 'user_permissions';
        const cached = this.permissionCache.get(cacheKey);

        if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
            return cached.data;
        }

        try {
            const response = await fetch('/users/api/dashboard-stats/');
            if (response.ok) {
                const data = await response.json();
                const permissions = this.parsePermissions(data);

                this.permissionCache.set(cacheKey, {
                    data: permissions,
                    timestamp: Date.now()
                });

                return permissions;
            }
        } catch (error) {
            console.error('获取用户权限失败:', error);
        }

        // 返回默认权限（只允许基本访问）
        return {
            products: { view: true },
            inventory: { view: false },
            transactions: { view: false },
            users: { view: false, change: false, view_audit: false },
            admin: { access: false }
        };
    }

    /**
     * 解析权限数据
     */
    parsePermissions(data) {
        // 这里可以根据实际API返回的数据结构进行解析
        // 暂时返回模拟数据
        return {
            products: { view: true, add: true, change: true, delete: true },
            inventory: { view: true, change: true },
            transactions: { view: true, add: true },
            users: { view: true, change: true, view_audit: true },
            admin: { access: true }
        };
    }

    /**
     * 清除缓存
     */
    clearCache() {
        this.menuCache.clear();
        this.permissionCache.clear();
    }

    /**
     * 如果需要则刷新菜单
     */
    refreshMenuIfNeeded() {
        const lastRefresh = localStorage.getItem('menu_last_refresh');
        const now = Date.now();

        if (!lastRefresh || now - parseInt(lastRefresh) > this.cacheTimeout) {
            this.clearCache();
            this.updateMenuVisibility();
            localStorage.setItem('menu_last_refresh', now.toString());
        }
    }

    /**
     * 手动刷新菜单
     */
    refresh() {
        this.clearCache();
        this.updateMenuVisibility();
        localStorage.setItem('menu_last_refresh', Date.now().toString());
    }
}

/**
 * 菜单动画效果
 */
class MenuAnimations {
    constructor() {
        this.init();
    }

    init() {
        this.addHoverEffects();
        this.addActiveStateAnimations();
        console.log('菜单动画效果已初始化');
    }

    /**
     * 添加悬停效果
     */
    addHoverEffects() {
        const menuItems = document.querySelectorAll('.nav-link, .dropdown-item');

        menuItems.forEach(item => {
            item.addEventListener('mouseenter', function() {
                this.style.transform = 'translateX(5px)';
                this.style.transition = 'transform 0.2s ease';
            });

            item.addEventListener('mouseleave', function() {
                this.style.transform = '';
            });
        });
    }

    /**
     * 添加激活状态动画
     */
    addActiveStateAnimations() {
        const activeItem = document.querySelector('.nav-link.active');
        if (activeItem) {
            activeItem.style.borderLeft = '4px solid var(--primary-color)';
            activeItem.style.background = 'linear-gradient(90deg, rgba(var(--primary-color-rgb), 0.1) 0%, transparent 100%)';
        }
    }
}

// 全局菜单管理器实例
window.menuManager = new MenuManager();
window.menuAnimations = new MenuAnimations();

/**
 * 初始化动态菜单控制
 */
function initializeDynamicMenuControl() {
    // 菜单管理器已在类实例化时初始化

    // 添加菜单刷新按钮事件
    const refreshButtons = document.querySelectorAll('[data-action="refresh-menu"]');
    refreshButtons.forEach(button => {
        button.addEventListener('click', () => {
            window.menuManager.refresh();
            window.notificationManager.success('菜单已刷新');
        });
    });

    console.log('动态菜单控制已初始化');
}

// 添加到全局导出
window.WarehouseJS.initializeDynamicMenuControl = initializeDynamicMenuControl;

// ==================== 库存编辑功能（模态框模式）====================
// 库存编辑使用通用的模态框编辑功能，无需单独的管理器类

/**
 * 初始化库存编辑功能（支持内联编辑和模态框编辑）
 */
function initializeInventoryEdit() {
    // 初始化内联编辑功能
    initializeInlineQuantityEdit();

    // 库存编辑使用通用的模态框编辑功能
    // 通过 initializeEditModal() 已经处理了编辑按钮的点击事件
    console.log('✅ 库存编辑功能已初始化（内联编辑 + 模态框模式）');
}

/**
 * 初始化内联数量编辑功能
 */
function initializeInlineQuantityEdit() {
    // 绑定编辑按钮点击事件
    document.addEventListener('click', function(e) {
        if (e.target.closest('.edit-quantity-btn')) {
            e.preventDefault();
            const button = e.target.closest('.edit-quantity-btn');
            const container = button.closest('.inventory-quantity-container');
            showQuantityEditMode(container);
        }

        // 保存按钮点击事件
        if (e.target.closest('.save-quantity-btn')) {
            e.preventDefault();
            const button = e.target.closest('.save-quantity-btn');
            const container = button.closest('.inventory-quantity-container');
            saveQuantityEdit(container);
        }

        // 取消按钮点击事件
        if (e.target.closest('.cancel-quantity-btn')) {
            e.preventDefault();
            const button = e.target.closest('.cancel-quantity-btn');
            const container = button.closest('.inventory-quantity-container');
            cancelQuantityEdit(container);
        }
    });

    // 绑定输入框回车键事件
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Enter' && e.target.classList.contains('quantity-input')) {
            e.preventDefault();
            const container = e.target.closest('.inventory-quantity-container');
            saveQuantityEdit(container);
        }

        // ESC键取消编辑
        if (e.key === 'Escape' && e.target.classList.contains('quantity-input')) {
            e.preventDefault();
            const container = e.target.closest('.inventory-quantity-container');
            cancelQuantityEdit(container);
        }
    });
}

/**
 * 显示数量编辑模式
 */
function showQuantityEditMode(container) {
    const displayDiv = container.querySelector('.quantity-display');
    const editDiv = container.querySelector('.quantity-edit');
    const input = editDiv.querySelector('.quantity-input');
    const quantityValue = container.querySelector('.quantity-value');

    // 隐藏显示模式，显示编辑模式
    displayDiv.style.display = 'none';
    editDiv.style.display = 'block';

    // 设置输入框的值为当前数量
    input.value = quantityValue.textContent.trim();

    // 聚焦并选中输入框内容
    input.focus();
    input.select();
}

/**
 * 取消数量编辑
 */
function cancelQuantityEdit(container) {
    const displayDiv = container.querySelector('.quantity-display');
    const editDiv = container.querySelector('.quantity-edit');

    // 显示显示模式，隐藏编辑模式
    displayDiv.style.display = 'flex';
    editDiv.style.display = 'none';
}

/**
 * 保存数量编辑
 */
function saveQuantityEdit(container) {
    const inventoryId = container.dataset.inventoryId;
    const input = container.querySelector('.quantity-input');
    const newQuantity = parseInt(input.value);
    const quantityValue = container.querySelector('.quantity-value');
    const originalQuantity = parseInt(quantityValue.dataset.originalQuantity);

    // 验证输入
    if (isNaN(newQuantity) || newQuantity < 0) {
        showAnimatedMessage('请输入有效的数量（不能小于0）', 'danger');
        input.focus();
        return;
    }

    // 如果数量没有变化，直接取消编辑
    if (newQuantity === originalQuantity) {
        cancelQuantityEdit(container);
        return;
    }

    // 显示保存状态
    const saveBtn = container.querySelector('.save-quantity-btn');
    const originalSaveBtnHtml = saveBtn.innerHTML;
    saveBtn.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i>';
    saveBtn.disabled = true;

    // 添加更新中的样式
    const row = container.closest('tr');
    if (row) {
        row.classList.add('updating');
    }

    // 发送AJAX请求
    makeAjaxRequest('/inventory/api/update-quantity/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({
            inventory_id: inventoryId,
            quantity: newQuantity
        })
    })
    .then(data => {
        if (data.success) {
            // 更新显示的数量
            quantityValue.textContent = newQuantity;
            quantityValue.dataset.originalQuantity = newQuantity;

            // 更新数量颜色
            quantityValue.className = quantityValue.className.replace(/text-(success|danger|warning)/, '');
            if (data.data.is_low_stock) {
                quantityValue.classList.add('text-danger');
            } else {
                quantityValue.classList.add('text-success');
            }

            // 更新状态标签（如果存在）
            const statusCell = row ? row.querySelector('td:nth-child(3)') : null;
            if (statusCell && data.data.status_html) {
                statusCell.innerHTML = data.data.status_html;
            }

            // 更新总价值（如果存在）
            const valueCell = row ? row.querySelector('td:nth-child(4)') : null;
            if (valueCell && data.data.total_value) {
                valueCell.textContent = data.data.total_value;
            }

            // 显示成功消息
            showAnimatedMessage(data.message, 'success');

            // 取消编辑模式
            cancelQuantityEdit(container);
        } else {
            throw new Error(data.error || '保存失败');
        }
    })
    .catch(error => {
        showAnimatedMessage(`保存失败：${error.message}`, 'danger');
        input.focus();
    })
    .finally(() => {
        // 恢复按钮状态
        saveBtn.innerHTML = originalSaveBtnHtml;
        saveBtn.disabled = false;

        // 移除更新中的样式
        if (row) {
            row.classList.remove('updating');
        }
    });
}


/**
 * 加载图表数据
 */
async function loadChartData() {
    try {
        const response = await fetch('/inventory/api/chart-data/');
        const result = await response.json();

        if (result.success) {
            return result.data;
        } else {
            console.error('获取图表数据失败:', result.error);
            return null;
        }
    } catch (error) {
        console.error('图表数据请求失败:', error);
        return null;
    }
}

/**
 * 初始化库存页面图表
 */
async function initializeInventoryCharts() {
    // 检查是否在库存页面且图表容器存在
    if (!document.getElementById('stockStatusChart') || !document.getElementById('stockValueChart')) {
        return;
    }

    console.log('🎨 初始化库存页面图表');

    // 清理已存在的图表实例
    if (WarehouseJS.ChartManager) {
        WarehouseJS.ChartManager.destroyChart('stockStatusChart');
        WarehouseJS.ChartManager.destroyChart('stockValueChart');
    }

    // 从API获取实时数据
    const chartData = await loadChartData();

    if (!chartData) {
        console.warn('⚠️ 无法获取图表数据，使用默认数据');
        // 使用默认数据作为备用
        chartData = {
            stock_status: {
                labels: ['正常库存', '低库存', '高库存', '缺货'],
                data: [0, 0, 0, 0]
            },
            stock_value: {
                labels: ['暂无数据'],
                data: [0]
            }
        };
    }

    // 库存状态分布饼图
    const stockStatusData = {
        labels: chartData.stock_status.labels,
        datasets: [{
            data: chartData.stock_status.data,
            backgroundColor: [
                'rgba(40, 167, 69, 0.8)',
                'rgba(255, 193, 7, 0.8)',
                'rgba(23, 162, 184, 0.8)',
                'rgba(220, 53, 69, 0.8)'
            ],
            borderColor: [
                'rgba(40, 167, 69, 1)',
                'rgba(255, 193, 7, 1)',
                'rgba(23, 162, 184, 1)',
                'rgba(220, 53, 69, 1)'
            ],
            borderWidth: 2
        }]
    };

    WarehouseJS.ChartManager.createPieChart('stockStatusChart', stockStatusData);

    // 库存价值分析柱状图
    const stockValueData = {
        labels: chartData.stock_value.labels,
        datasets: [{
            label: '库存价值 (万元)',
            data: chartData.stock_value.data,
            backgroundColor: 'rgba(44, 90, 160, 0.8)',
            borderColor: 'rgba(44, 90, 160, 1)',
            borderWidth: 2,
            borderRadius: 4
        }]
    };

    WarehouseJS.ChartManager.createBarChart('stockValueChart', stockValueData);

    // 初始化统计卡片动画
    if (window.WarehouseJS && window.WarehouseJS.initializeStatsCards) {
        window.WarehouseJS.initializeStatsCards();
    }

    console.log('✅ 库存页面图表初始化完成');
}

/**
 * 刷新图表数据
 */
async function refreshInventoryCharts() {
    console.log('🔄 刷新库存图表数据');

    const chartData = await loadChartData();
    if (!chartData) {
        console.warn('⚠️ 刷新图表数据失败');
        return;
    }

    // 更新库存状态饼图
    const stockStatusChart = WarehouseJS.ChartManager.charts.get('stockStatusChart');
    if (stockStatusChart) {
        stockStatusChart.data.datasets[0].data = chartData.stock_status.data;
        stockStatusChart.update();
    }

    // 更新库存价值柱状图
    const stockValueChart = WarehouseJS.ChartManager.charts.get('stockValueChart');
    if (stockValueChart) {
        stockValueChart.data.labels = chartData.stock_value.labels;
        stockValueChart.data.datasets[0].data = chartData.stock_value.data;
        stockValueChart.update();
    }

    console.log('✅ 库存图表数据刷新完成');
}

/**
 * 加载进出库趋势图表数据
 */
async function loadTransactionChartData(days = 7) {
    try {
        const response = await fetch(`/transactions/api/chart-data/?days=${days}`);
        const result = await response.json();

        if (result.success) {
            return result.data;
        } else {
            console.error('获取进出库趋势数据失败:', result.error);
            return null;
        }
    } catch (error) {
        console.error('进出库趋势数据请求失败:', error);
        return null;
    }
}

/**
 * 初始化交易页面图表
 */
async function initializeTransactionCharts() {
    // 检查是否在交易页面且图表容器存在
    if (!document.getElementById('transactionTrendChart')) {
        return;
    }

    console.log('🎨 初始化交易页面图表');

    // 清理已存在的图表实例
    if (WarehouseJS.ChartManager) {
        WarehouseJS.ChartManager.destroyChart('transactionTrendChart');
    }

    // 从API获取实时数据（默认7天）
    const chartData = await loadTransactionChartData(7);

    if (!chartData) {
        console.warn('⚠️ 无法获取进出库趋势数据，使用默认数据');
        // 使用默认数据作为备用
        const defaultData = {
            labels: ['暂无数据'],
            datasets: [{
                label: '入库数量',
                data: [0],
                borderColor: 'rgba(40, 167, 69, 1)',
                backgroundColor: 'rgba(40, 167, 69, 0.1)',
                fill: true,
                tension: 0.4
            }, {
                label: '出库数量',
                data: [0],
                borderColor: 'rgba(255, 99, 132, 1)',
                backgroundColor: 'rgba(255, 99, 132, 0.1)',
                fill: true,
                tension: 0.4
            }]
        };
        WarehouseJS.ChartManager.createLineChart('transactionTrendChart', defaultData, getTransactionChartOptions());
    } else {
        // 使用实际数据
        WarehouseJS.ChartManager.createLineChart('transactionTrendChart', chartData, getTransactionChartOptions());
    }

    console.log('✅ 交易页面图表初始化完成');
}

/**
 * 刷新进出库趋势图表数据
 */
async function refreshTransactionCharts() {
    console.log('🔄 刷新进出库趋势图表数据');

    const chartData = await loadTransactionChartData();
    if (!chartData) {
        console.warn('⚠️ 刷新进出库趋势图表数据失败');
        return;
    }

    // 更新进出库趋势图表
    const transactionChart = WarehouseJS.ChartManager.charts.get('transactionTrendChart');
    if (transactionChart) {
        transactionChart.data.labels = chartData.labels;
        transactionChart.data.datasets = chartData.datasets;
        transactionChart.update();
    }

    console.log('✅ 进出库趋势图表数据刷新完成');
}

/**
 * 获取进出库趋势图表配置选项
 */
function getTransactionChartOptions() {
    return {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'top',
                onClick: function(e, legendItem, legend) {
                    // 启用图例点击功能
                    const index = legendItem.datasetIndex;
                    const chart = legend.chart;
                    const meta = chart.getDatasetMeta(index);

                    // 切换数据集的可见性
                    meta.hidden = meta.hidden === null ? !chart.data.datasets[index].hidden : null;
                    chart.update();
                },
                labels: {
                    usePointStyle: true,
                    padding: 20
                }
            },
            tooltip: {
                mode: 'index',
                intersect: false,
                backgroundColor: 'rgba(0, 0, 0, 0.8)',
                titleColor: '#fff',
                bodyColor: '#fff',
                borderColor: 'rgba(255, 255, 255, 0.1)',
                borderWidth: 1,
                cornerRadius: 6,
                displayColors: true,
                callbacks: {
                    title: function(context) {
                        return `日期: ${context[0].label}`;
                    },
                    label: function(context) {
                        const label = context.dataset.label || '';
                        const value = context.parsed.y;
                        return `${label}: ${value} 件`;
                    },
                    footer: function(tooltipItems) {
                        let total = 0;
                        tooltipItems.forEach(function(tooltipItem) {
                            total += tooltipItem.parsed.y;
                        });
                        return `总计: ${total} 件`;
                    }
                }
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                grid: {
                    color: 'rgba(0, 0, 0, 0.1)'
                },
                ticks: {
                    stepSize: 1,
                    callback: function(value) {
                        return value + ' 件';
                    }
                }
            },
            x: {
                grid: {
                    display: false
                }
            }
        },
        elements: {
            line: {
                tension: 0.4
            },
            point: {
                radius: 4,
                hoverRadius: 6,
                backgroundColor: '#fff',
                borderWidth: 2
            }
        },
        interaction: {
            mode: 'nearest',
            axis: 'x',
            intersect: false
        }
    };
}

/**
 * 更新进出库趋势图表时间范围
 */
async function updateTransactionChartRange(days) {
    console.log(`🔄 更新进出库趋势图表时间范围: ${days}天`);

    // 更新按钮状态
    const buttons = document.querySelectorAll('[data-days]');
    buttons.forEach(btn => {
        if (parseInt(btn.dataset.days) === days) {
            btn.classList.remove('btn-outline-primary');
            btn.classList.add('btn-primary', 'active');
        } else {
            btn.classList.remove('btn-primary', 'active');
            btn.classList.add('btn-outline-primary');
        }
    });

    // 获取新数据并更新图表
    const chartData = await loadTransactionChartData(days);
    if (chartData) {
        const chart = WarehouseJS.ChartManager.charts.get('transactionTrendChart');
        if (chart) {
            chart.data.labels = chartData.labels;
            chart.data.datasets = chartData.datasets;
            chart.update('active');
        }
    } else {
        console.warn('⚠️ 无法获取新的图表数据');
    }
}



// initializeProductManagement函数已移除（商品状态功能已移除）

// toggleProductStatus函数已移除（商品状态功能已移除）

/**
 * 刷新商品表格
 */
function refreshProductTable() {
    console.log('🔄 刷新商品表格');

    const tableBody = document.getElementById('product-table-body');
    const paginationContainer = document.getElementById('product-pagination-container');

    if (!tableBody) {
        console.warn('⚠️ 未找到商品表格容器');
        return;
    }

    // 获取当前搜索参数
    const searchForm = document.querySelector('form[method="get"]');
    let searchParams = '';
    if (searchForm) {
        const formData = new FormData(searchForm);
        const params = new URLSearchParams();
        for (let [key, value] of formData.entries()) {
            if (value) params.append(key, value);
        }
        searchParams = params.toString();
    }

    // 显示非侵入式加载状态
    showLoadingState();

    // 发送AJAX请求
    const url = `/products/api/search/?${searchParams}`;
    fetch(url, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
        },
        credentials: 'same-origin'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 更新表格内容
            tableBody.innerHTML = data.table_html;

            // 更新分页
            if (paginationContainer && data.pagination_html) {
                paginationContainer.innerHTML = data.pagination_html;
            }

            // 重新应用权限相关的body类
            applyPermissionClasses();

            // 强制修复表格布局
            if (window.forceFixTableLayout) {
                setTimeout(() => {
                    window.forceFixTableLayout();
                }, 100);
            }

            // 重新绑定分页事件
            bindPaginationEvents();

            console.log('✅ 商品表格刷新成功');
        } else {
            // 使用非侵入式错误提示
            showToast(`商品表格刷新失败：${data.error || '未知错误'}`, 'error');
            console.error('❌ 商品表格刷新失败:', data.error);
        }
    })
    .catch(error => {
        // 使用非侵入式错误提示
        showToast('网络错误，商品表格刷新失败，请稍后重试', 'error');
        console.error('❌ 商品表格刷新请求失败:', error);
    })
    .finally(() => {
        // 隐藏加载状态
        hideLoadingState();
    });
}

/**
 * 强制初始化当前页面的图表
 */
function forceInitializeCurrentPageCharts() {
    const currentPath = window.location.pathname;
    console.log(`🔄 强制初始化当前页面图表: ${currentPath}`);

    if (currentPath.includes('/inventory/')) {
        // 检查库存图表容器
        const stockStatusChart = document.getElementById('stockStatusChart');
        const stockValueChart = document.getElementById('stockValueChart');

        if (stockStatusChart && stockValueChart) {
            console.log('📊 发现库存图表容器，开始初始化');
            setTimeout(() => {
                initializeInventoryCharts();
            }, 100);
        } else {
            console.warn('⚠️ 未找到库存图表容器');
        }
    } else if (currentPath.includes('/transactions/')) {
        // 检查交易图表容器
        const transactionChart = document.getElementById('transactionTrendChart');

        if (transactionChart) {
            console.log('📊 发现交易图表容器，开始初始化');
            setTimeout(() => {
                initializeTransactionCharts();
            }, 100);
        } else {
            console.warn('⚠️ 未找到交易图表容器');
        }
    }
}

/**
 * 强制重置布局到正常状态
 */
function forceResetLayout() {
    const mainContent = document.getElementById('main-content');
    const sidebar = document.getElementById('sidebar');
    const toggleButton = document.getElementById('sidebar-toggle');
    const overlay = document.querySelector('.sidebar-overlay');

    console.log('🔄 强制重置布局到正常状态');

    if (mainContent) {
        // 移除所有可能的状态类
        mainContent.classList.remove('shifted', 'compact-mode');
        // 移除所有内联样式
        mainContent.style.marginLeft = '';
        mainContent.style.width = '';
        mainContent.style.maxWidth = '';
        console.log('✅ 主内容区域已重置');
    }

    if (sidebar) {
        sidebar.classList.remove('collapsed', 'show');
        console.log('✅ 侧边栏已重置');
    }

    if (toggleButton) {
        toggleButton.classList.remove('shifted');
        console.log('✅ 切换按钮已重置');
    }

    if (overlay) {
        overlay.classList.remove('show');
        console.log('✅ 遮罩层已重置');
    }

    // 清除localStorage中的状态
    localStorage.removeItem('warehouse.sidebarIsCollapsed');
    console.log('✅ 布局状态已清除');
}

/**
 * 调试布局问题
 */
function debugLayout() {
    const mainContent = document.getElementById('main-content');
    const sidebar = document.getElementById('sidebar');
    const appContainer = document.querySelector('.app-container');

    console.log('🔍 布局调试信息:');
    console.log('App Container:', appContainer ? appContainer.getBoundingClientRect() : 'Not found');
    console.log('Sidebar:', sidebar ? sidebar.getBoundingClientRect() : 'Not found');
    console.log('Main Content:', mainContent ? mainContent.getBoundingClientRect() : 'Not found');

    if (mainContent) {
        console.log('Main Content Classes:', mainContent.className);
        console.log('Main Content Computed Style:', {
            marginLeft: getComputedStyle(mainContent).marginLeft,
            width: getComputedStyle(mainContent).width,
            maxWidth: getComputedStyle(mainContent).maxWidth,
            flex: getComputedStyle(mainContent).flex
        });
    }

    if (sidebar) {
        console.log('Sidebar Classes:', sidebar.className);
        console.log('Sidebar Computed Style:', {
            width: getComputedStyle(sidebar).width,
            position: getComputedStyle(sidebar).position
        });
    }
}

// 获取CSRF Token
function getCsrfToken() {
    const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]');
    if (csrfToken) {
        return csrfToken.value;
    }

    // 从cookie中获取
    const cookies = document.cookie.split(';');
    for (let cookie of cookies) {
        const [name, value] = cookie.trim().split('=');
        if (name === 'csrftoken') {
            return value;
        }
    }

    // 从meta标签获取
    const metaCsrf = document.querySelector('meta[name="csrf-token"]');
    if (metaCsrf) {
        return metaCsrf.getAttribute('content');
    }

    console.warn('⚠️ 无法获取CSRF Token');
    return '';
}

// 显示Toast消息
function showToast(message, type = 'info') {
    console.log(`📢 Toast消息 [${type}]:`, message);

    // 创建toast容器（如果不存在）
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
        toastContainer.style.zIndex = '9999';
        document.body.appendChild(toastContainer);
    }

    // 创建toast元素
    const toastId = 'toast-' + Date.now();
    const toastHtml = `
        <div id="${toastId}" class="toast align-items-center text-white bg-${type === 'error' ? 'danger' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : 'primary'} border-0" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="d-flex">
                <div class="toast-body">
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        </div>
    `;

    toastContainer.insertAdjacentHTML('beforeend', toastHtml);

    // 初始化并显示toast
    const toastElement = document.getElementById(toastId);
    const toast = new bootstrap.Toast(toastElement, {
        autohide: type !== 'info', // info类型的toast不自动隐藏（用于加载状态）
        delay: 3000
    });

    toast.show();

    // 返回toast实例以便外部控制
    return toast;
}

// 删除确认函数
function confirmDelete(deleteUrl, itemName) {
    console.log('🗑️ 准备删除:', itemName, '删除URL:', deleteUrl);

    // 使用Bootstrap的确认对话框
    if (confirm(`确定要删除商品"${itemName}"吗？\n\n此操作不可恢复！`)) {
        console.log('✅ 用户确认删除');

        // 显示加载状态
        const loadingToast = showToast('正在删除商品...', 'info');

        // 发送删除请求
        fetch(deleteUrl, {
            method: 'POST',
            headers: {
                'X-CSRFToken': getCsrfToken(),
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json',
            },
            credentials: 'same-origin'
        })
        .then(response => {
            console.log('🔄 删除请求响应状态:', response.status);

            if (response.ok) {
                console.log('✅ 删除成功');
                showToast(`商品"${itemName}"删除成功！`, 'success');

                // 刷新商品列表
                const currentPath = window.location.pathname;
                if (currentPath.includes('/products/') && window.WarehouseJS && window.WarehouseJS.refreshProductTable) {
                    // 在商品页面，刷新表格
                    setTimeout(() => {
                        try {
                            window.WarehouseJS.refreshProductTable();
                        } catch (error) {
                            console.error('❌ 刷新商品表格失败:', error);
                            window.location.reload();
                        }
                    }, 1000);
                } else {
                    // 其他情况，刷新页面
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                }

                // 刷新库存图表（如果在库存页面）
                if (currentPath.includes('/inventory/') && window.WarehouseJS && window.WarehouseJS.refreshInventoryCharts) {
                    setTimeout(() => {
                        try {
                            window.WarehouseJS.refreshInventoryCharts();
                            console.log('🔄 已触发库存图表刷新');
                        } catch (error) {
                            console.error('❌ 刷新库存图表失败:', error);
                        }
                    }, 1500);
                }
            } else {
                console.error('❌ 删除失败，状态码:', response.status);
                showToast('删除失败，请重试！', 'error');
            }
        })
        .catch(error => {
            console.error('❌ 删除请求失败:', error);
            showToast('删除失败，网络错误！', 'error');
        })
        .finally(() => {
            // 隐藏加载状态
            if (loadingToast) {
                loadingToast.hide();
            }
        });
    } else {
        console.log('❌ 用户取消删除');
    }
}

// 批量删除相关函数
function getSelectedItems() {
    const checkboxes = document.querySelectorAll('.product-checkbox:checked');
    return Array.from(checkboxes).map(checkbox => ({
        id: checkbox.value,
        name: checkbox.dataset.name,
        deleteUrl: checkbox.dataset.deleteUrl
    }));
}

function updateBatchDeleteButton() {
    const selectedItems = getSelectedItems();
    const batchDeleteBtn = document.getElementById('batch-delete-btn');
    const batchEditBtn = document.getElementById('batch-edit-btn');
    const selectedCountSpan = document.getElementById('selected-count');
    const selectionInfo = document.getElementById('selection-info');
    const selectedCountInfo = document.getElementById('selected-count-info');

    if (selectedItems.length > 0) {
        // 显示删除按钮
        if (batchDeleteBtn) {
            batchDeleteBtn.style.display = 'inline-block';
            selectedCountSpan.textContent = selectedItems.length;
        }

        // 只有选中一个商品时才显示编辑按钮
        if (batchEditBtn) {
            if (selectedItems.length === 1) {
                batchEditBtn.style.display = 'inline-block';
            } else {
                batchEditBtn.style.display = 'none';
            }
        }

        // 显示选中状态提示
        if (selectionInfo && selectedCountInfo) {
            selectionInfo.style.display = 'inline';
            selectedCountInfo.textContent = selectedItems.length;
        }
    } else {
        // 隐藏所有批量操作按钮和提示
        if (batchDeleteBtn) {
            batchDeleteBtn.style.display = 'none';
            selectedCountSpan.textContent = '0';
        }
        if (batchEditBtn) {
            batchEditBtn.style.display = 'none';
        }
        if (selectionInfo) {
            selectionInfo.style.display = 'none';
        }
    }

    // 更新全选复选框状态
    const selectAllCheckbox = document.getElementById('select-all');
    const allCheckboxes = document.querySelectorAll('.product-checkbox');

    if (selectAllCheckbox && allCheckboxes.length > 0) {
        if (selectedItems.length === allCheckboxes.length) {
            selectAllCheckbox.checked = true;
            selectAllCheckbox.indeterminate = false;
        } else if (selectedItems.length > 0) {
            selectAllCheckbox.checked = false;
            selectAllCheckbox.indeterminate = true;
        } else {
            selectAllCheckbox.checked = false;
            selectAllCheckbox.indeterminate = false;
        }
    }
}

function toggleSelectAll() {
    const selectAllCheckbox = document.getElementById('select-all');
    const productCheckboxes = document.querySelectorAll('.product-checkbox');

    productCheckboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
    });

    updateBatchDeleteButton();
}

/**
 * 库存页面：切换全选状态
 */
function toggleSelectAllInventory() {
    const selectAllCheckbox = document.getElementById('select-all');
    const inventoryCheckboxes = document.querySelectorAll('.inventory-checkbox');

    inventoryCheckboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
    });

    updateBatchDeleteButtonInventory();
}

/**
 * 编辑选中的商品（单个）
 */
function editSelectedProduct() {
    const selectedItems = getSelectedItems();
    if (selectedItems.length === 1) {
        const item = selectedItems[0];
        const productId = item.id;
        const productName = item.name;
        editProduct(productId, productName);
    }
}

/**
 * 双击编辑商品
 */
function editProduct(productId, productName) {
    const editUrl = `/products/${productId}/update/`;
    openEditModal(editUrl, productName, '商品编辑');
}

/**
 * 库存页面：获取选中的库存项目
 */
function getSelectedInventoryItems() {
    const checkboxes = document.querySelectorAll('.inventory-checkbox:checked');
    return Array.from(checkboxes).map(checkbox => ({
        id: checkbox.value,
        name: checkbox.dataset.name,
        deleteUrl: checkbox.dataset.deleteUrl
    }));
}

/**
 * 库存页面：更新批量操作按钮状态
 */
function updateBatchDeleteButtonInventory() {
    const selectedItems = getSelectedInventoryItems();
    const batchDeleteBtn = document.getElementById('batch-delete-btn');
    const batchEditBtn = document.getElementById('batch-edit-btn');
    const selectedCountSpan = document.getElementById('selected-count');
    const selectionInfo = document.getElementById('selection-info');
    const selectedCountInfo = document.getElementById('selected-count-info');

    if (selectedItems.length > 0) {
        // 显示批量操作按钮
        if (batchDeleteBtn) {
            batchDeleteBtn.style.display = 'inline-block';
            if (selectedCountSpan) {
                selectedCountSpan.textContent = selectedItems.length;
            }
        }

        if (batchEditBtn) {
            // 只有选择一个项目时才显示编辑按钮
            batchEditBtn.style.display = selectedItems.length === 1 ? 'inline-block' : 'none';
        }

        if (selectionInfo && selectedCountInfo) {
            selectionInfo.style.display = 'inline-block';
            selectedCountInfo.textContent = selectedItems.length;
        }
    } else {
        // 隐藏批量操作按钮
        if (batchDeleteBtn) batchDeleteBtn.style.display = 'none';
        if (batchEditBtn) batchEditBtn.style.display = 'none';
        if (selectionInfo) selectionInfo.style.display = 'none';
    }

    // 更新全选复选框状态
    const selectAllCheckbox = document.getElementById('select-all');
    const allCheckboxes = document.querySelectorAll('.inventory-checkbox');
    if (selectAllCheckbox && allCheckboxes.length > 0) {
        const checkedCount = document.querySelectorAll('.inventory-checkbox:checked').length;
        selectAllCheckbox.checked = checkedCount === allCheckboxes.length;
        selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < allCheckboxes.length;
    }
}

/**
 * 库存页面：编辑选中的库存（单个）
 */
function editSelectedInventory() {
    const selectedItems = getSelectedInventoryItems();
    if (selectedItems.length === 1) {
        const item = selectedItems[0];
        const inventoryId = item.id;
        const productName = item.name;
        editInventory(inventoryId, productName);
    }
}

/**
 * 库存页面：双击编辑库存
 */
function editInventory(inventoryId, productName) {
    const editUrl = `/inventory/${inventoryId}/update/`;
    openEditModal(editUrl, productName, '库存编辑');
}

/**
 * 库存页面：确认批量删除
 */
function confirmBatchDeleteInventory() {
    const selectedItems = getSelectedInventoryItems();
    if (selectedItems.length === 0) {
        showAnimatedMessage('请先选择要删除的库存记录', 'warning');
        return;
    }

    const itemNames = selectedItems.map(item => item.name).join('、');
    const message = `确定要删除以下 ${selectedItems.length} 个库存记录吗？\n\n${itemNames}\n\n此操作不可撤销！`;

    if (confirm(message)) {
        batchDeleteInventory(selectedItems);
    }
}

/**
 * 库存页面：执行批量删除
 */
function batchDeleteInventory(items) {
    const deletePromises = items.map(item => {
        return fetch(item.deleteUrl, {
            method: 'DELETE',
            headers: {
                'X-CSRFToken': getCsrfToken(),
                'Content-Type': 'application/json',
            }
        });
    });

    Promise.all(deletePromises)
        .then(responses => {
            const successCount = responses.filter(response => response.ok).length;
            const failCount = items.length - successCount;

            if (successCount > 0) {
                showAnimatedMessage(`成功删除 ${successCount} 个库存记录`, 'success');
                // 刷新页面数据
                const searchInput = document.querySelector('input[name="search"]');
                const query = searchInput ? searchInput.value : '';
                executeAjaxSearch(query);
            }

            if (failCount > 0) {
                showAnimatedMessage(`${failCount} 个库存记录删除失败`, 'danger');
            }
        })
        .catch(error => {
            console.error('批量删除失败:', error);
            showAnimatedMessage('批量删除操作失败', 'danger');
        });
}

/**
 * 强制重新应用表格样式和布局
 */
function reapplyTableStyles() {
    const table = document.getElementById('inventory-table');
    if (table) {
        // 强制重新计算表格布局
        table.style.tableLayout = 'fixed';

        // 确保colgroup样式正确应用
        const colgroups = table.querySelectorAll('colgroup col');
        colgroups.forEach(col => {
            const width = col.style.width;
            if (width) {
                col.style.width = width; // 强制重新设置
            }
        });

        // 触发重新渲染
        table.offsetHeight; // 强制重排
    }
}

/**
 * 重新绑定库存页面的事件监听器
 */
function rebindInventoryEvents() {
    // 重新绑定所有库存复选框的change事件
    const inventoryCheckboxes = document.querySelectorAll('.inventory-checkbox');
    inventoryCheckboxes.forEach(checkbox => {
        // 移除旧的事件监听器（如果有）
        checkbox.removeEventListener('change', updateBatchDeleteButtonInventory);
        // 添加新的事件监听器
        checkbox.addEventListener('change', updateBatchDeleteButtonInventory);
    });

    // 重新绑定全选复选框的change事件
    const selectAllCheckbox = document.getElementById('select-all');
    if (selectAllCheckbox) {
        selectAllCheckbox.removeEventListener('change', toggleSelectAllInventory);
        selectAllCheckbox.addEventListener('change', toggleSelectAllInventory);
    }

    // 重新绑定双击编辑事件
    const inventoryRows = document.querySelectorAll('.inventory-row[data-editable="true"]');
    inventoryRows.forEach(row => {
        const inventoryId = row.dataset.inventoryId;
        const productName = row.dataset.productName;
        if (inventoryId && productName) {
            // 移除旧的事件监听器（如果有）
            if (row._editHandler) {
                row.removeEventListener('dblclick', row._editHandler);
            }
            // 创建新的事件处理器
            row._editHandler = () => editInventory(inventoryId, productName);
            row.addEventListener('dblclick', row._editHandler);
        }
    });
}

/**
 * 绑定库存页面筛选器和排序器的change事件
 */
function bindInventoryFilters() {
    const currentPath = window.location.pathname;

    // 只在库存页面绑定
    if (!currentPath.includes('inventory')) {
        return;
    }

    // 绑定库存状态筛选器
    const statusSelect = document.getElementById('status');
    if (statusSelect) {
        statusSelect.removeEventListener('change', handleInventoryFilterChange);
        statusSelect.addEventListener('change', handleInventoryFilterChange);
    }

    // 绑定排序方式选择器
    const orderBySelect = document.getElementById('order_by');
    if (orderBySelect) {
        orderBySelect.removeEventListener('change', handleInventoryFilterChange);
        orderBySelect.addEventListener('change', handleInventoryFilterChange);
    }
}

/**
 * 处理库存页面筛选器和排序器的change事件
 */
function handleInventoryFilterChange() {
    // 获取当前的搜索关键词
    const searchInput = document.querySelector('input[name="search"]');
    const query = searchInput ? searchInput.value.trim() : '';

    // 显示加载状态
    showLoadingState();

    // 更新URL参数
    updateInventoryUrlParams();

    // 执行AJAX搜索，重置到第一页
    executeAjaxSearch(query, 1);
}

/**
 * 更新库存页面的URL参数
 */
function updateInventoryUrlParams() {
    const currentPath = window.location.pathname;

    // 只在库存页面更新URL
    if (!currentPath.includes('inventory')) {
        return;
    }

    // 获取当前的筛选和排序参数
    const searchInput = document.querySelector('input[name="search"]');
    const statusSelect = document.getElementById('status');
    const orderBySelect = document.getElementById('order_by');

    const search = searchInput ? searchInput.value.trim() : '';
    const status = statusSelect ? statusSelect.value : '';
    const orderBy = orderBySelect ? orderBySelect.value : '-updated_at';

    // 构建新的URL参数
    const url = new URL(window.location);

    // 更新或删除参数
    if (search) {
        url.searchParams.set('search', search);
    } else {
        url.searchParams.delete('search');
    }

    if (status) {
        url.searchParams.set('status', status);
    } else {
        url.searchParams.delete('status');
    }

    if (orderBy && orderBy !== '-updated_at') {
        url.searchParams.set('order_by', orderBy);
    } else {
        url.searchParams.delete('order_by');
    }

    // 重置页码到第一页
    url.searchParams.delete('page');

    // 更新浏览器历史记录，但不刷新页面
    window.history.replaceState({}, '', url);
}

function confirmBatchDelete() {
    const selectedItems = getSelectedItems();

    if (selectedItems.length === 0) {
        showToast('请先选择要删除的商品！', 'warning');
        return;
    }

    const itemNames = selectedItems.map(item => item.name).join('、');

    if (confirm(`确定要删除以下 ${selectedItems.length} 个商品吗？\n\n${itemNames}\n\n此操作不可恢复！`)) {
        console.log('✅ 用户确认批量删除', selectedItems);

        // 显示加载状态
        const loadingToast = showToast(`正在删除 ${selectedItems.length} 个商品...`, 'info');

        // 批量删除请求
        const deletePromises = selectedItems.map(item =>
            fetch(item.deleteUrl, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': getCsrfToken(),
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json',
                },
                credentials: 'same-origin'
            })
        );

        Promise.allSettled(deletePromises)
            .then(results => {
                const successful = results.filter(result => result.status === 'fulfilled' && result.value.ok).length;
                const failed = selectedItems.length - successful;

                if (failed === 0) {
                    showToast(`成功删除 ${successful} 个商品！`, 'success');
                } else {
                    showToast(`删除完成：成功 ${successful} 个，失败 ${failed} 个`, 'warning');
                }

                // 重置复选框状态，确保界面状态与实际数据一致
                console.log('🔄 重置复选框状态');

                // 清除所有商品复选框的选中状态
                const productCheckboxes = document.querySelectorAll('.product-checkbox');
                productCheckboxes.forEach(checkbox => {
                    checkbox.checked = false;
                });

                // 重置全选复选框状态
                const selectAllCheckbox = document.getElementById('select-all');
                if (selectAllCheckbox) {
                    selectAllCheckbox.checked = false;
                }

                // 更新批量操作按钮状态（隐藏按钮）
                if (window.updateBatchDeleteButton) {
                    window.updateBatchDeleteButton();
                }

                // 刷新商品列表
                setTimeout(() => {
                    if (window.WarehouseJS && window.WarehouseJS.refreshProductTable) {
                        window.WarehouseJS.refreshProductTable();
                    } else {
                        window.location.reload();
                    }
                }, 1000);
            })
            .catch(error => {
                console.error('❌ 批量删除失败:', error);
                showToast('批量删除失败，请重试！', 'error');
            })
            .finally(() => {
                if (loadingToast) {
                    loadingToast.hide();
                }
            });
    }
}

// 应用权限相关的CSS类
function applyPermissionClasses() {
    // 检查是否有复选框列来判断删除权限
    const hasCheckboxColumn = document.querySelector('.checkbox-column');

    if (!hasCheckboxColumn) {
        document.body.classList.add('no-delete-permission');
        document.body.classList.remove('has-delete-permission');
    } else {
        document.body.classList.remove('no-delete-permission');
        document.body.classList.add('has-delete-permission');
    }

    console.log('🔐 权限CSS类已更新:', hasCheckboxColumn ? '有删除权限' : '无删除权限');
}

// 调试表格布局
function debugTableLayout() {
    document.body.classList.toggle('debug-table-layout');

    const table = document.getElementById('product-table');
    if (table) {
        const headers = table.querySelectorAll('th');
        const cells = table.querySelectorAll('td');

        // 为表头添加宽度信息
        headers.forEach((th, index) => {
            const computedStyle = window.getComputedStyle(th);
            const width = computedStyle.width;
            th.setAttribute('data-width', `${index + 1}: ${width}`);
        });

        // 为第一行数据单元格添加宽度信息
        const firstRow = table.querySelector('tbody tr');
        if (firstRow) {
            const firstRowCells = firstRow.querySelectorAll('td');
            firstRowCells.forEach((td, index) => {
                const computedStyle = window.getComputedStyle(td);
                const width = computedStyle.width;
                td.setAttribute('data-width', `${index + 1}: ${width}`);
            });
        }
    }

    console.log('🔍 表格布局调试模式已', document.body.classList.contains('debug-table-layout') ? '启用' : '禁用');
}

// 强制修复表格列宽
function forceFixTableLayout() {
    const table = document.getElementById('product-table');
    if (!table) {
        console.warn('⚠️ 未找到product-table');
        return;
    }

    console.log('🔧 强制修复表格布局');

    // 强制设置表格样式
    table.style.tableLayout = 'fixed';
    table.style.width = '100%';
    table.style.borderCollapse = 'separate';
    table.style.borderSpacing = '0';

    // 定义列宽
    const columnWidths = ['5%', '22%', '11%', '9%', '7%', '13%', '9%', '12%', '12%'];

    // 强制设置表头列宽
    const headers = table.querySelectorAll('thead th');
    headers.forEach((th, index) => {
        if (columnWidths[index]) {
            th.style.width = columnWidths[index];
            th.style.minWidth = columnWidths[index];
            th.style.maxWidth = columnWidths[index];
            th.style.boxSizing = 'border-box';
            console.log(`📐 表头列${index + 1}设置为: ${columnWidths[index]}`);
        }
    });

    // 强制设置数据行列宽
    const rows = table.querySelectorAll('tbody tr');
    rows.forEach(row => {
        const cells = row.querySelectorAll('td');
        cells.forEach((td, index) => {
            if (columnWidths[index]) {
                td.style.width = columnWidths[index];
                td.style.minWidth = columnWidths[index];
                td.style.maxWidth = columnWidths[index];
                td.style.boxSizing = 'border-box';
                td.style.overflow = 'hidden';
                td.style.textOverflow = 'ellipsis';
                td.style.whiteSpace = 'nowrap';
            }
        });
    });

    console.log('✅ 表格布局强制修复完成');
}

// 强制修复表格布局
function forceFixTableLayout() {
    const table = document.getElementById('product-table');
    if (!table) {
        console.warn('⚠️ 未找到product-table');
        return;
    }

    console.log('🔧 强制修复表格布局');

    // 强制设置表格样式
    table.style.tableLayout = 'fixed';
    table.style.width = '100%';

    // 检查是否有删除权限（通过复选框列判断）
    const hasDeletePermission = table.querySelector('.checkbox-column') !== null;
    console.log(`📊 检测到权限状态: ${hasDeletePermission ? '有删除权限(9列)' : '无删除权限(8列)'}`);

    // 检查colgroup是否存在且列数正确（移除操作列后）
    let colgroup = table.querySelector('colgroup');
    const expectedCols = hasDeletePermission ? 8 : 7;
    const currentCols = colgroup ? colgroup.children.length : 0;

    if (!colgroup || currentCols !== expectedCols) {
        console.log(`📐 重新创建colgroup (当前${currentCols}列，需要${expectedCols}列)`);

        // 移除旧的colgroup
        if (colgroup) {
            colgroup.remove();
        }

        // 创建新的colgroup
        colgroup = document.createElement('colgroup');

        const columnWidths = hasDeletePermission
            ? ['60px', '30%', '15%', '12%', '10%', '15%', '10%', '18%']
            : ['35%', '18%', '15%', '12%', '20%', '15%', '20%'];

        columnWidths.forEach((width, index) => {
            const col = document.createElement('col');
            col.style.width = width;
            colgroup.appendChild(col);
            console.log(`  列${index + 1}: ${width}`);
        });

        table.insertBefore(colgroup, table.firstChild);
    }

    console.log('✅ 表格布局强制修复完成');
}

// 添加到全局导出
window.confirmDelete = confirmDelete;
window.applyPermissionClasses = applyPermissionClasses;
window.debugTableLayout = debugTableLayout;
window.forceFixTableLayout = forceFixTableLayout;
window.forceFixTableLayout = forceFixTableLayout;
window.confirmBatchDelete = confirmBatchDelete;
window.getSelectedItems = getSelectedItems;
window.updateBatchDeleteButton = updateBatchDeleteButton;
window.toggleSelectAll = toggleSelectAll;
window.editSelectedProduct = editSelectedProduct;
window.editProduct = editProduct;
window.WarehouseJS.initializeInventoryEdit = initializeInventoryEdit;
// window.WarehouseJS.initializeProductManagement = initializeProductManagement; // 商品状态功能已移除
// window.WarehouseJS.toggleProductStatus = toggleProductStatus; // 商品状态功能已移除
window.WarehouseJS.refreshProductTable = refreshProductTable;
window.WarehouseJS.clearExistingMessages = clearExistingMessages;
window.WarehouseJS.forceInitializeCurrentPageCharts = forceInitializeCurrentPageCharts;
window.WarehouseJS.debugLayout = debugLayout;
window.WarehouseJS.forceResetLayout = forceResetLayout;
window.WarehouseJS.bindPaginationEvents = bindPaginationEvents;

// ==================== 批量上传功能 ====================

/**
 * 打开批量上传模态框
 */
function openBatchUploadModal() {
    console.log('🚀 打开批量上传模态框');

    // 创建模态框HTML
    const modalHtml = `
        <div class="modal fade" id="batchUploadModal" tabindex="-1" aria-labelledby="batchUploadModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="batchUploadModalLabel">
                            <i class="bi bi-upload"></i> 批量上传商品
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <!-- 文件选择区域 -->
                        <div class="mb-4">
                            <label for="batchUploadFile" class="form-label">
                                <i class="bi bi-file-earmark-excel"></i> 选择Excel文件
                            </label>
                            <input type="file"
                                   class="form-control"
                                   id="batchUploadFile"
                                   accept=".xlsx,.xls"
                                   onchange="validateUploadFile(this)">
                            <div class="form-text">
                                支持格式：.xlsx, .xls | 最大文件大小：10MB
                            </div>
                        </div>

                        <!-- 文件信息显示 -->
                        <div id="fileInfo" class="alert alert-info" style="display: none;">
                            <i class="bi bi-info-circle"></i>
                            <span id="fileInfoText"></span>
                        </div>

                        <!-- 重复处理策略选择 -->
                        <div class="mb-4" id="duplicateStrategySection" style="display: none;">
                            <label class="form-label">
                                <i class="bi bi-arrow-repeat"></i> 重复商品处理方式
                            </label>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="duplicateStrategy" id="strategySkip" value="skip" checked>
                                <label class="form-check-label" for="strategySkip">
                                    <strong>跳过重复商品</strong> - 保留现有商品，不创建重复项
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="duplicateStrategy" id="strategyUpdate" value="update">
                                <label class="form-check-label" for="strategyUpdate">
                                    <strong>更新现有商品</strong> - 用新数据更新现有商品的描述和价格
                                </label>
                            </div>
                        </div>

                        <!-- 重复检测结果预览 -->
                        <div id="duplicatePreview" style="display: none;" class="mb-4">
                            <div class="alert alert-warning">
                                <h6><i class="bi bi-exclamation-triangle"></i> 重复检测结果</h6>
                                <div id="duplicateStats"></div>
                                <div id="duplicateDetails" class="mt-2" style="display: none;">
                                    <small class="text-muted">重复商品详情：</small>
                                    <div id="duplicateList" class="mt-1"></div>
                                </div>
                                <button type="button" class="btn btn-sm btn-outline-secondary mt-2" onclick="toggleDuplicateDetails()">
                                    <span id="toggleDuplicateText">显示详情</span>
                                </button>
                            </div>
                        </div>

                        <!-- 上传进度 -->
                        <div id="uploadProgress" style="display: none;">
                            <label class="form-label">上传进度</label>
                            <div class="progress mb-2">
                                <div class="progress-bar progress-bar-striped progress-bar-animated"
                                     role="progressbar"
                                     id="uploadProgressBar"
                                     style="width: 0%">0%</div>
                            </div>
                            <small class="text-muted" id="uploadStatus">准备上传...</small>
                        </div>

                        <!-- 错误提示区域 -->
                        <div id="uploadError" class="alert alert-danger" style="display: none;">
                            <i class="bi bi-exclamation-triangle"></i>
                            <span id="uploadErrorText"></span>
                        </div>

                        <!-- 成功反馈区域 -->
                        <div id="uploadSuccess" class="alert alert-success" style="display: none;">
                            <i class="bi bi-check-circle"></i>
                            <span id="uploadSuccessText"></span>
                        </div>

                        <!-- 使用说明 -->
                        <div class="mt-4">
                            <h6><i class="bi bi-lightbulb"></i> 使用说明：</h6>
                            <ol class="small text-muted">
                                <li>点击"下载模板"获取标准格式的Excel模板</li>
                                <li>在模板中填写商品信息（商品名称、商品描述、价格）</li>
                                <li>商品编码(SKU)将根据商品名称自动生成，无需手动填写</li>
                                <li>保存Excel文件并在此处上传</li>
                                <li>系统将自动验证数据并批量创建商品</li>
                            </ol>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-outline-secondary" onclick="downloadProductTemplate()">
                            <i class="bi bi-download"></i> 下载模板
                        </button>
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-success" id="startUploadBtn" onclick="startBatchUpload()" disabled>
                            <i class="bi bi-upload"></i> 开始上传
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 移除已存在的模态框
    const existingModal = document.getElementById('batchUploadModal');
    if (existingModal) {
        existingModal.remove();
    }

    // 添加模态框到页面
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('batchUploadModal'));
    modal.show();

    // 绑定模态框关闭事件
    document.getElementById('batchUploadModal').addEventListener('hidden.bs.modal', function() {
        this.remove(); // 关闭时移除模态框
    });
}

/**
 * 验证上传文件
 */
function validateUploadFile(input) {
    const file = input.files[0];
    const fileInfo = document.getElementById('fileInfo');
    const fileInfoText = document.getElementById('fileInfoText');
    const startUploadBtn = document.getElementById('startUploadBtn');
    const uploadError = document.getElementById('uploadError');
    const uploadErrorText = document.getElementById('uploadErrorText');

    // 隐藏之前的错误信息
    uploadError.style.display = 'none';

    if (!file) {
        fileInfo.style.display = 'none';
        startUploadBtn.disabled = true;
        return;
    }

    // 验证文件格式
    const allowedTypes = [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
        'application/vnd.ms-excel' // .xls
    ];

    if (!allowedTypes.includes(file.type) && !file.name.match(/\.(xlsx|xls)$/i)) {
        uploadErrorText.textContent = '请选择有效的Excel文件（.xlsx 或 .xls 格式）';
        uploadError.style.display = 'block';
        fileInfo.style.display = 'none';
        startUploadBtn.disabled = true;
        input.value = ''; // 清空文件选择
        return;
    }

    // 验证文件大小（10MB限制）
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
        uploadErrorText.textContent = '文件大小不能超过10MB，请选择较小的文件';
        uploadError.style.display = 'block';
        fileInfo.style.display = 'none';
        startUploadBtn.disabled = true;
        input.value = ''; // 清空文件选择
        return;
    }

    // 显示文件信息
    const fileSizeMB = (file.size / (1024 * 1024)).toFixed(2);
    fileInfoText.textContent = `已选择文件：${file.name} (${fileSizeMB}MB)`;
    fileInfo.style.display = 'block';

    // 预览重复检测
    previewDuplicates(file);

    console.log('✅ 文件验证通过:', file.name);
}

/**
 * 预览重复检测
 */
function previewDuplicates(file) {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('csrfmiddlewaretoken', getCsrfToken());

    // 显示加载状态
    const duplicatePreview = document.getElementById('duplicatePreview');
    const duplicateStats = document.getElementById('duplicateStats');
    duplicateStats.innerHTML = '<i class="bi bi-hourglass-split"></i> 正在检测重复商品...';
    duplicatePreview.style.display = 'block';

    fetch('/products/api/preview-duplicates/', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayDuplicatePreview(data);
        } else {
            duplicateStats.innerHTML = `<i class="bi bi-exclamation-triangle"></i> 检测失败：${data.error}`;
        }
    })
    .catch(error => {
        console.error('重复检测失败:', error);
        duplicateStats.innerHTML = '<i class="bi bi-exclamation-triangle"></i> 检测失败，请重试';
    });
}

/**
 * 显示重复检测预览结果
 */
function displayDuplicatePreview(data) {
    const duplicateStats = document.getElementById('duplicateStats');
    const duplicateDetails = document.getElementById('duplicateDetails');
    const duplicateList = document.getElementById('duplicateList');
    const strategySection = document.getElementById('duplicateStrategySection');
    const startUploadBtn = document.getElementById('startUploadBtn');

    // 显示统计信息
    let statsHtml = `
        <div class="row text-center">
            <div class="col-4">
                <div class="fw-bold text-success">${data.new_count}</div>
                <small>新商品</small>
            </div>
            <div class="col-4">
                <div class="fw-bold text-warning">${data.duplicate_count}</div>
                <small>重复商品</small>
            </div>
            <div class="col-4">
                <div class="fw-bold text-info">${data.total_count}</div>
                <small>总计</small>
            </div>
        </div>
    `;
    duplicateStats.innerHTML = statsHtml;

    // 显示重复商品详情
    if (data.duplicate_count > 0) {
        let detailsHtml = '';
        data.duplicates.forEach(dup => {
            const priceChangeIcon = dup.price_changed ?
                '<i class="bi bi-arrow-right text-warning" title="价格有变化"></i>' :
                '<i class="bi bi-check text-success" title="价格相同"></i>';

            detailsHtml += `
                <div class="small border-bottom py-1">
                    第${dup.row_number}行: <strong>${dup.name}</strong>
                    (现有SKU: ${dup.existing_sku})
                    ${priceChangeIcon}
                    ¥${dup.existing_price} → ¥${dup.new_price}
                </div>
            `;
        });
        duplicateList.innerHTML = detailsHtml;

        // 显示重复处理策略选择
        strategySection.style.display = 'block';
    } else {
        duplicateDetails.style.display = 'none';
        strategySection.style.display = 'none';
    }

    // 启用上传按钮
    startUploadBtn.disabled = false;
}

/**
 * 切换重复商品详情显示
 */
function toggleDuplicateDetails() {
    const duplicateDetails = document.getElementById('duplicateDetails');
    const toggleText = document.getElementById('toggleDuplicateText');

    if (duplicateDetails.style.display === 'none') {
        duplicateDetails.style.display = 'block';
        toggleText.textContent = '隐藏详情';
    } else {
        duplicateDetails.style.display = 'none';
        toggleText.textContent = '显示详情';
    }
}

/**
 * 开始批量上传
 */
function startBatchUpload() {
    const fileInput = document.getElementById('batchUploadFile');
    const file = fileInput.files[0];

    if (!file) {
        showUploadError('请先选择要上传的文件');
        return;
    }

    console.log('🚀 开始批量上传:', file.name);

    // 显示上传进度
    showUploadProgress();

    // 禁用上传按钮
    const startUploadBtn = document.getElementById('startUploadBtn');
    startUploadBtn.disabled = true;
    startUploadBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 上传中...';

    // 获取重复处理策略
    const duplicateStrategy = document.querySelector('input[name="duplicateStrategy"]:checked')?.value || 'skip';

    // 创建FormData
    const formData = new FormData();
    formData.append('file', file);
    formData.append('duplicate_strategy', duplicateStrategy);
    formData.append('csrfmiddlewaretoken', getCsrfToken());

    console.log('📋 重复处理策略:', duplicateStrategy);

    // 执行上传
    uploadFileWithProgress(formData);
}

/**
 * 带进度的文件上传
 */
function uploadFileWithProgress(formData) {
    const xhr = new XMLHttpRequest();

    // 上传进度监听
    xhr.upload.addEventListener('progress', function(e) {
        if (e.lengthComputable) {
            const percentComplete = Math.round((e.loaded / e.total) * 100);
            updateUploadProgress(percentComplete, '上传中...');
        }
    });

    // 上传完成监听
    xhr.addEventListener('load', function() {
        if (xhr.status === 200) {
            try {
                const response = JSON.parse(xhr.responseText);
                handleUploadResponse(response);
            } catch (error) {
                showUploadError('服务器响应格式错误，请重试');
                resetUploadButton();
            }
        } else {
            showUploadError(`上传失败：HTTP ${xhr.status}`);
            resetUploadButton();
        }
    });

    // 上传错误监听
    xhr.addEventListener('error', function() {
        showUploadError('网络错误，请检查网络连接后重试');
        resetUploadButton();
    });

    // 发送请求
    xhr.open('POST', '/products/api/batch-upload/', true);
    xhr.send(formData);
}

/**
 * 更新上传进度
 */
function updateUploadProgress(percent, status) {
    const progressBar = document.getElementById('uploadProgressBar');
    const uploadStatus = document.getElementById('uploadStatus');

    progressBar.style.width = percent + '%';
    progressBar.textContent = percent + '%';
    uploadStatus.textContent = status;

    if (percent === 100) {
        uploadStatus.textContent = '处理中，请稍候...';
    }
}

/**
 * 显示上传进度区域
 */
function showUploadProgress() {
    const uploadProgress = document.getElementById('uploadProgress');
    const uploadError = document.getElementById('uploadError');
    const uploadSuccess = document.getElementById('uploadSuccess');

    uploadProgress.style.display = 'block';
    uploadError.style.display = 'none';
    uploadSuccess.style.display = 'none';

    updateUploadProgress(0, '准备上传...');
}

/**
 * 显示上传错误
 */
function showUploadError(message) {
    const uploadError = document.getElementById('uploadError');
    const uploadErrorText = document.getElementById('uploadErrorText');
    const uploadProgress = document.getElementById('uploadProgress');
    const uploadSuccess = document.getElementById('uploadSuccess');

    uploadErrorText.textContent = message;
    uploadError.style.display = 'block';
    uploadProgress.style.display = 'none';
    uploadSuccess.style.display = 'none';

    console.error('❌ 上传错误:', message);
}

/**
 * 处理上传响应
 */
function handleUploadResponse(response) {
    updateUploadProgress(100, '处理完成');

    if (response.success) {
        showUploadSuccess(response);

        // 3秒后关闭模态框并刷新列表
        setTimeout(() => {
            const modal = bootstrap.Modal.getInstance(document.getElementById('batchUploadModal'));
            modal.hide();

            // 刷新商品列表
            if (window.WarehouseJS && window.WarehouseJS.refreshProductTable) {
                window.WarehouseJS.refreshProductTable();
            } else {
                window.location.reload();
            }
        }, 3000);
    } else {
        showUploadError(response.error || '上传处理失败');
        resetUploadButton();
    }
}

/**
 * 显示上传成功
 */
function showUploadSuccess(response) {
    const uploadSuccess = document.getElementById('uploadSuccess');
    const uploadSuccessText = document.getElementById('uploadSuccessText');
    const uploadProgress = document.getElementById('uploadProgress');
    const uploadError = document.getElementById('uploadError');

    let message = `批量上传成功！`;
    let messageParts = [];

    if (response.created_count > 0) {
        messageParts.push(`成功创建 ${response.created_count} 个商品`);
    }
    if (response.updated_count && response.updated_count > 0) {
        messageParts.push(`更新 ${response.updated_count} 个商品`);
    }
    if (response.skipped_count && response.skipped_count > 0) {
        messageParts.push(`跳过 ${response.skipped_count} 个商品`);
    }

    if (messageParts.length > 0) {
        message += ` ${messageParts.join('，')}`;
    }
    message += `。页面将在3秒后自动刷新...`;

    uploadSuccessText.textContent = message;
    uploadSuccess.style.display = 'block';
    uploadProgress.style.display = 'none';
    uploadError.style.display = 'none';

    console.log('✅ 上传成功:', response);
}

/**
 * 重置上传按钮
 */
function resetUploadButton() {
    const startUploadBtn = document.getElementById('startUploadBtn');
    if (startUploadBtn) {
        startUploadBtn.disabled = false;
        startUploadBtn.innerHTML = '<i class="bi bi-upload"></i> 开始上传';
    }
}

// ==================== 模板下载功能 ====================

/**
 * 下载商品批量上传模板
 */
function downloadProductTemplate() {
    console.log('📥 开始下载商品模板');

    // 显示加载状态
    const originalButton = event.target.closest('button');
    const originalContent = originalButton.innerHTML;
    originalButton.disabled = true;
    originalButton.innerHTML = '<i class="bi bi-hourglass-split"></i> 下载中...';

    // 显示加载提示
    const loadingToast = showToast('正在生成模板文件...', 'info');

    // 请求模板文件
    fetch('/products/api/download-template/', {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRFToken': getCsrfToken()
        },
        credentials: 'same-origin'
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        // 检查响应类型
        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')) {
            throw new Error('服务器返回的不是有效的Excel文件');
        }

        return response.blob();
    })
    .then(blob => {
        // 创建下载链接
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = '商品批量上传模板.xlsx';

        // 触发下载
        document.body.appendChild(link);
        link.click();

        // 清理
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        // 显示成功消息
        if (loadingToast) {
            loadingToast.hide();
        }
        showToast('模板文件下载成功！', 'success');

        console.log('✅ 模板下载成功');
    })
    .catch(error => {
        console.error('❌ 模板下载失败:', error);

        // 隐藏加载提示
        if (loadingToast) {
            loadingToast.hide();
        }

        // 显示错误消息
        let errorMessage = '模板下载失败';
        if (error.message.includes('HTTP 403')) {
            errorMessage = '权限不足，无法下载模板文件';
        } else if (error.message.includes('HTTP 404')) {
            errorMessage = '模板文件不存在，请联系管理员';
        } else if (error.message.includes('HTTP 500')) {
            errorMessage = '服务器错误，请稍后重试';
        } else if (error.message.includes('网络')) {
            errorMessage = '网络连接失败，请检查网络后重试';
        } else if (error.message) {
            errorMessage = `下载失败：${error.message}`;
        }

        showToast(errorMessage, 'error');
    })
    .finally(() => {
        // 恢复按钮状态
        if (originalButton) {
            originalButton.disabled = false;
            originalButton.innerHTML = originalContent;
        }
    });
}

// 添加到全局导出
window.openBatchUploadModal = openBatchUploadModal;
window.validateUploadFile = validateUploadFile;
window.startBatchUpload = startBatchUpload;
window.downloadProductTemplate = downloadProductTemplate;
window.previewDuplicates = previewDuplicates;
window.toggleDuplicateDetails = toggleDuplicateDetails;

// 进出库记录批量上传相关函数
window.openTransactionBatchUploadModal = openTransactionBatchUploadModal;
window.validateTransactionUploadFile = validateTransactionUploadFile;
window.startTransactionBatchUpload = startTransactionBatchUpload;
window.downloadTransactionTemplate = downloadTransactionTemplate;

// 进出库记录图表相关函数
window.loadTransactionChartData = loadTransactionChartData;
window.initializeTransactionCharts = initializeTransactionCharts;
window.refreshTransactionCharts = refreshTransactionCharts;
window.getTransactionChartOptions = getTransactionChartOptions;
window.updateTransactionChartRange = updateTransactionChartRange;

/**
 * 打开进出库记录批量上传模态框
 */
function openTransactionBatchUploadModal() {
    console.log('📤 打开进出库记录批量上传模态框');

    // 检查是否已存在模态框
    let existingModal = document.getElementById('transactionBatchUploadModal');
    if (existingModal) {
        existingModal.remove();
    }

    // 创建模态框HTML
    const modalHtml = `
        <div class="modal fade" id="transactionBatchUploadModal" tabindex="-1" aria-labelledby="transactionBatchUploadModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="transactionBatchUploadModalLabel">
                            <i class="bi bi-upload"></i> 批量上传进出库记录
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <!-- 文件选择区域 -->
                        <div class="mb-4">
                            <label for="transactionBatchUploadFile" class="form-label">
                                <i class="bi bi-file-earmark-excel"></i> 选择Excel文件
                            </label>
                            <input type="file"
                                   class="form-control"
                                   id="transactionBatchUploadFile"
                                   accept=".xlsx,.xls"
                                   onchange="validateTransactionUploadFile(this)">
                            <div class="form-text">
                                支持.xlsx和.xls格式，文件大小不超过10MB
                            </div>
                        </div>

                        <!-- 上传进度区域 -->
                        <div id="transactionUploadProgress" style="display: none;">
                            <div class="mb-3">
                                <label class="form-label">上传进度</label>
                                <div class="progress">
                                    <div class="progress-bar progress-bar-striped progress-bar-animated"
                                         role="progressbar"
                                         style="width: 0%"
                                         id="transactionUploadProgressBar">0%</div>
                                </div>
                            </div>
                            <div id="transactionUploadStatus" class="small text-muted">准备上传...</div>
                        </div>

                        <!-- 上传结果区域 -->
                        <div id="transactionUploadResult" style="display: none;">
                            <div class="alert" id="transactionUploadResultAlert">
                                <div id="transactionUploadResultContent"></div>
                            </div>
                        </div>

                        <!-- 使用说明 -->
                        <div class="mt-4">
                            <h6><i class="bi bi-lightbulb"></i> 使用说明：</h6>
                            <ol class="small text-muted">
                                <li>点击"下载模板"获取标准格式的Excel模板</li>
                                <li>在模板中填写进出库记录信息（商品名称、操作类型、数量、备注等）</li>
                                <li>请确保商品名称在系统中存在且唯一</li>
                                <li>保存Excel文件并在此处上传</li>
                                <li>系统将自动验证数据并批量创建进出库记录</li>
                            </ol>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-outline-secondary" onclick="downloadTransactionTemplate()">
                            <i class="bi bi-download"></i> 下载模板
                        </button>
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-success" id="startTransactionUploadBtn" onclick="startTransactionBatchUpload()" disabled>
                            <i class="bi bi-upload"></i> 开始上传
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 添加到页面
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('transactionBatchUploadModal'));
    modal.show();

    // 模态框关闭时清理
    document.getElementById('transactionBatchUploadModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

/**
 * 验证进出库记录上传文件
 */
function validateTransactionUploadFile(input) {
    const file = input.files[0];
    const startUploadBtn = document.getElementById('startTransactionUploadBtn');

    if (!file) {
        startUploadBtn.disabled = true;
        return;
    }

    // 验证文件类型
    const allowedTypes = [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-excel'
    ];

    if (!allowedTypes.includes(file.type) && !file.name.match(/\.(xlsx|xls)$/i)) {
        showTransactionUploadError('请选择Excel文件（.xlsx或.xls格式）');
        input.value = '';
        startUploadBtn.disabled = true;
        return;
    }

    // 验证文件大小（10MB）
    if (file.size > 10 * 1024 * 1024) {
        showTransactionUploadError('文件大小不能超过10MB');
        input.value = '';
        startUploadBtn.disabled = true;
        return;
    }

    // 验证通过
    startUploadBtn.disabled = false;
    console.log('✅ 进出库记录文件验证通过:', file.name);
}

/**
 * 显示进出库记录上传错误
 */
function showTransactionUploadError(message) {
    const resultDiv = document.getElementById('transactionUploadResult');
    const alertDiv = document.getElementById('transactionUploadResultAlert');
    const contentDiv = document.getElementById('transactionUploadResultContent');

    alertDiv.className = 'alert alert-danger';
    contentDiv.innerHTML = `<i class="bi bi-exclamation-triangle"></i> ${message}`;
    resultDiv.style.display = 'block';

    console.error('❌ 进出库记录上传错误:', message);
}

/**
 * 显示进出库记录上传进度
 */
function showTransactionUploadProgress() {
    const progressDiv = document.getElementById('transactionUploadProgress');
    const resultDiv = document.getElementById('transactionUploadResult');

    progressDiv.style.display = 'block';
    resultDiv.style.display = 'none';

    // 重置进度条
    const progressBar = document.getElementById('transactionUploadProgressBar');
    const statusDiv = document.getElementById('transactionUploadStatus');

    progressBar.style.width = '0%';
    progressBar.textContent = '0%';
    statusDiv.textContent = '正在上传文件...';
}

/**
 * 开始进出库记录批量上传
 */
function startTransactionBatchUpload() {
    const fileInput = document.getElementById('transactionBatchUploadFile');
    const file = fileInput.files[0];

    if (!file) {
        showTransactionUploadError('请先选择要上传的文件');
        return;
    }

    console.log('🚀 开始进出库记录批量上传:', file.name);

    // 显示上传进度
    showTransactionUploadProgress();

    // 禁用上传按钮
    const startUploadBtn = document.getElementById('startTransactionUploadBtn');
    startUploadBtn.disabled = true;
    startUploadBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 上传中...';

    // 创建FormData
    const formData = new FormData();
    formData.append('file', file);
    formData.append('csrfmiddlewaretoken', getCsrfToken());

    // 执行上传
    uploadTransactionFileWithProgress(formData);
}

/**
 * 带进度的进出库记录文件上传
 */
function uploadTransactionFileWithProgress(formData) {
    const xhr = new XMLHttpRequest();
    const progressBar = document.getElementById('transactionUploadProgressBar');
    const statusDiv = document.getElementById('transactionUploadStatus');

    // 上传进度监听
    xhr.upload.addEventListener('progress', function(e) {
        if (e.lengthComputable) {
            const percentComplete = Math.round((e.loaded / e.total) * 100);
            progressBar.style.width = percentComplete + '%';
            progressBar.textContent = percentComplete + '%';
            statusDiv.textContent = `正在上传文件... ${percentComplete}%`;
        }
    });

    // 请求完成监听
    xhr.addEventListener('load', function() {
        if (xhr.status === 200) {
            try {
                const response = JSON.parse(xhr.responseText);
                handleTransactionUploadResponse(response);
            } catch (error) {
                console.error('❌ 解析响应失败:', error);
                showTransactionUploadError('服务器响应格式错误');
            }
        } else {
            console.error('❌ 上传请求失败:', xhr.status, xhr.statusText);
            showTransactionUploadError(`上传失败：HTTP ${xhr.status}`);
        }

        // 恢复上传按钮
        const startUploadBtn = document.getElementById('startTransactionUploadBtn');
        startUploadBtn.disabled = false;
        startUploadBtn.innerHTML = '<i class="bi bi-upload"></i> 开始上传';
    });

    // 请求错误监听
    xhr.addEventListener('error', function() {
        console.error('❌ 网络错误');
        showTransactionUploadError('网络连接失败，请检查网络后重试');

        // 恢复上传按钮
        const startUploadBtn = document.getElementById('startTransactionUploadBtn');
        startUploadBtn.disabled = false;
        startUploadBtn.innerHTML = '<i class="bi bi-upload"></i> 开始上传';
    });

    // 发送请求
    xhr.open('POST', '/transactions/api/batch-upload/');
    xhr.send(formData);
}

/**
 * 处理进出库记录上传响应
 */
function handleTransactionUploadResponse(response) {
    const progressDiv = document.getElementById('transactionUploadProgress');
    const resultDiv = document.getElementById('transactionUploadResult');
    const alertDiv = document.getElementById('transactionUploadResultAlert');
    const contentDiv = document.getElementById('transactionUploadResultContent');

    // 隐藏进度条，显示结果
    progressDiv.style.display = 'none';
    resultDiv.style.display = 'block';

    if (response.success) {
        // 成功
        alertDiv.className = 'alert alert-success';

        let resultHtml = `
            <div class="d-flex align-items-center mb-2">
                <i class="bi bi-check-circle-fill me-2"></i>
                <strong>${response.message}</strong>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <small class="text-muted">成功创建：</small>
                    <span class="badge bg-success">${response.created_count}</span>
                </div>
                <div class="col-md-6">
                    <small class="text-muted">跳过记录：</small>
                    <span class="badge bg-warning">${response.skipped_count}</span>
                </div>
            </div>
        `;

        if (response.details && response.details.length > 0) {
            resultHtml += `
                <div class="mt-3">
                    <small class="text-muted">详细信息：</small>
                    <div class="small mt-1" style="max-height: 200px; overflow-y: auto;">
                        ${response.details.map(detail => `<div>${detail}</div>`).join('')}
                    </div>
                </div>
            `;
        }

        contentDiv.innerHTML = resultHtml;

        // 3秒后自动关闭模态框并刷新页面
        setTimeout(() => {
            const modal = bootstrap.Modal.getInstance(document.getElementById('transactionBatchUploadModal'));
            if (modal) {
                modal.hide();
            }

            // 刷新进出库趋势图表
            if (window.refreshTransactionCharts) {
                try {
                    window.refreshTransactionCharts();
                } catch (error) {
                    console.error('❌ 刷新进出库图表失败:', error);
                }
            }

            window.location.reload();
        }, 3000);

        console.log('✅ 进出库记录批量上传成功');
    } else {
        // 失败
        alertDiv.className = 'alert alert-danger';
        contentDiv.innerHTML = `
            <div class="d-flex align-items-center">
                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                <strong>上传失败：${response.error}</strong>
            </div>
        `;
        console.error('❌ 进出库记录批量上传失败:', response.error);
    }
}

/**
 * 下载进出库记录批量上传模板
 */
function downloadTransactionTemplate() {
    console.log('📥 开始下载进出库记录模板');

    // 显示加载状态
    const originalButton = event.target.closest('button');
    const originalContent = originalButton.innerHTML;
    originalButton.disabled = true;
    originalButton.innerHTML = '<i class="bi bi-hourglass-split"></i> 下载中...';

    // 显示加载提示
    const loadingToast = showToast('正在生成模板文件...', 'info');

    // 请求模板文件
    fetch('/transactions/api/download-template/', {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRFToken': getCsrfToken()
        },
        credentials: 'same-origin'
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return response.blob();
    })
    .then(blob => {
        // 创建下载链接
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = '进出库记录批量上传模板.xlsx';

        // 触发下载
        document.body.appendChild(link);
        link.click();

        // 清理
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        // 显示成功消息
        if (loadingToast) {
            loadingToast.hide();
        }
        showToast('模板文件下载成功！', 'success');

        console.log('✅ 进出库记录模板下载成功');
    })
    .catch(error => {
        console.error('❌ 进出库记录模板下载失败:', error);

        // 隐藏加载提示
        if (loadingToast) {
            loadingToast.hide();
        }

        // 显示错误消息
        let errorMessage = '模板下载失败';
        if (error.message.includes('HTTP 403')) {
            errorMessage = '权限不足，无法下载模板文件';
        } else if (error.message.includes('HTTP 404')) {
            errorMessage = '模板文件不存在，请联系管理员';
        } else if (error.message.includes('HTTP 500')) {
            errorMessage = '服务器错误，请稍后重试';
        } else if (error.message.includes('网络')) {
            errorMessage = '网络连接失败，请检查网络后重试';
        } else if (error.message) {
            errorMessage = `下载失败：${error.message}`;
        }

        showToast(errorMessage, 'error');
    })
    .finally(() => {
        // 恢复按钮状态
        if (originalButton) {
            originalButton.disabled = false;
            originalButton.innerHTML = originalContent;
        }
    });
}

/**
 * 初始化商品搜索选择组件 - 使用Select2
 */
function initializeProductSearch() {
    const productSelects = document.querySelectorAll('.product-search');

    productSelects.forEach(select => {
        if (select.dataset.searchUrl) {
            // 使用Select2初始化
            $(select).select2({
                theme: 'bootstrap-5',
                placeholder: '请选择商品...',
                allowClear: true,
                ajax: {
                    url: select.dataset.searchUrl,
                    dataType: 'json',
                    delay: 250,
                    data: function (params) {
                        return {
                            search: params.term || '',
                            page: params.page || 1
                        };
                    },
                    processResults: function (data, params) {
                        params.page = params.page || 1;

                        if (data.success && data.results) {
                            return {
                                results: data.results.map(item => ({
                                    id: item.id,
                                    text: `${item.name} (${item.sku})`
                                })),
                                pagination: {
                                    more: data.has_next || false
                                }
                            };
                        }
                        return { results: [] };
                    },
                    cache: true
                },
                minimumInputLength: 0,
                language: {
                    noResults: function() {
                        return '未找到匹配的商品';
                    },
                    searching: function() {
                        return '搜索中...';
                    },
                    inputTooShort: function() {
                        return '请输入商品名称或SKU进行搜索';
                    }
                }
            });

            // 监听选择变化事件，触发库存信息更新
            $(select).on('select2:select', function(e) {
                const event = new Event('change', { bubbles: true });
                this.dispatchEvent(event);
            });
        }
    });
}

// searchProducts函数已被Select2的内置AJAX功能替代

// 在页面加载完成后初始化商品搜索功能
document.addEventListener('DOMContentLoaded', function() {
    // 延迟初始化，确保其他组件已加载
    setTimeout(initializeProductSearch, 500);
});

// 将库存相关函数添加到全局作用域
window.toggleSelectAllInventory = toggleSelectAllInventory;
window.updateBatchDeleteButtonInventory = updateBatchDeleteButtonInventory;
window.getSelectedInventoryItems = getSelectedInventoryItems;
window.editSelectedInventory = editSelectedInventory;
window.editInventory = editInventory;
window.confirmBatchDeleteInventory = confirmBatchDeleteInventory;
window.batchDeleteInventory = batchDeleteInventory;
window.reapplyTableStyles = reapplyTableStyles;
window.rebindInventoryEvents = rebindInventoryEvents;
window.bindInventoryFilters = bindInventoryFilters;
window.handleInventoryFilterChange = handleInventoryFilterChange;
window.updateInventoryUrlParams = updateInventoryUrlParams;
