{% extends "products/base.html" %}
{% load static %}

{% block title %}用户角色管理 - 仓库管理系统{% endblock %}

{% block extra_css %}
<style>
.user-management-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    padding: 1.5rem;
}

.user-card {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    transition: all 0.2s ease;
}

.user-card:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.user-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 1.2rem;
}

.user-info {
    flex: 1;
    margin-left: 1rem;
}

.user-name {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.user-email {
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.user-meta {
    display: flex;
    gap: 1rem;
    font-size: 0.8rem;
    color: #6c757d;
}

.role-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.role-super-admin {
    background-color: #e3f2fd;
    color: #1565c0;
}

.role-warehouse-manager {
    background-color: #e8f5e8;
    color: #2e7d32;
}

.role-warehouse-operator {
    background-color: #fff3e0;
    color: #ef6c00;
}

.role-viewer {
    background-color: #f3e5f5;
    color: #7b1fa2;
}

.role-select {
    min-width: 180px;
}

.user-actions {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.search-filters {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1.5rem;
}

.filter-row {
    display: flex;
    gap: 1rem;
    align-items: end;
    flex-wrap: wrap;
}

.filter-group {
    flex: 1;
    min-width: 200px;
}

.stats-bar {
    background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1.5rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

.bulk-actions {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    display: none;
}

.bulk-actions.show {
    display: block;
}

.loading-spinner {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    /* 移除旋转动画，保留静态加载指示器 */
}

/* spin动画已移除 */

@media (max-width: 768px) {
    .user-card {
        flex-direction: column;
        text-align: center;
    }
    
    .user-info {
        margin-left: 0;
        margin-top: 1rem;
    }
    
    .user-actions {
        margin-top: 1rem;
        justify-content: center;
    }
    
    .filter-row {
        flex-direction: column;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">用户角色管理</h1>
        <div class="btn-group">
            <a href="{% url 'users:permission_dashboard' %}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> 返回仪表板
            </a>
            <button type="button" class="btn btn-success" onclick="exportUsers()">
                <i class="fas fa-download"></i> 导出用户
            </button>
        </div>
    </div>

    <!-- 统计信息 -->
    <div class="stats-bar">
        <div class="stats-grid">
            <div class="stat-item">
                <div class="stat-number">{{ total_users }}</div>
                <div class="stat-label">总用户数</div>
            </div>
            {% for role_code, role_name in roles %}
            <div class="stat-item">
                <div class="stat-number" id="role-count-{{ role_code }}">
                    {{ users|length|default:0 }}
                </div>
                <div class="stat-label">{{ role_name }}</div>
            </div>
            {% endfor %}
        </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="search-filters">
        <div class="filter-row">
            <div class="filter-group">
                <label for="searchInput" class="form-label">搜索用户</label>
                <input type="text" id="searchInput" class="form-control" 
                       placeholder="输入用户名或邮箱..." onkeyup="searchUsers()">
            </div>
            <div class="filter-group">
                <label for="roleFilter" class="form-label">角色筛选</label>
                <select id="roleFilter" class="form-select" onchange="filterByRole()">
                    <option value="">全部角色</option>
                    {% for role_code, role_name in roles %}
                    <option value="{{ role_code }}">{{ role_name }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="filter-group">
                <label for="statusFilter" class="form-label">状态筛选</label>
                <select id="statusFilter" class="form-select" onchange="filterByStatus()">
                    <option value="">全部状态</option>
                    <option value="active">活跃</option>
                    <option value="inactive">非活跃</option>
                </select>
            </div>
            <div class="filter-group">
                <label class="form-label">&nbsp;</label>
                <button type="button" class="btn btn-outline-secondary w-100" onclick="clearFilters()">
                    <i class="fas fa-times"></i> 清除筛选
                </button>
            </div>
        </div>
    </div>

    <!-- 批量操作 -->
    <div class="bulk-actions" id="bulkActions">
        <div class="d-flex justify-content-between align-items-center">
            <span>已选择 <span id="selectedCount">0</span> 个用户</span>
            <div class="btn-group">
                <button type="button" class="btn btn-sm btn-outline-primary" onclick="bulkChangeRole()">
                    <i class="fas fa-user-tag"></i> 批量修改角色
                </button>
                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="clearSelection()">
                    <i class="fas fa-times"></i> 取消选择
                </button>
            </div>
        </div>
    </div>

    <!-- 用户列表 -->
    <div class="user-management-container">
        <div id="userList">
            {% for user in users %}
            <div class="user-card d-flex align-items-center" 
                 data-user-id="{{ user.id }}"
                 data-username="{{ user.username|lower }}"
                 data-email="{{ user.email|lower }}"
                 data-role="{{ user.userprofile.role }}"
                 data-status="{% if user.is_active %}active{% else %}inactive{% endif %}">
                
                <input type="checkbox" class="form-check-input me-3 user-checkbox" 
                       value="{{ user.id }}" onchange="updateSelection()">
                
                <div class="user-avatar">
                    {{ user.username|first|upper }}
                </div>
                
                <div class="user-info">
                    <div class="user-name">{{ user.username }}</div>
                    <div class="user-email">{{ user.email }}</div>
                    <div class="user-meta">
                        <span>
                            <i class="fas fa-circle {% if user.is_active %}text-success{% else %}text-danger{% endif %}"></i>
                            {% if user.is_active %}活跃{% else %}非活跃{% endif %}
                        </span>
                        {% if user.last_login %}
                        <span>
                            <i class="fas fa-clock"></i>
                            最后登录: {{ user.last_login|timesince }}前
                        </span>
                        {% endif %}
                    </div>
                </div>
                
                <div class="user-actions">
                    <span class="role-badge role-{{ user.userprofile.role }}">
                        {{ user.userprofile.get_role_display }}
                    </span>
                    
                    <select class="form-select form-select-sm role-select" 
                            data-user-id="{{ user.id }}"
                            onchange="changeUserRole(this)">
                        {% for role_code, role_name in roles %}
                        <option value="{{ role_code }}" 
                                {% if user.userprofile.role == role_code %}selected{% endif %}>
                            {{ role_name }}
                        </option>
                        {% endfor %}
                    </select>
                    
                    <button type="button" class="btn btn-sm btn-outline-info" 
                            onclick="viewUserPermissions({{ user.id }})">
                        <i class="fas fa-eye"></i>
                    </button>
                </div>
            </div>
            {% empty %}
            <div class="text-center py-5">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <p class="text-muted">暂无用户数据</p>
            </div>
            {% endfor %}
        </div>

        <!-- 分页 -->
        {% if users.has_other_pages %}
        <nav aria-label="用户分页" class="mt-4">
            <ul class="pagination justify-content-center">
                {% if users.has_previous %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ users.previous_page_number }}">上一页</a>
                </li>
                {% endif %}
                
                {% for num in users.paginator.page_range %}
                {% if users.number == num %}
                <li class="page-item active">
                    <span class="page-link">{{ num }}</span>
                </li>
                {% elif num > users.number|add:'-3' and num < users.number|add:'3' %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                </li>
                {% endif %}
                {% endfor %}
                
                {% if users.has_next %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ users.next_page_number }}">下一页</a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
    </div>
</div>

<!-- 用户权限预览模态框 -->
<div class="modal fade" id="userPermissionsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">用户权限详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="userPermissionsContent">
                <div class="text-center">
                    <div class="loading-spinner"></div>
                    <p class="mt-2">加载中...</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let selectedUsers = new Set();

// 搜索用户
function searchUsers() {
    const query = document.getElementById('searchInput').value.toLowerCase();
    const userCards = document.querySelectorAll('.user-card');
    
    userCards.forEach(card => {
        const username = card.dataset.username;
        const email = card.dataset.email;
        
        if (username.includes(query) || email.includes(query)) {
            card.style.display = '';
        } else {
            card.style.display = 'none';
        }
    });
}

// 按角色筛选
function filterByRole() {
    const role = document.getElementById('roleFilter').value;
    const userCards = document.querySelectorAll('.user-card');
    
    userCards.forEach(card => {
        if (!role || card.dataset.role === role) {
            card.style.display = '';
        } else {
            card.style.display = 'none';
        }
    });
}

// 按状态筛选
function filterByStatus() {
    const status = document.getElementById('statusFilter').value;
    const userCards = document.querySelectorAll('.user-card');
    
    userCards.forEach(card => {
        if (!status || card.dataset.status === status) {
            card.style.display = '';
        } else {
            card.style.display = 'none';
        }
    });
}

// 清除筛选
function clearFilters() {
    document.getElementById('searchInput').value = '';
    document.getElementById('roleFilter').value = '';
    document.getElementById('statusFilter').value = '';
    
    const userCards = document.querySelectorAll('.user-card');
    userCards.forEach(card => {
        card.style.display = '';
    });
}

// 修改用户角色
function changeUserRole(select) {
    const userId = select.dataset.userId;
    const newRole = select.value;
    const originalValue = select.querySelector('option[selected]').value;
    
    // 显示加载状态
    select.disabled = true;
    
    fetch('{% url "users:ajax_change_user_role" %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCookie('csrftoken')
        },
        body: JSON.stringify({
            user_id: userId,
            role: newRole
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 更新角色徽章
            const card = select.closest('.user-card');
            const badge = card.querySelector('.role-badge');
            badge.className = `role-badge role-${newRole}`;
            badge.textContent = select.options[select.selectedIndex].text;
            
            // 更新数据属性
            card.dataset.role = newRole;
            
            showMessage(data.message, 'success');
        } else {
            // 恢复原值
            select.value = originalValue;
            showMessage(data.error, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        select.value = originalValue;
        showMessage('角色修改失败，请重试', 'error');
    })
    .finally(() => {
        select.disabled = false;
    });
}

// 查看用户权限
function viewUserPermissions(userId) {
    const modal = new bootstrap.Modal(document.getElementById('userPermissionsModal'));
    const content = document.getElementById('userPermissionsContent');
    
    // 显示加载状态
    content.innerHTML = `
        <div class="text-center">
            <div class="loading-spinner"></div>
            <p class="mt-2">加载中...</p>
        </div>
    `;
    
    modal.show();
    
    fetch(`{% url "users:ajax_get_user_permissions" %}?user_id=${userId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayUserPermissions(data);
            } else {
                content.innerHTML = `<div class="alert alert-danger">${data.error}</div>`;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            content.innerHTML = '<div class="alert alert-danger">加载失败，请重试</div>';
        });
}

// 显示用户权限
function displayUserPermissions(data) {
    const content = document.getElementById('userPermissionsContent');
    const user = data.user;
    
    let html = `
        <div class="row mb-3">
            <div class="col-md-6">
                <h6>用户信息</h6>
                <p><strong>用户名:</strong> ${user.username}</p>
                <p><strong>邮箱:</strong> ${user.email}</p>
                <p><strong>角色:</strong> <span class="role-badge role-${user.role}">${user.role_display}</span></p>
            </div>
            <div class="col-md-6">
                <h6>状态信息</h6>
                <p><strong>状态:</strong> ${user.is_active ? '活跃' : '非活跃'}</p>
                <p><strong>最后登录:</strong> ${user.last_login ? new Date(user.last_login).toLocaleString() : '从未登录'}</p>
            </div>
        </div>
        
        <h6>角色权限</h6>
        <div class="row">
    `;
    
    // 显示角色权限
    for (const [category, permissions] of Object.entries(data.role_permissions || {})) {
        html += `
            <div class="col-md-6 mb-3">
                <h6 class="text-primary">${category}</h6>
                <ul class="list-unstyled">
        `;
        
        permissions.forEach(perm => {
            html += `<li><i class="fas fa-check text-success"></i> ${perm.description || perm.name}</li>`;
        });
        
        html += `
                </ul>
            </div>
        `;
    }
    
    html += '</div>';
    
    // 显示自定义权限
    if (data.custom_permissions && data.custom_permissions.length > 0) {
        html += `
            <h6 class="mt-3">自定义权限</h6>
            <div class="table-responsive">
                <table class="table table-sm">
                    <thead>
                        <tr>
                            <th>权限键</th>
                            <th>状态</th>
                            <th>优先级</th>
                            <th>原因</th>
                        </tr>
                    </thead>
                    <tbody>
        `;
        
        data.custom_permissions.forEach(perm => {
            html += `
                <tr>
                    <td><code>${perm.permission_key}</code></td>
                    <td>
                        <span class="badge ${perm.is_granted ? 'bg-success' : 'bg-danger'}">
                            ${perm.is_granted ? '已授权' : '已拒绝'}
                        </span>
                    </td>
                    <td>${perm.priority}</td>
                    <td>${perm.grant_reason || '-'}</td>
                </tr>
            `;
        });
        
        html += `
                    </tbody>
                </table>
            </div>
        `;
    }
    
    content.innerHTML = html;
}

// 更新选择状态
function updateSelection() {
    const checkboxes = document.querySelectorAll('.user-checkbox:checked');
    selectedUsers.clear();
    
    checkboxes.forEach(cb => {
        selectedUsers.add(cb.value);
    });
    
    const bulkActions = document.getElementById('bulkActions');
    const selectedCount = document.getElementById('selectedCount');
    
    if (selectedUsers.size > 0) {
        bulkActions.classList.add('show');
        selectedCount.textContent = selectedUsers.size;
    } else {
        bulkActions.classList.remove('show');
    }
}

// 清除选择
function clearSelection() {
    const checkboxes = document.querySelectorAll('.user-checkbox');
    checkboxes.forEach(cb => cb.checked = false);
    updateSelection();
}

// 批量修改角色
function bulkChangeRole() {
    if (selectedUsers.size === 0) return;
    
    const newRole = prompt('请输入新角色代码 (super_admin, warehouse_manager, warehouse_operator, viewer):');
    if (!newRole) return;
    
    const validRoles = ['super_admin', 'warehouse_manager', 'warehouse_operator', 'viewer'];
    if (!validRoles.includes(newRole)) {
        showMessage('无效的角色代码', 'error');
        return;
    }
    
    const promises = Array.from(selectedUsers).map(userId => {
        return fetch('{% url "users:ajax_change_user_role" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            body: JSON.stringify({
                user_id: userId,
                role: newRole
            })
        }).then(response => response.json());
    });
    
    Promise.all(promises)
        .then(results => {
            const successes = results.filter(r => r.success).length;
            const failures = results.length - successes;
            
            if (failures === 0) {
                showMessage(`成功修改 ${successes} 个用户的角色`, 'success');
                location.reload(); // 刷新页面
            } else {
                showMessage(`修改完成：成功 ${successes} 个，失败 ${failures} 个`, 'warning');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showMessage('批量修改失败', 'error');
        });
}

// 导出用户
function exportUsers() {
    const users = Array.from(document.querySelectorAll('.user-card')).map(card => {
        const username = card.querySelector('.user-name').textContent;
        const email = card.querySelector('.user-email').textContent;
        const role = card.dataset.role;
        const status = card.dataset.status;
        
        return { username, email, role, status };
    });
    
    const csv = [
        ['用户名', '邮箱', '角色', '状态'],
        ...users.map(u => [u.username, u.email, u.role, u.status])
    ].map(row => row.join(',')).join('\n');
    
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = 'users.csv';
    link.click();
}

// 显示消息
function showMessage(message, type) {
    const alertClass = {
        'success': 'alert-success',
        'error': 'alert-danger',
        'warning': 'alert-warning',
        'info': 'alert-info'
    }[type] || 'alert-info';
    
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert ${alertClass} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.querySelector('.container-fluid').insertBefore(alertDiv, document.querySelector('.stats-bar'));
    
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}

// 获取CSRF Token
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}
</script>
{% endblock %}
