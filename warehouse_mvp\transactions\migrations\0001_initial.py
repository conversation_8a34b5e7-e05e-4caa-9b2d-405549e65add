# Generated by Django 4.1.2 on 2025-07-29 09:44

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("products", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Transaction",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "transaction_type",
                    models.CharField(
                        choices=[("IN", "入库"), ("OUT", "出库")],
                        max_length=3,
                        verbose_name="交易类型",
                    ),
                ),
                ("quantity", models.PositiveIntegerField(verbose_name="数量")),
                ("reason", models.CharField(max_length=200, verbose_name="原因")),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                ("notes", models.TextField(blank=True, verbose_name="备注")),
                (
                    "operator",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="操作员",
                    ),
                ),
                (
                    "product",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="products.product",
                        verbose_name="商品",
                    ),
                ),
            ],
            options={
                "verbose_name": "进出库记录",
                "verbose_name_plural": "进出库记录",
                "ordering": ["-created_at"],
            },
        ),
    ]
