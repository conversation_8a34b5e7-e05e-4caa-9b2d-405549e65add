from django.shortcuts import render, get_object_or_404
from django.views.generic import ListView, UpdateView, DeleteView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib import messages
from django.urls import reverse_lazy
from django.http import JsonResponse
from django.db.models import Q, F
from django.db import models
from django.template.loader import render_to_string
from django.utils import timezone
from django.core.paginator import Paginator
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.contrib.auth.decorators import login_required
import json
from .models import Inventory
from .forms import InventoryForm
from users.decorators import PermissionRequiredMixin
from users.permissions import PermissionManager

# Create your views here.

class InventoryListView(PermissionRequiredMixin, ListView):
    """库存查看视图"""
    model = Inventory
    template_name = 'inventory/list.html'
    context_object_name = 'inventories'
    paginate_by = 15
    permission_app = 'inventory'
    permission_action = 'view'

    def get_queryset(self):
        queryset = Inventory.objects.select_related('product').all()

        # 搜索功能
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(product__name__icontains=search) |
                Q(product__sku__icontains=search)
            )

        # 库存状态筛选
        status = self.request.GET.get('status')
        if status == 'low':
            # 低库存筛选
            queryset = [inv for inv in queryset if inv.is_low_stock]
        elif status == 'normal':
            # 正常库存筛选
            queryset = [inv for inv in queryset if not inv.is_low_stock and inv.quantity < inv.max_stock]
        elif status == 'high':
            # 高库存筛选
            queryset = [inv for inv in queryset if inv.quantity >= inv.max_stock]

        # 排序
        order_by = self.request.GET.get('order_by', '-updated_at')
        if isinstance(queryset, list):
            # 如果是列表（经过状态筛选），需要手动排序
            if order_by == 'quantity':
                queryset.sort(key=lambda x: x.quantity)
            elif order_by == '-quantity':
                queryset.sort(key=lambda x: x.quantity, reverse=True)
            elif order_by == 'product__name':
                queryset.sort(key=lambda x: x.product.name)
            elif order_by == '-product__name':
                queryset.sort(key=lambda x: x.product.name, reverse=True)
        else:
            # 如果是QuerySet，使用order_by
            queryset = queryset.order_by(order_by)

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search'] = self.request.GET.get('search', '')
        context['status'] = self.request.GET.get('status', '')
        context['order_by'] = self.request.GET.get('order_by', '-updated_at')

        # 统计信息
        all_inventories = Inventory.objects.select_related('product').all()
        context['total_products'] = all_inventories.count()
        context['low_stock_count'] = sum(1 for inv in all_inventories if inv.is_low_stock)
        context['total_value'] = sum(inv.quantity * inv.product.price for inv in all_inventories)

        return context


@login_required
@require_http_methods(["GET"])
def get_chart_data(request):
    """获取图表数据API"""
    try:
        # 获取所有有效的库存记录（只包含存在的商品）
        inventories = Inventory.objects.select_related('product').filter(
            product__isnull=False
        ).all()

        # 库存状态统计
        status_stats = {
            'normal': 0,    # 正常库存
            'low': 0,       # 低库存
            'high': 0,      # 高库存
            'out': 0        # 缺货
        }

        # 商品分类价值统计（简化版，按价格区间分类）
        category_stats = {
            '高价值商品(>1000)': 0,
            '中价值商品(100-1000)': 0,
            '低价值商品(<100)': 0
        }

        for inventory in inventories:
            # 统计库存状态
            if inventory.quantity == 0:
                status_stats['out'] += 1
            elif inventory.is_low_stock:
                status_stats['low'] += 1
            elif inventory.quantity >= inventory.max_stock:
                status_stats['high'] += 1
            else:
                status_stats['normal'] += 1

            # 统计商品价值分类
            product_value = float(inventory.product.price) * inventory.quantity
            if inventory.product.price >= 1000:
                category_stats['高价值商品(>1000)'] += product_value / 10000  # 转换为万元
            elif inventory.product.price >= 100:
                category_stats['中价值商品(100-1000)'] += product_value / 10000
            else:
                category_stats['低价值商品(<100)'] += product_value / 10000

        # 构造图表数据
        chart_data = {
            'stock_status': {
                'labels': ['正常库存', '低库存', '高库存', '缺货'],
                'data': [
                    status_stats['normal'],
                    status_stats['low'],
                    status_stats['high'],
                    status_stats['out']
                ]
            },
            'stock_value': {
                'labels': list(category_stats.keys()),
                'data': [round(value, 2) for value in category_stats.values()]
            }
        }

        return JsonResponse({
            'success': True,
            'data': chart_data,
            'timestamp': timezone.now().isoformat()
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': f'获取图表数据失败：{str(e)}'
        })


class InventoryUpdateView(PermissionRequiredMixin, UpdateView):
    """库存编辑视图"""
    model = Inventory
    form_class = InventoryForm
    template_name = 'inventory/edit.html'
    success_url = reverse_lazy('inventory:list')
    permission_app = 'inventory'
    permission_action = 'change'

    def get_object(self):
        """获取要编辑的库存对象"""
        return get_object_or_404(Inventory, pk=self.kwargs['pk'])

    def form_valid(self, form):
        """表单验证成功后的处理"""
        response = super().form_valid(form)

        # 添加成功消息
        messages.success(
            self.request,
            f'库存设置已更新：{self.object.product.name}'
        )

        # 如果是AJAX请求，返回JSON响应
        if self.request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({
                'success': True,
                'message': f'库存设置已更新：{self.object.product.name}',
                'redirect_url': str(self.success_url)
            })

        return response

    def form_invalid(self, form):
        """表单验证失败的处理"""
        # 如果是AJAX请求，返回JSON响应
        if self.request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({
                'success': False,
                'errors': form.errors,
                'form_html': self.render_to_string_form(form)
            })

        return super().form_invalid(form)

    def get(self, request, *args, **kwargs):
        """处理GET请求"""
        self.object = self.get_object()

        # 如果是AJAX请求，只返回表单HTML
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            form = self.get_form()
            return JsonResponse({
                'success': True,
                'form_html': self.render_to_string_form(form)
            })

        return super().get(request, *args, **kwargs)

    def render_to_string_form(self, form):
        """渲染表单为HTML字符串"""
        from django.template.loader import render_to_string
        return render_to_string('inventory/edit_form.html', {
            'form': form,
            'inventory': self.object
        }, request=self.request)


@login_required
@require_http_methods(["POST"])
@csrf_exempt
def ajax_update_inventory_quantity(request):
    """AJAX: 更新库存数量"""
    # 检查权限
    if not PermissionManager.user_has_permission(request.user, 'inventory', 'change'):
        return JsonResponse({'success': False, 'error': '权限不足'}, status=403)

    try:
        data = json.loads(request.body)
        inventory_id = data.get('inventory_id')
        new_quantity = data.get('quantity')

        if not inventory_id or new_quantity is None:
            return JsonResponse({'success': False, 'error': '参数不完整'})

        # 验证数量
        try:
            new_quantity = int(new_quantity)
            if new_quantity < 0:
                return JsonResponse({'success': False, 'error': '库存数量不能为负数'})
        except (ValueError, TypeError):
            return JsonResponse({'success': False, 'error': '库存数量必须是有效数字'})

        # 获取库存记录
        inventory = get_object_or_404(Inventory, id=inventory_id)
        old_quantity = inventory.quantity

        # 更新库存数量
        inventory.quantity = new_quantity
        inventory.updated_by = request.user
        inventory.save()

        # 计算库存状态
        is_low_stock = new_quantity <= inventory.min_stock
        is_high_stock = new_quantity >= inventory.max_stock

        if is_low_stock:
            status_html = '<span class="badge bg-danger"><i class="bi bi-exclamation-triangle"></i> 低库存</span>'
            status_class = 'text-danger'
        elif is_high_stock:
            status_html = '<span class="badge bg-info"><i class="bi bi-check-circle"></i> 库存充足</span>'
            status_class = 'text-success'
        else:
            status_html = '<span class="badge bg-success"><i class="bi bi-check"></i> 正常</span>'
            status_class = 'text-success'

        # 计算库存价值
        total_value = new_quantity * inventory.product.price

        # 记录库存变更日志（如果需要的话）
        from users.models import AuditLog
        AuditLog.objects.create(
            user=request.user,
            action='INVENTORY_UPDATED',
            content_type_id=1,
            object_id=inventory.id,
            object_repr=f"库存更新: {inventory.product.name}",
            changes={
                'product': inventory.product.name,
                'old_quantity': old_quantity,
                'new_quantity': new_quantity,
                'difference': new_quantity - old_quantity
            },
            ip_address=request.META.get('REMOTE_ADDR', ''),
            user_agent=request.META.get('HTTP_USER_AGENT', ''),
            extra_data={'operation': 'quantity_update'}
        )

        return JsonResponse({
            'success': True,
            'message': f'库存已更新：{old_quantity} → {new_quantity}',
            'data': {
                'inventory_id': inventory_id,
                'old_quantity': old_quantity,
                'new_quantity': new_quantity,
                'status_html': status_html,
                'status_class': status_class,
                'total_value': f"¥{total_value:.2f}",
                'is_low_stock': is_low_stock,
                'is_high_stock': is_high_stock,
                'updated_at': inventory.updated_at.strftime('%m-%d %H:%M'),
                'updated_by': request.user.username
            }
        })

    except json.JSONDecodeError:
        return JsonResponse({'success': False, 'error': 'JSON格式错误'})
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


@login_required
@require_http_methods(["GET"])
def ajax_search(request):
    """AJAX搜索库存"""
    try:
        # 获取搜索参数
        search_query = request.GET.get('search', '').strip()
        status_filter = request.GET.get('status', '')
        order_by = request.GET.get('order_by', '-updated_at')
        page = int(request.GET.get('page', 1))

        # 构建查询
        queryset = Inventory.objects.select_related('product', 'updated_by')

        # 搜索过滤
        if search_query:
            queryset = queryset.filter(
                Q(product__name__icontains=search_query) |
                Q(product__sku__icontains=search_query) |
                Q(product__description__icontains=search_query)
            )

        # 状态过滤
        if status_filter:
            if status_filter == 'low':
                # 低库存：数量小于等于最小库存阈值
                queryset = queryset.filter(quantity__lte=F('min_stock'))
            elif status_filter == 'normal':
                # 正常库存：数量大于最小库存且小于最大库存
                queryset = queryset.filter(
                    quantity__gt=F('min_stock'),
                    quantity__lt=F('max_stock')
                )
            elif status_filter == 'high':
                # 高库存：数量大于等于最大库存
                queryset = queryset.filter(quantity__gte=F('max_stock'))

        # 排序
        queryset = queryset.order_by(order_by)

        # 分页
        paginator = Paginator(queryset, 10)  # 每页10条
        page_obj = paginator.get_page(page)

        # 渲染表格内容
        table_html = render_to_string('inventory/partials/inventory_table.html', {
            'inventories': page_obj,
            'page_obj': page_obj,
            'request': request,
            'perms': request.user.get_all_permissions() if request.user.is_authenticated else {},
        })

        # 渲染完整的表格（包括colgroup和thead）
        full_table_html = render_to_string('inventory/partials/inventory_full_table.html', {
            'inventories': page_obj,
            'page_obj': page_obj,
            'request': request,
            'perms': request.user.get_all_permissions() if request.user.is_authenticated else {},
        })

        # 渲染分页
        pagination_html = render_to_string('inventory/partials/pagination.html', {
            'page_obj': page_obj,
            'request': request,
        })

        return JsonResponse({
            'success': True,
            'table_html': table_html,
            'full_table_html': full_table_html,
            'pagination_html': pagination_html,
            'total_count': paginator.count,
            'current_page': page_obj.number,
            'total_pages': paginator.num_pages,
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': f'搜索失败：{str(e)}'
        })


class InventoryDeleteView(PermissionRequiredMixin, DeleteView):
    """库存删除视图"""
    model = Inventory
    permission_app = 'inventory'
    permission_action = 'delete'
    success_url = reverse_lazy('inventory:list')

    def delete(self, request, *args, **kwargs):
        """处理删除请求"""
        try:
            inventory = self.get_object()
            product_name = inventory.product.name

            # 执行删除
            inventory.delete()

            # 返回JSON响应
            if request.headers.get('Content-Type') == 'application/json':
                return JsonResponse({
                    'success': True,
                    'message': f'库存记录 "{product_name}" 已删除'
                })
            else:
                messages.success(request, f'库存记录 "{product_name}" 已删除')
                return super().delete(request, *args, **kwargs)

        except Exception as e:
            if request.headers.get('Content-Type') == 'application/json':
                return JsonResponse({
                    'success': False,
                    'error': f'删除失败：{str(e)}'
                })
            else:
                messages.error(request, f'删除失败：{str(e)}')
                return JsonResponse({'success': False, 'error': str(e)}, status=500)

