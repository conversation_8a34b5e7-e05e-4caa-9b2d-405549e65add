{% extends 'products/base.html' %}

{% block title %}新增进出库记录 - 仓库管理系统{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="bi bi-plus-circle"></i> 新增进出库记录
                </h4>
            </div>
            <div class="card-body">
                <form method="post" id="transactionForm">
                    {% csrf_token %}
                    
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {{ form.non_field_errors }}
                        </div>
                    {% endif %}

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.product.id_for_label }}" class="form-label">
                                    {{ form.product.label }} <span class="text-danger">*</span>
                                </label>
                                {{ form.product }}
                                {% if form.product.errors %}
                                    <div class="text-danger small">{{ form.product.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.transaction_type.id_for_label }}" class="form-label">
                                    {{ form.transaction_type.label }} <span class="text-danger">*</span>
                                </label>
                                {{ form.transaction_type }}
                                {% if form.transaction_type.errors %}
                                    <div class="text-danger small">{{ form.transaction_type.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- 库存信息显示区域 -->
                    <div id="stockInfo" class="alert alert-info" style="display: none;">
                        <h6><i class="bi bi-info-circle"></i> 当前库存信息</h6>
                        <div class="row">
                            <div class="col-md-3">
                                <small class="text-muted">当前库存</small>
                                <div class="fw-bold" id="currentStock">-</div>
                            </div>
                            <div class="col-md-3">
                                <small class="text-muted">最低库存</small>
                                <div id="minStock">-</div>
                            </div>
                            <div class="col-md-3">
                                <small class="text-muted">最高库存</small>
                                <div id="maxStock">-</div>
                            </div>
                            <div class="col-md-3">
                                <small class="text-muted">库存状态</small>
                                <div id="stockStatus">-</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.quantity.id_for_label }}" class="form-label">
                                    {{ form.quantity.label }} <span class="text-danger">*</span>
                                </label>
                                {{ form.quantity }}
                                {% if form.quantity.errors %}
                                    <div class="text-danger small">{{ form.quantity.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.reason.id_for_label }}" class="form-label">
                                    {{ form.reason.label }} <span class="text-danger">*</span>
                                </label>
                                {{ form.reason }}
                                {% if form.reason.errors %}
                                    <div class="text-danger small">{{ form.reason.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.notes.id_for_label }}" class="form-label">
                            {{ form.notes.label }}
                        </label>
                        {{ form.notes }}
                        {% if form.notes.errors %}
                            <div class="text-danger small">{{ form.notes.errors.0 }}</div>
                        {% endif %}
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{% url 'transactions:list' %}" class="btn btn-secondary">
                            <i class="bi bi-arrow-left"></i> 返回列表
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle"></i> 提交记录
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const productSelect = document.getElementById('id_product');
    const stockInfo = document.getElementById('stockInfo');
    
    // 监听商品选择变化
    productSelect.addEventListener('change', function() {
        const productId = this.value;
        if (productId) {
            // 获取库存信息
            fetch(`{% url 'transactions:get_stock' %}?product_id=${productId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('currentStock').textContent = data.current_stock;
                        document.getElementById('minStock').textContent = data.min_stock;
                        document.getElementById('maxStock').textContent = data.max_stock;
                        document.getElementById('stockStatus').textContent = data.status;
                        
                        // 根据库存状态设置样式
                        const currentStockEl = document.getElementById('currentStock');
                        currentStockEl.className = data.is_low_stock ? 'fw-bold text-danger' : 'fw-bold text-success';
                        
                        stockInfo.style.display = 'block';
                    } else {
                        stockInfo.style.display = 'none';
                        alert('获取库存信息失败：' + data.error);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    stockInfo.style.display = 'none';
                });
        } else {
            stockInfo.style.display = 'none';
        }
    });
});
</script>
{% endblock %}
