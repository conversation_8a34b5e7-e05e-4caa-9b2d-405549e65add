"""
批量交易性能测试
验证批量出入库操作的优化效果
"""
import time
import threading
from django.test import TestCase, TransactionTestCase
from django.contrib.auth.models import User
from django.db import connection
from django.conf import settings
from products.models import Product
from inventory.models import Inventory
from transactions.models import Transaction
from transactions.services import BatchTransactionService
from transactions.views import BatchUploadView
from users.models import AuditLog
from users.signals import set_bulk_operation_context, clear_bulk_operation_context


class BatchTransactionPerformanceTest(TransactionTestCase):
    """批量交易性能测试"""
    
    def setUp(self):
        """设置测试数据"""
        # 创建测试用户
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123',
            email='<EMAIL>'
        )
        
        # 创建测试商品
        self.products = []
        for i in range(500):  # 创建500个商品用于测试
            product = Product.objects.create(
                name=f'测试商品{i+1}',
                sku=f'TEST{i+1:03d}',
                description=f'测试商品{i+1}的描述',
                price=100.00 + i
            )
            self.products.append(product)
            
            # 创建对应的库存记录
            Inventory.objects.create(
                product=product,
                quantity=1000,  # 初始库存1000
                min_stock=10,
                max_stock=2000
            )
    
    def create_test_data(self, count, transaction_type='入库'):
        """创建测试数据"""
        test_data = []
        for i in range(count):
            product = self.products[i % len(self.products)]
            test_data.append({
                'product': product,
                'transaction_type': transaction_type,
                'quantity': 10,
                'reason': f'测试{transaction_type}',
                'notes': f'性能测试数据{i+1}',
                'sku': product.sku
            })
        return test_data
    
    def count_database_queries(self, func, *args, **kwargs):
        """统计数据库查询次数"""
        initial_queries = len(connection.queries)
        result = func(*args, **kwargs)
        final_queries = len(connection.queries)
        query_count = final_queries - initial_queries
        return result, query_count
    
    def test_performance_comparison_small_batch(self):
        """测试小批量操作性能对比（10个商品）"""
        print("\n=== 小批量操作性能测试（10个商品） ===")
        self._run_performance_test(10)
    
    def test_performance_comparison_medium_batch(self):
        """测试中等批量操作性能对比（50个商品）"""
        print("\n=== 中等批量操作性能测试（50个商品） ===")
        self._run_performance_test(50)
    
    def test_performance_comparison_large_batch(self):
        """测试大批量操作性能对比（100个商品）"""
        print("\n=== 大批量操作性能测试（100个商品） ===")
        self._run_performance_test(100)
    
    def test_performance_comparison_extra_large_batch(self):
        """测试超大批量操作性能对比（500个商品）"""
        print("\n=== 超大批量操作性能测试（500个商品） ===")
        self._run_performance_test(500)
    
    def _run_performance_test(self, item_count):
        """运行性能测试"""
        test_data = self.create_test_data(item_count)
        
        # 测试原版本性能
        legacy_result = self._test_legacy_performance(test_data)
        
        # 清理数据
        self._cleanup_test_data()
        
        # 测试优化版本性能
        optimized_result = self._test_optimized_performance(test_data)
        
        # 对比和验证结果
        self._compare_and_validate_results(legacy_result, optimized_result, item_count)
    
    def _test_legacy_performance(self, test_data):
        """测试原版本性能"""
        print(f"测试原版本性能...")
        
        # 记录初始状态
        initial_transactions = Transaction.objects.count()
        initial_audit_logs = AuditLog.objects.count()
        
        # 执行性能测试
        view = BatchUploadView()
        start_time = time.time()
        result, query_count = self.count_database_queries(
            view.create_transactions_batch_legacy, test_data, self.user
        )
        end_time = time.time()
        
        # 统计结果
        final_transactions = Transaction.objects.count()
        final_audit_logs = AuditLog.objects.count()
        
        return {
            'version': 'legacy',
            'processing_time': end_time - start_time,
            'query_count': query_count,
            'created_count': result['created_count'],
            'skipped_count': result['skipped_count'],
            'transactions_created': final_transactions - initial_transactions,
            'audit_logs_created': final_audit_logs - initial_audit_logs
        }
    
    def _test_optimized_performance(self, test_data):
        """测试优化版本性能"""
        print(f"测试优化版本性能...")
        
        # 记录初始状态
        initial_transactions = Transaction.objects.count()
        initial_audit_logs = AuditLog.objects.count()
        
        # 执行性能测试
        start_time = time.time()
        result, query_count = self.count_database_queries(
            BatchTransactionService.create_batch_optimized, test_data, self.user
        )
        end_time = time.time()
        
        # 统计结果
        final_transactions = Transaction.objects.count()
        final_audit_logs = AuditLog.objects.count()
        
        return {
            'version': 'optimized',
            'processing_time': end_time - start_time,
            'query_count': query_count,
            'created_count': result['created_count'],
            'skipped_count': result['skipped_count'],
            'transactions_created': final_transactions - initial_transactions,
            'audit_logs_created': final_audit_logs - initial_audit_logs
        }
    
    def _compare_and_validate_results(self, legacy_result, optimized_result, item_count):
        """对比和验证结果"""
        print(f"\n性能对比结果：")
        print(f"商品数量: {item_count}")
        print(f"原版本处理时间: {legacy_result['processing_time']:.3f}秒")
        print(f"优化版本处理时间: {optimized_result['processing_time']:.3f}秒")
        print(f"时间改善: {((legacy_result['processing_time'] - optimized_result['processing_time']) / legacy_result['processing_time'] * 100):.1f}%")
        
        print(f"\n数据库查询对比：")
        print(f"原版本查询次数: {legacy_result['query_count']}")
        print(f"优化版本查询次数: {optimized_result['query_count']}")
        print(f"查询减少: {((legacy_result['query_count'] - optimized_result['query_count']) / legacy_result['query_count'] * 100):.1f}%")
        
        print(f"\n审计日志对比：")
        print(f"原版本审计日志: {legacy_result['audit_logs_created']}")
        print(f"优化版本审计日志: {optimized_result['audit_logs_created']}")
        print(f"日志减少: {((legacy_result['audit_logs_created'] - optimized_result['audit_logs_created']) / legacy_result['audit_logs_created'] * 100):.1f}%")
        
        # 验证数据一致性
        self.assertEqual(legacy_result['created_count'], optimized_result['created_count'], 
                        "创建的交易记录数量应该一致")
        self.assertEqual(legacy_result['transactions_created'], optimized_result['transactions_created'], 
                        "实际创建的交易记录数量应该一致")
        
        # 验证性能提升
        self.assertLess(optimized_result['processing_time'], legacy_result['processing_time'], 
                       "优化版本应该更快")
        self.assertLess(optimized_result['query_count'], legacy_result['query_count'], 
                       "优化版本应该使用更少的数据库查询")
        self.assertLess(optimized_result['audit_logs_created'], legacy_result['audit_logs_created'], 
                       "优化版本应该创建更少的审计日志")
        
        # 验证预期的性能提升目标
        query_reduction_percent = (legacy_result['query_count'] - optimized_result['query_count']) / legacy_result['query_count'] * 100
        audit_reduction_percent = (legacy_result['audit_logs_created'] - optimized_result['audit_logs_created']) / legacy_result['audit_logs_created'] * 100
        
        self.assertGreater(query_reduction_percent, 80, 
                          f"数据库查询减少应该超过80%，实际减少{query_reduction_percent:.1f}%")
        self.assertGreater(audit_reduction_percent, 80, 
                          f"审计日志减少应该超过80%，实际减少{audit_reduction_percent:.1f}%")
    
    def _cleanup_test_data(self):
        """清理测试数据"""
        Transaction.objects.all().delete()
        AuditLog.objects.all().delete()
        # 重置库存
        for product in self.products:
            try:
                inventory = Inventory.objects.get(product=product)
                inventory.quantity = 1000
                inventory.save()
            except Inventory.DoesNotExist:
                pass
    
    def test_data_consistency(self):
        """测试数据一致性"""
        print("\n=== 数据一致性测试 ===")
        
        test_data = self.create_test_data(20)
        
        # 使用优化版本处理
        result = BatchTransactionService.create_batch_optimized(test_data, self.user)
        
        # 验证Transaction记录
        transactions = Transaction.objects.filter(operator=self.user)
        self.assertEqual(len(transactions), result['created_count'])
        
        # 验证库存更新
        for transaction_obj in transactions:
            inventory = Inventory.objects.get(product=transaction_obj.product)
            if transaction_obj.transaction_type == 'IN':
                expected_quantity = 1000 + transaction_obj.quantity
            else:
                expected_quantity = 1000 - transaction_obj.quantity
            self.assertEqual(inventory.quantity, expected_quantity)
        
        # 验证审计日志
        batch_audit_logs = AuditLog.objects.filter(action='BATCH_TRANSACTION')
        self.assertEqual(len(batch_audit_logs), 1)
        
        print("数据一致性验证通过")
    
    def test_concurrent_operations(self):
        """测试并发操作安全性"""
        print("\n=== 并发操作安全性测试 ===")
        
        def worker(worker_id):
            test_data = self.create_test_data(10, f'入库-Worker{worker_id}')
            try:
                result = BatchTransactionService.create_batch_optimized(test_data, self.user)
                return result['created_count']
            except Exception as e:
                print(f"Worker {worker_id} 失败: {str(e)}")
                return 0
        
        # 创建多个线程并发执行
        threads = []
        results = []
        
        for i in range(3):
            thread = threading.Thread(target=lambda i=i: results.append(worker(i)))
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 验证结果
        total_created = sum(results)
        self.assertGreater(total_created, 0, "并发操作应该成功创建记录")
        
        print(f"并发操作测试完成，总共创建 {total_created} 条记录")
