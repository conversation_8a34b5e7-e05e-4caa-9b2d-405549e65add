{% extends 'products/base.html' %}
{% load static %}

{% block title %}操作审计日志 - 仓库管理系统{% endblock %}

{% block extra_css %}
<style>
/* 审计日志表格特定样式 */
#audit-log-table {
    table-layout: fixed !important;
    width: 100% !important;
    min-width: 900px !important;
}

#audit-log-table th,
#audit-log-table td {
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
    padding: 8px !important;
    vertical-align: middle !important;
}

.action-badge {
    font-size: 0.75rem;
    padding: 3px 6px;
    white-space: nowrap;
}

.changes-detail {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: pointer;
}

.changes-detail:hover {
    white-space: normal;
    word-wrap: break-word;
    background-color: #f8f9fa;
    padding: 4px;
    border-radius: 4px;
}

.filter-form {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
}

/* 响应式处理 */
@media (max-width: 768px) {
    #audit-log-table {
        min-width: 700px !important;
    }

    #audit-log-table th,
    #audit-log-table td {
        padding: 6px 4px !important;
        font-size: 0.8rem !important;
    }

    .action-badge {
        font-size: 0.65rem;
        padding: 2px 4px;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="bi bi-journal-text text-primary"></i>
                        操作审计日志
                    </h2>
                    <p class="text-muted mb-0">查看系统操作记录和审计信息</p>
                </div>
                <div>
                    <a href="{% url 'users:role_management' %}" class="btn btn-outline-primary">
                        <i class="bi bi-shield-check"></i> 权限管理
                    </a>
                    <a href="{% url 'users:management' %}" class="btn btn-outline-primary">
                        <i class="bi bi-people"></i> 用户管理
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 统计信息 -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="stats-card">
                <h4 class="mb-1">{{ stats.total_logs }}</h4>
                <small>总日志数</small>
            </div>
        </div>
        <div class="col-md-4">
            <div class="stats-card">
                <h4 class="mb-1">{{ stats.today_logs }}</h4>
                <small>今日操作</small>
            </div>
        </div>
        <div class="col-md-4">
            <div class="stats-card">
                <h4 class="mb-1">{{ stats.week_logs }}</h4>
                <small>本周操作</small>
            </div>
        </div>
    </div>

    <!-- 过滤表单 -->
    <div class="row mb-4">
        <div class="col-12">
            <form method="get" class="filter-form">
                <div class="row">
                    <div class="col-md-3">
                        <label class="form-label">搜索</label>
                        <input type="text" name="search" class="form-control" 
                               value="{{ search }}" placeholder="用户名、对象、操作...">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">操作类型</label>
                        <select name="action" class="form-select">
                            <option value="">全部</option>
                            {% for action_code, action_name in action_choices %}
                                <option value="{{ action_code }}" 
                                        {% if selected_action == action_code %}selected{% endif %}>
                                    {{ action_name }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">操作用户</label>
                        <select name="user" class="form-select">
                            <option value="">全部用户</option>
                            {% for user in users %}
                                <option value="{{ user.id }}" 
                                        {% if selected_user == user.id|stringformat:"s" %}selected{% endif %}>
                                    {{ user.username }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">开始日期</label>
                        <input type="date" name="date_from" class="form-control" value="{{ date_from }}">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">结束日期</label>
                        <input type="date" name="date_to" class="form-control" value="{{ date_to }}">
                    </div>
                    <div class="col-md-1 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="bi bi-search"></i>
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- 审计日志表格 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-list-ul"></i> 操作记录
                        <span class="badge bg-secondary ms-2">{{ page_obj.paginator.count }} 条记录</span>
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="unified-table-container">
                        <table class="unified-table table-theme-primary audit-log-table mb-0" id="audit-log-table">
                            <colgroup>
                                <col style="width: 12%;"><!-- 时间 -->
                                <col style="width: 10%;"><!-- 用户 -->
                                <col style="width: 8%;"><!-- 操作 -->
                                <col style="width: 10%;"><!-- 对象类型 -->
                                <col style="width: 15%;"><!-- 对象 -->
                                <col style="width: 35%;"><!-- 变更详情 -->
                                <col style="width: 10%;"><!-- IP地址 -->
                            </colgroup>
                            <thead>
                                <tr>
                                    <th>时间</th>
                                    <th>用户</th>
                                    <th>操作</th>
                                    <th>对象类型</th>
                                    <th>对象</th>
                                    <th>变更详情</th>
                                    <th>IP地址</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for log in audit_logs %}
                                <tr>
                                    <td>
                                        <small>{{ log.timestamp|date:"m-d H:i:s" }}</small>
                                    </td>
                                    <td>
                                        {% if log.user %}
                                            <strong>{{ log.user.username }}</strong>
                                        {% else %}
                                            <span class="text-muted">系统</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge action-badge
                                            {% if log.action == 'CREATE' %}bg-success
                                            {% elif log.action == 'UPDATE' %}bg-warning
                                            {% elif log.action == 'DELETE' %}bg-danger
                                            {% elif log.action == 'LOGIN' %}bg-info
                                            {% elif log.action == 'LOGOUT' %}bg-secondary
                                            {% else %}bg-primary{% endif %}">
                                            {{ log.get_action_display }}
                                        </span>
                                    </td>
                                    <td>
                                        <small>{{ log.content_type.model|title }}</small>
                                    </td>
                                    <td>
                                        <span title="{{ log.object_repr }}" class="text-truncate d-inline-block" style="max-width: 100%;">
                                            {{ log.object_repr|truncatechars:25 }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="changes-detail" title="{{ log.get_changes_display }}">
                                            {% if log.changes %}
                                                <span>{{ log.get_changes_display|truncatechars:80 }}</span>
                                            {% else %}
                                                <span class="text-muted">-</span>
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td>
                                        <small>{{ log.ip_address|default:"-" }}</small>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="7" class="text-center text-muted py-4">
                                        暂无审计日志记录
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 分页 -->
    {% if is_paginated %}
    <div class="row mt-4">
        <div class="col-12">
            <nav aria-label="审计日志分页">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.action %}&action={{ request.GET.action }}{% endif %}{% if request.GET.user %}&user={{ request.GET.user }}{% endif %}{% if request.GET.date_from %}&date_from={{ request.GET.date_from }}{% endif %}{% if request.GET.date_to %}&date_to={{ request.GET.date_to }}{% endif %}">首页</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.action %}&action={{ request.GET.action }}{% endif %}{% if request.GET.user %}&user={{ request.GET.user }}{% endif %}{% if request.GET.date_from %}&date_from={{ request.GET.date_from }}{% endif %}{% if request.GET.date_to %}&date_to={{ request.GET.date_to }}{% endif %}">上一页</a>
                        </li>
                    {% endif %}

                    <li class="page-item active">
                        <span class="page-link">
                            第 {{ page_obj.number }} 页，共 {{ page_obj.paginator.num_pages }} 页
                        </span>
                    </li>

                    {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.action %}&action={{ request.GET.action }}{% endif %}{% if request.GET.user %}&user={{ request.GET.user }}{% endif %}{% if request.GET.date_from %}&date_from={{ request.GET.date_from }}{% endif %}{% if request.GET.date_to %}&date_to={{ request.GET.date_to }}{% endif %}">下一页</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.action %}&action={{ request.GET.action }}{% endif %}{% if request.GET.user %}&user={{ request.GET.user }}{% endif %}{% if request.GET.date_from %}&date_from={{ request.GET.date_from }}{% endif %}{% if request.GET.date_to %}&date_to={{ request.GET.date_to }}{% endif %}">末页</a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}
