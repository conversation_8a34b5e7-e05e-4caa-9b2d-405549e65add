"""
初始化菜单配置的管理命令
"""
from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from users.models import MenuConfiguration, MenuRolePermission
from users.permissions import UserRoles


class Command(BaseCommand):
    help = '初始化菜单配置'

    def add_arguments(self, parser):
        parser.add_argument(
            '--reset',
            action='store_true',
            help='重置所有菜单配置',
        )

    def handle(self, *args, **options):
        if options['reset']:
            self.stdout.write('正在重置菜单配置...')
            MenuConfiguration.objects.all().delete()

        # 创建主菜单项
        menu_configs = [
            {
                'name': 'products',
                'url_name': 'products:list',
                'icon_class': 'bi bi-box',
                'display_text': '商品管理',
                'permission_key': 'view',
                'app_name': 'products',
                'sort_order': 1,
            },
            {
                'name': 'inventory',
                'url_name': 'inventory:list',
                'icon_class': 'bi bi-clipboard-data',
                'display_text': '库存查看',
                'permission_key': 'view',
                'app_name': 'inventory',
                'sort_order': 2,
            },
            {
                'name': 'transactions',
                'url_name': 'transactions:list',
                'icon_class': 'bi bi-arrow-left-right',
                'display_text': '进出库记录',
                'permission_key': 'view',
                'app_name': 'transactions',
                'sort_order': 3,
            },
            {
                'name': 'users_management',
                'url_name': 'users:management',
                'icon_class': 'bi bi-people',
                'display_text': '用户管理',
                'permission_key': 'view',
                'app_name': 'users',
                'sort_order': 4,
            },
            {
                'name': 'permission_dashboard',
                'url_name': 'users:permission_dashboard',
                'icon_class': 'bi bi-speedometer2',
                'display_text': '权限仪表板',
                'permission_key': 'change',
                'app_name': 'users',
                'sort_order': 5,
            },
            {
                'name': 'audit_log',
                'url_name': 'users:audit_log',
                'icon_class': 'bi bi-journal-text',
                'display_text': '操作日志',
                'permission_key': 'view_audit',
                'app_name': 'users',
                'sort_order': 6,
            },
        ]

        created_menus = []
        for config in menu_configs:
            menu, created = MenuConfiguration.objects.get_or_create(
                name=config['name'],
                defaults=config
            )
            
            if created:
                created_menus.append(menu)
                self.stdout.write(
                    self.style.SUCCESS(f'创建菜单: {menu.display_text}')
                )
            else:
                # 更新现有菜单
                for key, value in config.items():
                    setattr(menu, key, value)
                menu.save()
                self.stdout.write(
                    self.style.WARNING(f'更新菜单: {menu.display_text}')
                )

        # 为不同角色分配菜单权限
        role_menu_permissions = {
            UserRoles.SUPER_ADMIN: [
                'products', 'inventory', 'transactions', 
                'users_management', 'permission_dashboard', 'audit_log'
            ],
            UserRoles.WAREHOUSE_MANAGER: [
                'products', 'inventory', 'transactions', 'users_management'
            ],
            UserRoles.WAREHOUSE_OPERATOR: [
                'products', 'inventory', 'transactions'
            ],
            UserRoles.VIEWER: [
                'products', 'inventory'
            ],
        }

        # 获取系统用户作为授权人
        try:
            system_user = User.objects.filter(is_superuser=True).first()
            if not system_user:
                system_user = User.objects.first()
        except User.DoesNotExist:
            self.stdout.write(
                self.style.ERROR('没有找到系统用户，无法设置菜单权限')
            )
            return

        # 创建角色菜单权限
        for role, menu_names in role_menu_permissions.items():
            for menu_name in menu_names:
                try:
                    menu = MenuConfiguration.objects.get(name=menu_name)
                    permission, created = MenuRolePermission.objects.get_or_create(
                        menu=menu,
                        role=role,
                        defaults={
                            'is_granted': True,
                            'granted_by': system_user
                        }
                    )
                    
                    if created:
                        self.stdout.write(
                            self.style.SUCCESS(
                                f'为角色 {role} 分配菜单权限: {menu.display_text}'
                            )
                        )
                    else:
                        permission.is_granted = True
                        permission.save()
                        self.stdout.write(
                            self.style.WARNING(
                                f'更新角色 {role} 的菜单权限: {menu.display_text}'
                            )
                        )
                        
                except MenuConfiguration.DoesNotExist:
                    self.stdout.write(
                        self.style.ERROR(f'菜单不存在: {menu_name}')
                    )

        self.stdout.write(
            self.style.SUCCESS(
                f'菜单配置初始化完成！共创建/更新 {len(menu_configs)} 个菜单项'
            )
        )

        # 显示统计信息
        total_menus = MenuConfiguration.objects.count()
        active_menus = MenuConfiguration.objects.filter(is_active=True).count()
        total_permissions = MenuRolePermission.objects.count()
        
        self.stdout.write('\n=== 菜单配置统计 ===')
        self.stdout.write(f'总菜单数: {total_menus}')
        self.stdout.write(f'活跃菜单数: {active_menus}')
        self.stdout.write(f'角色权限数: {total_permissions}')
        
        # 显示各角色的菜单权限
        self.stdout.write('\n=== 角色菜单权限 ===')
        for role_code, role_name in UserRoles.CHOICES:
            permissions = MenuRolePermission.objects.filter(
                role=role_code, 
                is_granted=True
            ).count()
            self.stdout.write(f'{role_name}: {permissions} 个菜单权限')
