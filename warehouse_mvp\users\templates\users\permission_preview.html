{% extends "products/base.html" %}
{% load static %}

{% block title %}权限预览 - 仓库管理系统{% endblock %}

{% block extra_css %}
<style>
.preview-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.preview-selector {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    margin-bottom: 1.5rem;
}

.selector-group {
    display: flex;
    gap: 1rem;
    align-items: end;
    flex-wrap: wrap;
}

.selector-item {
    flex: 1;
    min-width: 200px;
}

.permission-category {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    margin-bottom: 1rem;
    overflow: hidden;
}

.category-header {
    background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem;
    font-weight: 600;
    display: flex;
    justify-content: between;
    align-items: center;
}

.category-toggle {
    background: none;
    border: none;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
}

.category-content {
    padding: 1rem;
}

.permission-item {
    display: flex;
    justify-content: between;
    align-items: center;
    padding: 0.75rem;
    border-bottom: 1px solid #f8f9fa;
    transition: background-color 0.2s ease;
}

.permission-item:last-child {
    border-bottom: none;
}

.permission-item:hover {
    background-color: #f8f9fa;
}

.permission-info {
    flex: 1;
}

.permission-name {
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.permission-description {
    font-size: 0.9rem;
    color: #6c757d;
}

.permission-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.status-granted {
    background-color: #e8f5e8;
    color: #2e7d32;
}

.status-denied {
    background-color: #ffebee;
    color: #c62828;
}

.status-inherited {
    background-color: #e3f2fd;
    color: #1565c0;
}

.status-custom {
    background-color: #fff3e0;
    color: #ef6c00;
}

.preview-summary {
    background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.5rem;
    border-radius: 8px;
    margin-bottom: 1.5rem;
}

.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
}

.summary-item {
    text-align: center;
}

.summary-number {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.25rem;
}

.summary-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

.user-info-card {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
}

.user-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 1.5rem;
    margin-right: 1rem;
}

.user-details {
    flex: 1;
}

.user-name {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.user-role {
    margin-bottom: 0.5rem;
}

.user-meta {
    display: flex;
    gap: 1rem;
    font-size: 0.9rem;
    color: #6c757d;
}

.search-box {
    position: relative;
    margin-bottom: 1rem;
}

.search-input {
    padding-left: 2.5rem;
}

.search-icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
}

.empty-state {
    text-align: center;
    padding: 3rem;
    color: #6c757d;
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

@media (max-width: 768px) {
    .selector-group {
        flex-direction: column;
    }
    
    .summary-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .user-info-card {
        flex-direction: column;
        text-align: center;
    }
    
    .user-avatar {
        margin: 0 auto 1rem auto;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">权限预览</h1>
        <div class="btn-group">
            <a href="{% url 'users:permission_dashboard' %}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> 返回仪表板
            </a>
            <button type="button" class="btn btn-primary" onclick="exportPermissions()">
                <i class="fas fa-download"></i> 导出权限
            </button>
        </div>
    </div>

    <!-- 选择器 -->
    <div class="preview-selector">
        <h5 class="mb-3">选择预览对象</h5>
        <div class="selector-group">
            <div class="selector-item">
                <label for="userSelect" class="form-label">选择用户</label>
                <select id="userSelect" class="form-select" onchange="previewUserPermissions()">
                    <option value="">请选择用户...</option>
                    {% for user in users %}
                    <option value="{{ user.id }}" {% if preview_user and preview_user.id == user.id %}selected{% endif %}>
                        {{ user.username }} ({{ user.email }})
                    </option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="selector-item">
                <label class="form-label">或</label>
                <div class="text-center">
                    <span class="text-muted">选择角色预览</span>
                </div>
            </div>
            
            <div class="selector-item">
                <label for="roleSelect" class="form-label">选择角色</label>
                <select id="roleSelect" class="form-select" onchange="previewRolePermissions()">
                    <option value="">请选择角色...</option>
                    {% for role_code, role_name in roles %}
                    <option value="{{ role_code }}" {% if preview_role and preview_role == role_name %}selected{% endif %}>
                        {{ role_name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
        </div>
    </div>

    {% if preview_user %}
    <!-- 用户信息卡片 -->
    <div class="user-info-card d-flex align-items-center">
        <div class="user-avatar">
            {{ preview_user.username|first|upper }}
        </div>
        <div class="user-details">
            <div class="user-name">{{ preview_user.username }}</div>
            <div class="user-role">
                <span class="role-badge role-{{ preview_user.userprofile.role }}">
                    {{ preview_user.userprofile.get_role_display }}
                </span>
            </div>
            <div class="user-meta">
                <span>
                    <i class="fas fa-envelope"></i> {{ preview_user.email }}
                </span>
                <span>
                    <i class="fas fa-circle {% if preview_user.is_active %}text-success{% else %}text-danger{% endif %}"></i>
                    {% if preview_user.is_active %}活跃{% else %}非活跃{% endif %}
                </span>
                {% if preview_user.last_login %}
                <span>
                    <i class="fas fa-clock"></i> {{ preview_user.last_login|timesince }}前登录
                </span>
                {% endif %}
            </div>
        </div>
    </div>
    {% endif %}

    {% if preview_role %}
    <!-- 角色信息卡片 -->
    <div class="user-info-card">
        <div class="text-center">
            <h4>{{ preview_role }} 角色权限预览</h4>
            <p class="text-muted">以下是该角色拥有的所有权限</p>
        </div>
    </div>
    {% endif %}

    {% if user_permissions or role_permissions %}
    <!-- 权限统计 -->
    <div class="preview-summary">
        <div class="summary-grid">
            <div class="summary-item">
                <div class="summary-number" id="totalPermissions">
                    {% if user_permissions %}
                        {{ user_permissions|length }}
                    {% elif role_permissions %}
                        {{ role_permissions|length }}
                    {% else %}
                        0
                    {% endif %}
                </div>
                <div class="summary-label">总权限数</div>
            </div>
            <div class="summary-item">
                <div class="summary-number" id="grantedPermissions">
                    {% if user_permissions %}
                        {{ user_permissions|length }}
                    {% elif role_permissions %}
                        {{ role_permissions|length }}
                    {% else %}
                        0
                    {% endif %}
                </div>
                <div class="summary-label">已授权</div>
            </div>
            <div class="summary-item">
                <div class="summary-number" id="categoryCount">
                    {% if user_permissions %}
                        {{ user_permissions.keys|length }}
                    {% elif role_permissions %}
                        {{ role_permissions.keys|length }}
                    {% else %}
                        0
                    {% endif %}
                </div>
                <div class="summary-label">权限分类</div>
            </div>
        </div>
    </div>

    <!-- 搜索框 -->
    <div class="search-box">
        <input type="text" class="form-control search-input" id="permissionSearch" 
               placeholder="搜索权限..." onkeyup="searchPermissions()">
        <i class="fas fa-search search-icon"></i>
    </div>

    <!-- 权限列表 -->
    <div id="permissionsList">
        {% if user_permissions %}
            {% for category, permissions in user_permissions.items %}
            <div class="permission-category" data-category="{{ category }}">
                <div class="category-header">
                    <span>
                        <i class="fas fa-folder"></i> {{ category|capfirst }}
                        <span class="badge bg-light text-dark ms-2">{{ permissions|length }}</span>
                    </span>
                    <button class="category-toggle" onclick="toggleCategory(this)">
                        <i class="fas fa-chevron-down"></i>
                    </button>
                </div>
                <div class="category-content">
                    {% for permission in permissions %}
                    <div class="permission-item" data-permission="{{ permission }}">
                        <div class="permission-info">
                            <div class="permission-name">{{ permission }}</div>
                            <div class="permission-description">用户权限</div>
                        </div>
                        <div class="permission-status">
                            <span class="status-badge status-granted">
                                <i class="fas fa-check"></i> 已授权
                            </span>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endfor %}
        {% elif role_permissions %}
            {% for category, permissions in role_permissions.items %}
            <div class="permission-category" data-category="{{ category }}">
                <div class="category-header">
                    <span>
                        <i class="fas fa-folder"></i> {{ category|capfirst }}
                        <span class="badge bg-light text-dark ms-2">{{ permissions|length }}</span>
                    </span>
                    <button class="category-toggle" onclick="toggleCategory(this)">
                        <i class="fas fa-chevron-down"></i>
                    </button>
                </div>
                <div class="category-content">
                    {% for permission in permissions %}
                    <div class="permission-item" data-permission="{{ permission.name }}">
                        <div class="permission-info">
                            <div class="permission-name">{{ permission.name }}</div>
                            <div class="permission-description">{{ permission.description }}</div>
                        </div>
                        <div class="permission-status">
                            <span class="status-badge status-inherited">
                                <i class="fas fa-user-tag"></i> 角色权限
                            </span>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endfor %}
        {% endif %}
    </div>
    {% else %}
    <!-- 空状态 -->
    <div class="preview-container">
        <div class="empty-state">
            <i class="fas fa-shield-alt"></i>
            <h5>请选择用户或角色</h5>
            <p>选择一个用户或角色来预览其权限配置</p>
        </div>
    </div>
    {% endif %}

    {% if error %}
    <div class="preview-container">
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle"></i> {{ error }}
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
// 预览用户权限
function previewUserPermissions() {
    const userId = document.getElementById('userSelect').value;
    if (userId) {
        // 清除角色选择
        document.getElementById('roleSelect').value = '';
        // 跳转到用户权限预览
        window.location.href = `{% url 'users:permission_preview' %}?user_id=${userId}`;
    }
}

// 预览角色权限
function previewRolePermissions() {
    const roleCode = document.getElementById('roleSelect').value;
    if (roleCode) {
        // 清除用户选择
        document.getElementById('userSelect').value = '';
        // 跳转到角色权限预览
        window.location.href = `{% url 'users:permission_preview' %}?role=${roleCode}`;
    }
}

// 切换分类展开/折叠
function toggleCategory(button) {
    const content = button.closest('.category-header').nextElementSibling;
    const icon = button.querySelector('i');
    
    if (content.style.display === 'none') {
        content.style.display = '';
        icon.className = 'fas fa-chevron-down';
    } else {
        content.style.display = 'none';
        icon.className = 'fas fa-chevron-right';
    }
}

// 搜索权限
function searchPermissions() {
    const query = document.getElementById('permissionSearch').value.toLowerCase();
    const permissionItems = document.querySelectorAll('.permission-item');
    const categories = document.querySelectorAll('.permission-category');
    
    permissionItems.forEach(item => {
        const permissionName = item.dataset.permission.toLowerCase();
        const description = item.querySelector('.permission-description').textContent.toLowerCase();
        
        if (permissionName.includes(query) || description.includes(query)) {
            item.style.display = '';
        } else {
            item.style.display = 'none';
        }
    });
    
    // 隐藏没有可见权限的分类
    categories.forEach(category => {
        const visibleItems = category.querySelectorAll('.permission-item[style=""], .permission-item:not([style])');
        if (visibleItems.length === 0) {
            category.style.display = 'none';
        } else {
            category.style.display = '';
        }
    });
}

// 导出权限
function exportPermissions() {
    const permissions = [];
    const permissionItems = document.querySelectorAll('.permission-item');
    
    permissionItems.forEach(item => {
        const name = item.dataset.permission;
        const description = item.querySelector('.permission-description').textContent;
        const status = item.querySelector('.status-badge').textContent.trim();
        const category = item.closest('.permission-category').dataset.category;
        
        permissions.push({ category, name, description, status });
    });
    
    if (permissions.length === 0) {
        alert('没有权限数据可导出');
        return;
    }
    
    const csv = [
        ['分类', '权限名称', '描述', '状态'],
        ...permissions.map(p => [p.category, p.name, p.description, p.status])
    ].map(row => row.join(',')).join('\n');
    
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    
    const subject = document.getElementById('userSelect').value ? 
        `user_${document.getElementById('userSelect').options[document.getElementById('userSelect').selectedIndex].text}` :
        `role_${document.getElementById('roleSelect').options[document.getElementById('roleSelect').selectedIndex].text}`;
    
    link.download = `permissions_${subject}.csv`;
    link.click();
}

// 全部展开/折叠
function toggleAllCategories(expand = true) {
    const buttons = document.querySelectorAll('.category-toggle');
    buttons.forEach(button => {
        const content = button.closest('.category-header').nextElementSibling;
        const icon = button.querySelector('i');
        
        if (expand) {
            content.style.display = '';
            icon.className = 'fas fa-chevron-down';
        } else {
            content.style.display = 'none';
            icon.className = 'fas fa-chevron-right';
        }
    });
}

// 键盘快捷键
document.addEventListener('keydown', function(e) {
    if (e.ctrlKey && e.key === 'f') {
        e.preventDefault();
        document.getElementById('permissionSearch').focus();
    }
    
    if (e.ctrlKey && e.key === 'e') {
        e.preventDefault();
        exportPermissions();
    }
});

// 页面加载完成后默认展开所有分类
document.addEventListener('DOMContentLoaded', function() {
    toggleAllCategories(true);
});
</script>
{% endblock %}
