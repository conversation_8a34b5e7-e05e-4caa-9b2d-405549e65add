from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth import login, authenticate, logout
from django.contrib.auth.forms import UserCreationForm
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.views.generic import ListView, TemplateView
from django.contrib.auth.models import User
from django.http import JsonResponse
from django.db import transaction, models
from django.core.paginator import Paginator
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
import json
from datetime import datetime, timedelta
from django.utils import timezone
from .models import (
    UserProfile, AuditLog, PagePermission, RolePagePermission,
    CustomPermission, PermissionTemplate, MenuConfiguration, MenuRolePermission
)
from .decorators import AdminRequiredMixin, SuperAdminRequiredMixin, PermissionRequiredMixin
from .permissions import UserRoles, PermissionManager

# Create your views here.

def user_login(request):
    """用户登录视图"""
    if request.user.is_authenticated:
        return redirect('products:list')

    if request.method == 'POST':
        username = request.POST.get('username')
        password = request.POST.get('password')

        if username and password:
            user = authenticate(request, username=username, password=password)
            if user is not None:
                login(request, user)
                messages.success(request, f'欢迎回来，{user.username}！')
                next_url = request.GET.get('next', 'products:list')
                return redirect(next_url)
            else:
                messages.error(request, '用户名或密码错误')
        else:
            messages.error(request, '请输入用户名和密码')

    return render(request, 'users/login.html')

def user_register(request):
    """用户注册视图"""
    if request.user.is_authenticated:
        return redirect('products:list')

    if request.method == 'POST':
        form = UserCreationForm(request.POST)
        if form.is_valid():
            try:
                with transaction.atomic():
                    user = form.save()
                    # 创建用户资料，默认为查看员
                    UserProfile.objects.create(user=user, role=UserRoles.VIEWER)

                    # 自动登录
                    username = form.cleaned_data.get('username')
                    password = form.cleaned_data.get('password1')
                    user = authenticate(username=username, password=password)
                    login(request, user)

                    messages.success(request, f'注册成功！欢迎，{username}！')
                    return redirect('products:list')
            except Exception as e:
                messages.error(request, f'注册失败：{str(e)}')
        else:
            for field, errors in form.errors.items():
                for error in errors:
                    messages.error(request, f'{field}: {error}')
    else:
        form = UserCreationForm()

    return render(request, 'users/register.html', {'form': form})

@login_required
def user_logout(request):
    """用户登出视图"""
    logout(request)
    messages.success(request, '您已成功登出')
    return redirect('users:login')

class UserManagementView(PermissionRequiredMixin, ListView):
    """用户管理视图（仅管理员可访问）"""
    model = UserProfile
    template_name = 'users/management.html'
    context_object_name = 'user_profiles'
    paginate_by = 20
    permission_app = 'users'
    permission_action = 'view'

    def get_queryset(self):
        return UserProfile.objects.select_related('user').all().order_by('-created_at')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['total_users'] = UserProfile.objects.count()

        # 统计各角色用户数量
        role_stats = {}
        for role_code, role_name in UserRoles.CHOICES:
            role_stats[role_name] = UserProfile.objects.filter(role=role_code).count()
        context['role_stats'] = role_stats
        context['available_roles'] = UserRoles.CHOICES

        return context


class RoleManagementView(SuperAdminRequiredMixin, ListView):
    """角色管理视图（仅超级管理员可访问）"""
    model = UserProfile
    template_name = 'users/role_management.html'
    context_object_name = 'user_profiles'
    paginate_by = 20

    def get_queryset(self):
        return UserProfile.objects.select_related('user').all().order_by('-created_at')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['available_roles'] = UserRoles.CHOICES
        context['permission_matrix'] = self._get_permission_matrix()
        return context

    def _get_permission_matrix(self):
        """获取权限矩阵用于显示"""
        matrix = {}
        for role_code, role_name in UserRoles.CHOICES:
            matrix[role_name] = PermissionManager.get_role_permissions(role_code)
        return matrix


@login_required
def assign_user_role(request):
    """分配用户角色（AJAX接口）"""
    if request.method != 'POST':
        return JsonResponse({'success': False, 'message': '请求方法错误'})

    # 检查权限：只有超级管理员可以分配角色
    if not request.user.is_superuser:
        user_profile = getattr(request.user, 'userprofile', None)
        if not user_profile or user_profile.role != UserRoles.SUPER_ADMIN:
            return JsonResponse({'success': False, 'message': '权限不足'})

    try:
        user_id = request.POST.get('user_id')
        new_role = request.POST.get('role')

        if not user_id or not new_role:
            return JsonResponse({'success': False, 'message': '参数不完整'})

        # 验证角色是否有效
        valid_roles = [role[0] for role in UserRoles.CHOICES]
        if new_role not in valid_roles:
            return JsonResponse({'success': False, 'message': '无效的角色'})

        # 获取目标用户
        target_user = get_object_or_404(User, id=user_id)

        # 不能修改自己的角色
        if target_user == request.user:
            return JsonResponse({'success': False, 'message': '不能修改自己的角色'})

        # 分配角色
        success = PermissionManager.assign_user_to_role(target_user, new_role)

        if success:
            role_name = UserRoles.get_display_name(new_role)
            return JsonResponse({
                'success': True,
                'message': f'成功将用户 {target_user.username} 的角色设置为 {role_name}'
            })
        else:
            return JsonResponse({'success': False, 'message': '角色分配失败'})

    except Exception as e:
        return JsonResponse({'success': False, 'message': f'操作失败：{str(e)}'})


@login_required
def init_permission_system(request):
    """初始化权限系统（仅超级管理员可访问）"""
    if not request.user.is_superuser:
        messages.error(request, '权限不足')
        return redirect('users:management')

    try:
        from .permissions import init_permissions
        init_permissions()
        messages.success(request, '权限系统初始化成功！')
    except Exception as e:
        messages.error(request, f'权限系统初始化失败：{str(e)}')

    return redirect('users:role_management')


class AuditLogView(AdminRequiredMixin, ListView):
    """操作审计日志视图（仅管理员可访问）"""
    model = AuditLog
    template_name = 'users/audit_log.html'
    context_object_name = 'audit_logs'
    paginate_by = 50
    ordering = ['-timestamp']

    def get_queryset(self):
        queryset = super().get_queryset().select_related('user', 'content_type')

        # 搜索过滤
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                models.Q(user__username__icontains=search) |
                models.Q(object_repr__icontains=search) |
                models.Q(action__icontains=search)
            )

        # 操作类型过滤
        action = self.request.GET.get('action')
        if action:
            queryset = queryset.filter(action=action)

        # 用户过滤
        user_id = self.request.GET.get('user')
        if user_id:
            queryset = queryset.filter(user_id=user_id)

        # 日期范围过滤
        date_from = self.request.GET.get('date_from')
        date_to = self.request.GET.get('date_to')
        if date_from:
            from django.utils.dateparse import parse_date
            date_obj = parse_date(date_from)
            if date_obj:
                queryset = queryset.filter(timestamp__date__gte=date_obj)
        if date_to:
            from django.utils.dateparse import parse_date
            date_obj = parse_date(date_to)
            if date_obj:
                queryset = queryset.filter(timestamp__date__lte=date_obj)

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # 添加过滤选项
        context['action_choices'] = AuditLog.ACTION_CHOICES
        context['users'] = User.objects.filter(
            auditlog__isnull=False
        ).distinct().order_by('username')

        # 保持搜索参数
        context['search'] = self.request.GET.get('search', '')
        context['selected_action'] = self.request.GET.get('action', '')
        context['selected_user'] = self.request.GET.get('user', '')
        context['date_from'] = self.request.GET.get('date_from', '')
        context['date_to'] = self.request.GET.get('date_to', '')

        # 统计信息
        from django.utils import timezone
        from datetime import timedelta

        today = timezone.now().date()
        context['stats'] = {
            'total_logs': AuditLog.objects.count(),
            'today_logs': AuditLog.objects.filter(timestamp__date=today).count(),
            'week_logs': AuditLog.objects.filter(
                timestamp__date__gte=today - timedelta(days=7)
            ).count(),
        }

        return context


# ==================== 权限管理仪表板视图 ====================

class PermissionDashboardView(SuperAdminRequiredMixin, TemplateView):
    """权限管理仪表板主页"""
    template_name = 'users/permission_dashboard.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # 获取统计数据
        context.update({
            'total_users': User.objects.count(),
            'total_permissions': PagePermission.objects.count(),
            'total_role_permissions': RolePagePermission.objects.count(),
            'total_custom_permissions': CustomPermission.objects.count(),
            'total_templates': PermissionTemplate.objects.count(),
            'active_permissions': PagePermission.objects.filter(is_active=True).count(),
            'role_stats': self._get_role_statistics(),
            'recent_changes': self._get_recent_permission_changes(),
            'permission_categories': self._get_permission_categories(),
        })

        return context

    def _get_role_statistics(self):
        """获取角色统计信息"""
        role_stats = {}
        for role_code, role_name in UserRoles.CHOICES:
            user_count = UserProfile.objects.filter(role=role_code).count()
            permission_count = RolePagePermission.objects.filter(
                role=role_code, is_granted=True
            ).count()
            role_stats[role_code] = {
                'name': role_name,
                'user_count': user_count,
                'permission_count': permission_count
            }
        return role_stats

    def _get_recent_permission_changes(self):
        """获取最近的权限变更"""
        recent_logs = AuditLog.objects.filter(
            action__in=['PERMISSION_GRANTED', 'PERMISSION_REVOKED', 'ROLE_CHANGED']
        ).order_by('-timestamp')[:10]

        return recent_logs

    def _get_permission_categories(self):
        """获取权限分类统计"""
        categories = PagePermission.objects.values('category').annotate(
            count=models.Count('id'),
            active_count=models.Count('id', filter=models.Q(is_active=True))
        ).order_by('category')

        return list(categories)


class PermissionMatrixView(SuperAdminRequiredMixin, TemplateView):
    """权限矩阵可视化视图"""
    template_name = 'users/permission_matrix.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # 获取所有权限和角色
        permissions = PagePermission.objects.filter(is_active=True).order_by('category', 'sort_order')
        roles = UserRoles.CHOICES

        # 构建权限矩阵
        matrix = self._build_permission_matrix(permissions, roles)

        context.update({
            'permissions': permissions,
            'roles': roles,
            'matrix': matrix,
            'categories': permissions.values_list('category', flat=True).distinct(),
        })

        return context

    def _build_permission_matrix(self, permissions, roles):
        """构建权限矩阵数据"""
        matrix = {}

        # 获取所有角色权限分配
        role_permissions = RolePagePermission.objects.filter(
            page_permission__in=permissions,
            is_granted=True
        ).select_related('page_permission')

        # 构建权限映射
        permission_map = {}
        for rp in role_permissions:
            key = f"{rp.role}_{rp.page_permission.id}"
            permission_map[key] = rp

        # 构建矩阵
        for permission in permissions:
            matrix[permission.id] = {}
            for role_code, role_name in roles:
                key = f"{role_code}_{permission.id}"
                matrix[permission.id][role_code] = permission_map.get(key) is not None

        return matrix


class UserRoleManagementView(SuperAdminRequiredMixin, TemplateView):
    """用户角色管理视图"""
    template_name = 'users/user_role_management.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # 获取用户列表
        users = User.objects.select_related('userprofile').order_by('username')

        # 分页
        paginator = Paginator(users, 20)
        page_number = self.request.GET.get('page')
        page_obj = paginator.get_page(page_number)

        context.update({
            'users': page_obj,
            'roles': UserRoles.CHOICES,
            'total_users': users.count(),
        })

        return context


class PermissionPreviewView(SuperAdminRequiredMixin, TemplateView):
    """权限预览视图"""
    template_name = 'users/permission_preview.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        user_id = self.request.GET.get('user_id')
        role_code = self.request.GET.get('role')

        if user_id:
            try:
                user = User.objects.get(id=user_id)
                permissions = self._get_user_permissions(user)
                context['preview_user'] = user
                context['user_permissions'] = permissions
            except User.DoesNotExist:
                context['error'] = '用户不存在'

        elif role_code:
            permissions = self._get_role_permissions(role_code)
            context['preview_role'] = dict(UserRoles.CHOICES).get(role_code)
            context['role_permissions'] = permissions

        context['users'] = User.objects.select_related('userprofile').order_by('username')
        context['roles'] = UserRoles.CHOICES

        return context

    def _get_user_permissions(self, user):
        """获取用户的所有权限"""
        permissions = PermissionManager.get_user_permissions(user)

        # 按分类组织权限
        categorized_permissions = {}
        for perm in permissions:
            if '.' in perm:
                parts = perm.split('.')
                if len(parts) >= 2:
                    category = parts[1]
                    if category not in categorized_permissions:
                        categorized_permissions[category] = []
                    categorized_permissions[category].append(perm)

        return categorized_permissions

    def _get_role_permissions(self, role_code):
        """获取角色的所有权限"""
        role_permissions = RolePagePermission.objects.filter(
            role=role_code,
            is_granted=True,
            page_permission__is_active=True
        ).select_related('page_permission')

        # 按分类组织权限
        categorized_permissions = {}
        for rp in role_permissions:
            category = rp.page_permission.category
            if category not in categorized_permissions:
                categorized_permissions[category] = []
            categorized_permissions[category].append(rp.page_permission)

        return categorized_permissions


# ==================== AJAX API 视图 ====================

@login_required
@require_http_methods(["POST"])
@csrf_exempt
def ajax_change_user_role(request):
    """AJAX: 修改用户角色"""
    if not PermissionManager.user_has_permission(request.user, 'users', 'change'):
        return JsonResponse({'success': False, 'error': '权限不足'}, status=403)

    try:
        data = json.loads(request.body)
        user_id = data.get('user_id')
        new_role = data.get('role')

        if not user_id or not new_role:
            return JsonResponse({'success': False, 'error': '参数不完整'})

        # 验证角色是否有效
        valid_roles = [role[0] for role in UserRoles.CHOICES]
        if new_role not in valid_roles:
            return JsonResponse({'success': False, 'error': '无效的角色'})

        # 获取用户
        user = get_object_or_404(User, id=user_id)
        user_profile = user.userprofile
        old_role = user_profile.role

        # 修改角色
        with transaction.atomic():
            user_profile.role = new_role
            user_profile.save()

            # 记录审计日志
            AuditLog.objects.create(
                user=request.user,
                action='ROLE_CHANGED',
                content_type_id=1,
                object_id=user.id,
                object_repr=f"用户 {user.username} 角色变更",
                changes={
                    'old_role': old_role,
                    'new_role': new_role,
                    'target_user': user.username
                },
                ip_address=request.META.get('REMOTE_ADDR', ''),
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                extra_data={'operation': 'role_change'}
            )

            # 清除用户权限缓存
            PermissionManager.clear_user_permission_cache(user)

        return JsonResponse({
            'success': True,
            'message': f'用户 {user.username} 的角色已更新为 {dict(UserRoles.CHOICES)[new_role]}'
        })

    except json.JSONDecodeError:
        return JsonResponse({'success': False, 'error': 'JSON格式错误'})
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


@login_required
@require_http_methods(["POST"])
@csrf_exempt
def ajax_toggle_role_permission(request):
    """AJAX: 切换角色权限"""
    if not PermissionManager.user_has_permission(request.user, 'users', 'change'):
        return JsonResponse({'success': False, 'error': '权限不足'}, status=403)

    try:
        data = json.loads(request.body)
        role_code = data.get('role')
        permission_id = data.get('permission_id')

        if not role_code or not permission_id:
            return JsonResponse({'success': False, 'error': '参数不完整'})

        # 获取权限
        permission = get_object_or_404(PagePermission, id=permission_id)

        # 获取或创建角色权限
        role_permission, created = RolePagePermission.objects.get_or_create(
            role=role_code,
            page_permission=permission,
            defaults={
                'is_granted': True,
                'granted_by': request.user,
                'grant_source': 'manual'
            }
        )

        if not created:
            # 切换权限状态
            role_permission.is_granted = not role_permission.is_granted
            role_permission.granted_by = request.user
            role_permission.save()

        # 记录审计日志
        AuditLog.objects.create(
            user=request.user,
            action='PERMISSION_GRANTED' if role_permission.is_granted else 'PERMISSION_REVOKED',
            content_type_id=1,
            object_id=permission.id,
            object_repr=f"角色权限变更: {role_code} - {permission.name}",
            changes={
                'role': role_code,
                'permission': permission.name,
                'granted': role_permission.is_granted
            },
            ip_address=request.META.get('REMOTE_ADDR', ''),
            user_agent=request.META.get('HTTP_USER_AGENT', ''),
            extra_data={'operation': 'role_permission_toggle'}
        )

        # 清除相关用户的权限缓存
        PermissionManager._clear_role_users_cache(role_code)

        return JsonResponse({
            'success': True,
            'granted': role_permission.is_granted,
            'message': f'角色 {role_code} 对权限 {permission.name} 的访问已{"授予" if role_permission.is_granted else "撤销"}'
        })

    except json.JSONDecodeError:
        return JsonResponse({'success': False, 'error': 'JSON格式错误'})
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


@login_required
@require_http_methods(["GET"])
def ajax_search_permissions(request):
    """AJAX: 搜索权限"""
    if not PermissionManager.user_has_permission(request.user, 'users', 'view'):
        return JsonResponse({'success': False, 'error': '权限不足'}, status=403)

    try:
        query = request.GET.get('q', '').strip()
        category = request.GET.get('category', '').strip()

        permissions = PagePermission.objects.filter(is_active=True)

        if query:
            permissions = permissions.filter(
                models.Q(name__icontains=query) |
                models.Q(description__icontains=query) |
                models.Q(app_name__icontains=query)
            )

        if category:
            permissions = permissions.filter(category=category)

        permissions = permissions.order_by('category', 'sort_order')[:50]

        results = []
        for perm in permissions:
            results.append({
                'id': perm.id,
                'name': perm.name,
                'description': perm.description,
                'app_name': perm.app_name,
                'category': perm.category,
                'permission_level': perm.permission_level
            })

        return JsonResponse({
            'success': True,
            'permissions': results,
            'total': len(results)
        })

    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


@login_required
@require_http_methods(["GET"])
def ajax_get_user_permissions(request):
    """AJAX: 获取用户权限详情"""
    if not PermissionManager.user_has_permission(request.user, 'users', 'view'):
        return JsonResponse({'success': False, 'error': '权限不足'}, status=403)

    try:
        user_id = request.GET.get('user_id')
        if not user_id:
            return JsonResponse({'success': False, 'error': '用户ID不能为空'})

        user = get_object_or_404(User, id=user_id)

        # 获取用户基本信息
        user_info = {
            'id': user.id,
            'username': user.username,
            'email': user.email,
            'role': user.userprofile.role,
            'role_display': UserRoles.get_display_name(user.userprofile.role),
            'is_active': user.is_active,
            'last_login': user.last_login.isoformat() if user.last_login else None
        }

        # 获取用户权限
        permissions = PermissionManager.get_user_permissions(user)

        # 获取角色权限
        role_permissions = RolePagePermission.objects.filter(
            role=user.userprofile.role,
            is_granted=True,
            page_permission__is_active=True
        ).select_related('page_permission')

        role_perms = []
        for rp in role_permissions:
            role_perms.append({
                'id': rp.page_permission.id,
                'name': rp.page_permission.name,
                'description': rp.page_permission.description,
                'category': rp.page_permission.category,
                'source': 'role'
            })

        # 获取自定义权限
        custom_permissions = CustomPermission.objects.filter(
            user=user,
            is_granted__isnull=False
        )

        custom_perms = []
        for cp in custom_permissions:
            custom_perms.append({
                'permission_key': cp.permission_key,
                'is_granted': cp.is_granted,
                'priority': cp.priority,
                'grant_reason': cp.grant_reason,
                'source': 'custom'
            })

        return JsonResponse({
            'success': True,
            'user': user_info,
            'all_permissions': permissions,
            'role_permissions': role_perms,
            'custom_permissions': custom_perms
        })

    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


@login_required
@require_http_methods(["GET"])
def ajax_get_dashboard_stats(request):
    """AJAX: 获取仪表板统计数据"""
    if not PermissionManager.user_has_permission(request.user, 'users', 'view'):
        return JsonResponse({'success': False, 'error': '权限不足'}, status=403)

    try:
        # 基础统计
        stats = {
            'users': {
                'total': User.objects.count(),
                'active': User.objects.filter(is_active=True).count(),
                'inactive': User.objects.filter(is_active=False).count()
            },
            'permissions': {
                'total': PagePermission.objects.count(),
                'active': PagePermission.objects.filter(is_active=True).count(),
                'inactive': PagePermission.objects.filter(is_active=False).count()
            },
            'role_permissions': RolePagePermission.objects.filter(is_granted=True).count(),
            'custom_permissions': CustomPermission.objects.filter(is_granted=True).count(),
            'templates': PermissionTemplate.objects.filter(is_active=True).count()
        }

        # 角色分布
        role_distribution = {}
        for role_code, role_name in UserRoles.CHOICES:
            count = UserProfile.objects.filter(role=role_code).count()
            role_distribution[role_code] = {
                'name': role_name,
                'count': count
            }

        # 权限分类统计
        category_stats = list(
            PagePermission.objects.values('category').annotate(
                total=models.Count('id'),
                active=models.Count('id', filter=models.Q(is_active=True))
            ).order_by('category')
        )

        # 最近活动
        recent_activities = AuditLog.objects.filter(
            action__in=['ROLE_CHANGED', 'PERMISSION_GRANTED', 'PERMISSION_REVOKED']
        ).order_by('-timestamp')[:10]

        activities = []
        for log in recent_activities:
            activities.append({
                'action': log.action,
                'user': log.user.username,
                'timestamp': log.timestamp.isoformat(),
                'description': log.object_repr
            })

        return JsonResponse({
            'success': True,
            'stats': stats,
            'role_distribution': role_distribution,
            'category_stats': category_stats,
            'recent_activities': activities
        })

    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


# ==================== 菜单权限管理视图 ====================

class MenuManagementView(SuperAdminRequiredMixin, TemplateView):
    """菜单权限管理视图"""
    template_name = 'users/menu_management.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # 获取所有菜单配置
        menus = MenuConfiguration.objects.filter(is_active=True).order_by('sort_order')

        # 获取菜单权限矩阵
        menu_permissions = {}
        for menu in menus:
            menu_permissions[menu.id] = {}
            for role_code, role_name in UserRoles.CHOICES:
                try:
                    permission = MenuRolePermission.objects.get(menu=menu, role=role_code)
                    menu_permissions[menu.id][role_code] = permission.is_granted
                except MenuRolePermission.DoesNotExist:
                    menu_permissions[menu.id][role_code] = False

        context.update({
            'menus': menus,
            'roles': UserRoles.CHOICES,
            'menu_permissions': menu_permissions,
            'total_menus': menus.count(),
        })

        return context


@login_required
@require_http_methods(["POST"])
@csrf_exempt
def ajax_toggle_menu_permission(request):
    """AJAX: 切换菜单权限"""
    if not PermissionManager.user_has_permission(request.user, 'users', 'change'):
        return JsonResponse({'success': False, 'error': '权限不足'}, status=403)

    try:
        data = json.loads(request.body)
        menu_id = data.get('menu_id')
        role_code = data.get('role')

        if not menu_id or not role_code:
            return JsonResponse({'success': False, 'error': '参数不完整'})

        # 获取菜单
        menu = get_object_or_404(MenuConfiguration, id=menu_id)

        # 获取或创建菜单权限
        menu_permission, created = MenuRolePermission.objects.get_or_create(
            menu=menu,
            role=role_code,
            defaults={
                'is_granted': True,
                'granted_by': request.user
            }
        )

        if not created:
            # 切换权限状态
            menu_permission.is_granted = not menu_permission.is_granted
            menu_permission.granted_by = request.user
            menu_permission.save()

        # 记录审计日志
        AuditLog.objects.create(
            user=request.user,
            action='MENU_PERMISSION_CHANGED',
            content_type_id=1,
            object_id=menu.id,
            object_repr=f"菜单权限变更: {role_code} - {menu.display_text}",
            changes={
                'menu': menu.display_text,
                'role': role_code,
                'granted': menu_permission.is_granted
            },
            ip_address=request.META.get('REMOTE_ADDR', ''),
            user_agent=request.META.get('HTTP_USER_AGENT', ''),
            extra_data={'operation': 'menu_permission_toggle'}
        )

        # 清除菜单权限缓存
        from django.core.cache import cache
        cache.delete_pattern('menu_permissions:*')

        return JsonResponse({
            'success': True,
            'granted': menu_permission.is_granted,
            'message': f'角色 {role_code} 对菜单 {menu.display_text} 的访问已{"授予" if menu_permission.is_granted else "撤销"}'
        })

    except json.JSONDecodeError:
        return JsonResponse({'success': False, 'error': 'JSON格式错误'})
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


@login_required
@require_http_methods(["GET"])
def ajax_get_menu_permissions(request):
    """AJAX: 获取菜单权限数据"""
    if not PermissionManager.user_has_permission(request.user, 'users', 'view'):
        return JsonResponse({'success': False, 'error': '权限不足'}, status=403)

    try:
        # 获取当前用户的菜单权限
        user_permissions = {}

        if request.user.is_authenticated:
            try:
                user_profile = request.user.userprofile
                role = user_profile.role

                # 获取该角色的菜单权限
                menu_permissions = MenuRolePermission.objects.filter(
                    role=role,
                    is_granted=True,
                    menu__is_active=True
                ).select_related('menu')

                for mp in menu_permissions:
                    user_permissions[mp.menu.name] = {
                        'display_text': mp.menu.display_text,
                        'url_name': mp.menu.url_name,
                        'icon_class': mp.menu.icon_class,
                        'granted': mp.is_granted
                    }

            except Exception:
                pass

        return JsonResponse({
            'success': True,
            'permissions': user_permissions
        })

    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})
