from django.contrib import admin
from .models import Inventory

# Register your models here.

@admin.register(Inventory)
class InventoryAdmin(admin.ModelAdmin):
    list_display = ['product', 'quantity', 'min_stock', 'max_stock', 'stock_status', 'updated_at']
    list_filter = ['updated_at']
    search_fields = ['product__name', 'product__sku']
    readonly_fields = ['updated_at']

    def delete_model(self, request, obj):
        """重写删除方法，记录审计日志"""
        from users.services import AuditService
        from users.signals import _mark_admin_action_logged

        # 标记admin操作已记录，避免信号重复记录
        _mark_admin_action_logged(obj, 'delete')

        # 手动记录审计日志，传入当前用户
        AuditService.log_delete(
            instance=obj,
            user=request.user,
            request=request,
            extra_data={
                'model': 'Inventory',
                'operation': 'admin_delete_inventory',
                'admin_action': True
            }
        )

        # 执行实际删除
        super().delete_model(request, obj)

    def delete_queryset(self, request, queryset):
        """重写批量删除方法，记录审计日志"""
        from users.services import AuditService

        # 为每个对象记录审计日志
        for obj in queryset:
            AuditService.log_delete(
                instance=obj,
                user=request.user,
                request=request,
                extra_data={
                    'model': 'Inventory',
                    'operation': 'admin_batch_delete_inventory',
                    'admin_action': True
                }
            )

        # 执行实际批量删除
        super().delete_queryset(request, queryset)

    def stock_status(self, obj):
        return obj.stock_status
    stock_status.short_description = '库存状态'
