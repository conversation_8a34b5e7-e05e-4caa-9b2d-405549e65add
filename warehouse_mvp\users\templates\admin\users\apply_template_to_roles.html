{% extends "admin/base_site.html" %}
{% load i18n admin_urls static admin_modify %}

{% block title %}{{ title }} | {{ site_title|default:_('Django site admin') }}{% endblock %}

{% block breadcrumbs %}
<div class="breadcrumbs">
    <a href="{% url 'admin:index' %}">{% trans 'Home' %}</a>
    &rsaquo; <a href="{% url 'admin:users_permissiontemplate_changelist' %}">权限模板</a>
    &rsaquo; {{ title }}
</div>
{% endblock %}

{% block content %}
<div class="module aligned">
    <h1>{{ title }}</h1>
    
    <div class="form-row">
        <div class="field-box">
            <h3>选择目标角色</h3>
            <p>将选中的权限模板应用到指定角色。</p>
            
            <form method="post">
                {% csrf_token %}
                
                <!-- 保持选中的模板 -->
                {% for obj in queryset %}
                    <input type="hidden" name="{{ action_checkbox_name }}" value="{{ obj.pk }}">
                {% endfor %}
                
                <div class="form-row">
                    <label for="role_code">目标角色:</label>
                    <select name="role_code" id="role_code" required>
                        <option value="">请选择角色</option>
                        {% for role_code, role_name in roles %}
                            <option value="{{ role_code }}">{{ role_name }}</option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="form-row">
                    <h4>将应用以下权限模板:</h4>
                    <ul>
                        {% for obj in queryset %}
                            <li>
                                <strong>{{ obj.name }}</strong> ({{ obj.category }})
                                {% if obj.description %}
                                    <br><small>{{ obj.description }}</small>
                                {% endif %}
                                {% if obj.target_roles %}
                                    <br><small>推荐角色: 
                                        {% for role in obj.target_roles %}
                                            {{ role }}{% if not forloop.last %}, {% endif %}
                                        {% endfor %}
                                    </small>
                                {% endif %}
                            </li>
                        {% endfor %}
                    </ul>
                </div>
                
                <div class="form-row">
                    <div class="help">
                        <strong>注意:</strong> 应用模板将会创建或更新角色的页面权限。如果权限已存在，将会被更新为模板中的设置。
                    </div>
                </div>
                
                <div class="submit-row">
                    <input type="submit" name="apply" value="应用到角色" class="default">
                    <a href="{% url 'admin:users_permissiontemplate_changelist' %}" class="button cancel-link">取消</a>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const roleSelect = document.getElementById('role_code');
    
    roleSelect.addEventListener('change', function() {
        const selectedRole = this.options[this.selectedIndex];
        if (selectedRole.value) {
            // 可以在这里添加角色权限预览功能
            console.log('选择了角色:', selectedRole.text);
        }
    });
});
</script>
{% endblock %}
