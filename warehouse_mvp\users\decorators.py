"""
权限装饰器模块
提供基于角色的访问控制装饰器和页面级权限检查
"""
from functools import wraps
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin
from django.core.exceptions import PermissionDenied
from django.shortcuts import redirect
from django.contrib import messages
from django.http import JsonResponse, HttpResponseForbidden
from django.template.loader import render_to_string
from django.urls import resolve, reverse
from django.utils.decorators import method_decorator
from django.views.decorators.cache import cache_page
from django.core.cache import cache
from django.conf import settings
import logging
import time
from typing import Optional, List, Union, Callable, Any
from .permissions import PermissionManager, UserRoles


logger = logging.getLogger(__name__)


def get_client_ip(request):
    """获取客户端IP地址"""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip


def _get_permission_cache_key(user_id: int, permission_key: str) -> str:
    """生成权限缓存键"""
    return f"permission:{user_id}:{permission_key}"


def _cache_permission_result(user_id: int, permission_key: str, result: bool, timeout: int = 300):
    """缓存权限检查结果"""
    cache_key = _get_permission_cache_key(user_id, permission_key)
    cache.set(cache_key, result, timeout)


def _get_cached_permission_result(user_id: int, permission_key: str) -> Optional[bool]:
    """获取缓存的权限检查结果"""
    cache_key = _get_permission_cache_key(user_id, permission_key)
    return cache.get(cache_key)


def _log_permission_check(request, permission_key: str, result: bool, check_time: float):
    """记录权限检查日志"""
    if getattr(settings, 'DEBUG', False):
        logger.debug(
            f"Permission check: user={request.user.username}, "
            f"permission={permission_key}, result={result}, "
            f"time={check_time:.3f}s"
        )


def _handle_permission_denied(request, permission_key: str, raise_exception: bool = True):
    """处理权限拒绝"""
    # 记录无权限访问尝试
    from .models import AuditLog
    try:
        AuditLog.objects.create(
            user=request.user,
            action='PERMISSION_DENIED',
            content_type_id=1,
            object_id=0,
            object_repr=f"权限检查失败: {permission_key}",
            changes={'permission_key': permission_key, 'result': 'denied'},
            ip_address=get_client_ip(request),
            user_agent=request.META.get('HTTP_USER_AGENT', ''),
            extra_data={'permission_key': permission_key}
        )
    except Exception as e:
        logger.warning(f"Failed to log permission denial: {e}")

    # 处理AJAX请求
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return JsonResponse({
            'success': False,
            'error': 'permission_denied',
            'message': f'您没有权限执行此操作：{permission_key}'
        }, status=403)

    # 处理普通请求
    if raise_exception:
        raise PermissionDenied(f"您没有权限执行此操作：{permission_key}")
    else:
        messages.error(request, f'您没有权限执行此操作：{permission_key}')
        return redirect('products:list')


def page_permission_required(
    permission_name: str = None,
    app_name: str = None,
    use_cache: bool = True,
    cache_timeout: int = 300,
    raise_exception: bool = True
):
    """
    页面级权限检查装饰器

    Args:
        permission_name: 页面权限名称，如果不提供则自动从URL解析
        app_name: 应用名称，如果不提供则自动从URL解析
        use_cache: 是否使用权限缓存
        cache_timeout: 缓存超时时间（秒）
        raise_exception: 是否抛出异常，False时重定向到无权限页面
    """
    def decorator(view_func):
        @wraps(view_func)
        @login_required
        def _wrapped_view(request, *args, **kwargs):
            start_time = time.time()

            # 自动解析权限信息
            if not permission_name or not app_name:
                try:
                    resolved = resolve(request.path)
                    auto_app_name = app_name or resolved.app_name
                    auto_permission_name = permission_name or resolved.url_name

                    if not auto_app_name or not auto_permission_name:
                        logger.warning(f"Cannot auto-resolve permission for {request.path}")
                        auto_app_name = app_name or 'unknown'
                        auto_permission_name = permission_name or 'unknown'
                except Exception as e:
                    logger.warning(f"Failed to resolve URL {request.path}: {e}")
                    auto_app_name = app_name or 'unknown'
                    auto_permission_name = permission_name or 'unknown'
            else:
                auto_app_name = app_name
                auto_permission_name = permission_name

            permission_key = f"page.{auto_app_name}.{auto_permission_name}"

            # 检查缓存
            has_permission = None
            if use_cache:
                has_permission = _get_cached_permission_result(request.user.id, permission_key)

            # 如果缓存中没有，进行权限检查
            if has_permission is None:
                has_permission = PermissionManager.check_page_permission(
                    request.user, auto_app_name, auto_permission_name
                )

                # 缓存结果
                if use_cache:
                    _cache_permission_result(request.user.id, permission_key, has_permission, cache_timeout)

            check_time = time.time() - start_time
            _log_permission_check(request, permission_key, has_permission, check_time)

            if not has_permission:
                return _handle_permission_denied(request, permission_key, raise_exception)

            return view_func(request, *args, **kwargs)
        return _wrapped_view
    return decorator


def dynamic_permission_required(
    permission_func: Callable[[Any, Any], str],
    use_cache: bool = True,
    cache_timeout: int = 300,
    raise_exception: bool = True
):
    """
    动态权限检查装饰器

    Args:
        permission_func: 动态生成权限键的函数，接收(request, *args, **kwargs)参数
        use_cache: 是否使用权限缓存
        cache_timeout: 缓存超时时间（秒）
        raise_exception: 是否抛出异常
    """
    def decorator(view_func):
        @wraps(view_func)
        @login_required
        def _wrapped_view(request, *args, **kwargs):
            start_time = time.time()

            # 动态生成权限键
            try:
                permission_key = permission_func(request, *args, **kwargs)
            except Exception as e:
                logger.error(f"Failed to generate dynamic permission key: {e}")
                permission_key = "unknown.permission"

            # 检查缓存
            has_permission = None
            if use_cache:
                has_permission = _get_cached_permission_result(request.user.id, permission_key)

            # 如果缓存中没有，进行权限检查
            if has_permission is None:
                has_permission = PermissionManager.check_custom_permission(
                    request.user, permission_key
                )

                # 缓存结果
                if use_cache:
                    _cache_permission_result(request.user.id, permission_key, has_permission, cache_timeout)

            check_time = time.time() - start_time
            _log_permission_check(request, permission_key, has_permission, check_time)

            if not has_permission:
                return _handle_permission_denied(request, permission_key, raise_exception)

            return view_func(request, *args, **kwargs)
        return _wrapped_view
    return decorator


def permission_required(
    app_name: str,
    action: str,
    raise_exception: bool = True,
    use_cache: bool = True,
    cache_timeout: int = 300
):
    """
    增强的权限检查装饰器（保持向后兼容）

    Args:
        app_name: 应用名称 (products, inventory, transactions, users, audit_log)
        action: 操作类型 (add, change, delete, view)
        raise_exception: 是否抛出异常，False时重定向到无权限页面
        use_cache: 是否使用权限缓存
        cache_timeout: 缓存超时时间（秒）
    """
    def decorator(view_func):
        @wraps(view_func)
        @login_required
        def _wrapped_view(request, *args, **kwargs):
            start_time = time.time()
            permission_key = f"app.{app_name}.{action}"

            # 检查缓存
            has_permission = None
            if use_cache:
                has_permission = _get_cached_permission_result(request.user.id, permission_key)

            # 如果缓存中没有，进行权限检查
            if has_permission is None:
                has_permission = PermissionManager.user_has_permission(
                    request.user, app_name, action
                )

                # 缓存结果
                if use_cache:
                    _cache_permission_result(request.user.id, permission_key, has_permission, cache_timeout)

            check_time = time.time() - start_time
            _log_permission_check(request, permission_key, has_permission, check_time)

            if not has_permission:
                return _handle_permission_denied(request, permission_key, raise_exception)

            return view_func(request, *args, **kwargs)
        return _wrapped_view
    return decorator


def multi_permission_required(
    permissions: List[tuple],
    operator: str = 'AND',
    use_cache: bool = True,
    cache_timeout: int = 300,
    raise_exception: bool = True
):
    """
    多权限检查装饰器

    Args:
        permissions: 权限列表，每个元素为(app_name, action)元组
        operator: 权限组合方式，'AND'或'OR'
        use_cache: 是否使用权限缓存
        cache_timeout: 缓存超时时间（秒）
        raise_exception: 是否抛出异常
    """
    def decorator(view_func):
        @wraps(view_func)
        @login_required
        def _wrapped_view(request, *args, **kwargs):
            start_time = time.time()

            permission_results = []
            permission_keys = []

            for app_name, action in permissions:
                permission_key = f"app.{app_name}.{action}"
                permission_keys.append(permission_key)

                # 检查缓存
                has_permission = None
                if use_cache:
                    has_permission = _get_cached_permission_result(request.user.id, permission_key)

                # 如果缓存中没有，进行权限检查
                if has_permission is None:
                    has_permission = PermissionManager.user_has_permission(
                        request.user, app_name, action
                    )

                    # 缓存结果
                    if use_cache:
                        _cache_permission_result(request.user.id, permission_key, has_permission, cache_timeout)

                permission_results.append(has_permission)

            # 根据操作符计算最终结果
            if operator.upper() == 'AND':
                final_result = all(permission_results)
            elif operator.upper() == 'OR':
                final_result = any(permission_results)
            else:
                raise ValueError(f"Invalid operator: {operator}. Use 'AND' or 'OR'")

            check_time = time.time() - start_time
            combined_key = f"multi:{operator}:{':'.join(permission_keys)}"
            _log_permission_check(request, combined_key, final_result, check_time)

            if not final_result:
                return _handle_permission_denied(request, combined_key, raise_exception)

            return view_func(request, *args, **kwargs)
        return _wrapped_view
    return decorator

def role_required(*allowed_roles):
    """
    角色检查装饰器

    Args:
        allowed_roles: 允许的角色列表
    """
    def decorator(view_func):
        @wraps(view_func)
        @login_required
        def _wrapped_view(request, *args, **kwargs):
            user_role = PermissionManager.get_user_role(request.user)

            if user_role not in allowed_roles and not request.user.is_superuser:
                # 处理AJAX请求
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return JsonResponse({
                        'success': False,
                        'error': 'role_denied',
                        'message': f'您的角色({user_role})无权访问此功能'
                    }, status=403)

                messages.error(request, f'您的角色({user_role})无权访问此功能')
                return redirect('products:list')

            return view_func(request, *args, **kwargs)
        return _wrapped_view
    return decorator


def super_admin_required(view_func):
    """超级管理员权限装饰器"""
    return role_required(UserRoles.SUPER_ADMIN)(view_func)


def admin_required(view_func):
    """管理员权限装饰器（超级管理员或仓库管理员）"""
    return role_required(UserRoles.SUPER_ADMIN, UserRoles.WAREHOUSE_MANAGER)(view_func)

class PermissionRequiredMixin(LoginRequiredMixin):
    """增强的权限检查Mixin类"""
    permission_app = None
    permission_action = None
    use_cache = True
    cache_timeout = 300
    raise_exception = False

    def get_permission_app(self):
        """获取权限应用名称，可以被子类重写"""
        return self.permission_app

    def get_permission_action(self):
        """获取权限操作名称，可以被子类重写"""
        return self.permission_action

    def get_permission_denied_message(self):
        """获取权限拒绝消息，可以被子类重写"""
        app = self.get_permission_app()
        action = self.get_permission_action()
        return f'您没有权限执行此操作：{app}.{action}'

    def handle_permission_denied(self, request):
        """处理权限拒绝，可以被子类重写"""
        if self.raise_exception:
            raise PermissionDenied(self.get_permission_denied_message())
        else:
            messages.error(request, self.get_permission_denied_message())
            return redirect('products:list')

    def dispatch(self, request, *args, **kwargs):
        app = self.get_permission_app()
        action = self.get_permission_action()

        if not app or not action:
            raise ValueError("必须设置 permission_app 和 permission_action")

        # 检查登录
        if not request.user.is_authenticated:
            return self.handle_no_permission()

        start_time = time.time()
        permission_key = f"app.{app}.{action}"

        # 检查缓存
        has_permission = None
        if self.use_cache:
            has_permission = _get_cached_permission_result(request.user.id, permission_key)

        # 如果缓存中没有，进行权限检查
        if has_permission is None:
            has_permission = PermissionManager.user_has_permission(
                request.user, app, action
            )

            # 缓存结果
            if self.use_cache:
                _cache_permission_result(request.user.id, permission_key, has_permission, self.cache_timeout)

        check_time = time.time() - start_time
        _log_permission_check(request, permission_key, has_permission, check_time)

        if not has_permission:
            return self.handle_permission_denied(request)

        return super().dispatch(request, *args, **kwargs)


class PagePermissionRequiredMixin(LoginRequiredMixin):
    """页面权限检查Mixin类"""
    page_permission_name = None
    page_app_name = None
    use_cache = True
    cache_timeout = 300
    raise_exception = False
    auto_resolve_permission = True

    def get_page_permission_name(self):
        """获取页面权限名称，可以被子类重写"""
        if self.page_permission_name:
            return self.page_permission_name

        if self.auto_resolve_permission:
            # 尝试从URL解析
            try:
                resolved = resolve(self.request.path)
                return resolved.url_name
            except Exception:
                pass

        return None

    def get_page_app_name(self):
        """获取页面应用名称，可以被子类重写"""
        if self.page_app_name:
            return self.page_app_name

        if self.auto_resolve_permission:
            # 尝试从URL解析
            try:
                resolved = resolve(self.request.path)
                return resolved.app_name
            except Exception:
                pass

        return None

    def get_permission_denied_message(self):
        """获取权限拒绝消息，可以被子类重写"""
        app = self.get_page_app_name()
        permission = self.get_page_permission_name()
        return f'您没有权限访问此页面：{app}.{permission}'

    def handle_permission_denied(self, request):
        """处理权限拒绝，可以被子类重写"""
        if self.raise_exception:
            raise PermissionDenied(self.get_permission_denied_message())
        else:
            messages.error(request, self.get_permission_denied_message())
            return redirect('products:list')

    def dispatch(self, request, *args, **kwargs):
        self.request = request  # 保存request以供其他方法使用

        app = self.get_page_app_name()
        permission = self.get_page_permission_name()

        if not app or not permission:
            if not self.auto_resolve_permission:
                raise ValueError("必须设置 page_app_name 和 page_permission_name 或启用 auto_resolve_permission")
            else:
                logger.warning(f"Cannot resolve page permission for {request.path}")
                app = app or 'unknown'
                permission = permission or 'unknown'

        # 检查登录
        if not request.user.is_authenticated:
            return self.handle_no_permission()

        start_time = time.time()
        permission_key = f"page.{app}.{permission}"

        # 检查缓存
        has_permission = None
        if self.use_cache:
            has_permission = _get_cached_permission_result(request.user.id, permission_key)

        # 如果缓存中没有，进行权限检查
        if has_permission is None:
            has_permission = PermissionManager.check_page_permission(
                request.user, app, permission
            )

            # 缓存结果
            if self.use_cache:
                _cache_permission_result(request.user.id, permission_key, has_permission, self.cache_timeout)

        check_time = time.time() - start_time
        _log_permission_check(request, permission_key, has_permission, check_time)

        if not has_permission:
            return self.handle_permission_denied(request)

        return super().dispatch(request, *args, **kwargs)


class MultiPermissionRequiredMixin(LoginRequiredMixin):
    """多权限检查Mixin类"""
    required_permissions = []  # [(app_name, action), ...]
    permission_operator = 'AND'  # 'AND' 或 'OR'
    use_cache = True
    cache_timeout = 300
    raise_exception = False

    def get_required_permissions(self):
        """获取所需权限列表，可以被子类重写"""
        return self.required_permissions

    def get_permission_operator(self):
        """获取权限操作符，可以被子类重写"""
        return self.permission_operator

    def get_permission_denied_message(self):
        """获取权限拒绝消息，可以被子类重写"""
        permissions = self.get_required_permissions()
        operator = self.get_permission_operator()
        perm_strs = [f"{app}.{action}" for app, action in permissions]
        return f'您没有权限执行此操作：{f" {operator} ".join(perm_strs)}'

    def handle_permission_denied(self, request):
        """处理权限拒绝，可以被子类重写"""
        if self.raise_exception:
            raise PermissionDenied(self.get_permission_denied_message())
        else:
            messages.error(request, self.get_permission_denied_message())
            return redirect('products:list')

    def dispatch(self, request, *args, **kwargs):
        permissions = self.get_required_permissions()
        operator = self.get_permission_operator()

        if not permissions:
            raise ValueError("必须设置 required_permissions")

        # 检查登录
        if not request.user.is_authenticated:
            return self.handle_no_permission()

        start_time = time.time()
        permission_results = []
        permission_keys = []

        for app_name, action in permissions:
            permission_key = f"app.{app_name}.{action}"
            permission_keys.append(permission_key)

            # 检查缓存
            has_permission = None
            if self.use_cache:
                has_permission = _get_cached_permission_result(request.user.id, permission_key)

            # 如果缓存中没有，进行权限检查
            if has_permission is None:
                has_permission = PermissionManager.user_has_permission(
                    request.user, app_name, action
                )

                # 缓存结果
                if self.use_cache:
                    _cache_permission_result(request.user.id, permission_key, has_permission, self.cache_timeout)

            permission_results.append(has_permission)

        # 根据操作符计算最终结果
        if operator.upper() == 'AND':
            final_result = all(permission_results)
        elif operator.upper() == 'OR':
            final_result = any(permission_results)
        else:
            raise ValueError(f"Invalid operator: {operator}. Use 'AND' or 'OR'")

        check_time = time.time() - start_time
        combined_key = f"multi:{operator}:{':'.join(permission_keys)}"
        _log_permission_check(request, combined_key, final_result, check_time)

        if not final_result:
            return self.handle_permission_denied(request)

        return super().dispatch(request, *args, **kwargs)


class RoleRequiredMixin(LoginRequiredMixin):
    """增强的角色检查Mixin类"""
    allowed_roles = []
    use_cache = True
    cache_timeout = 300
    raise_exception = False

    def get_allowed_roles(self):
        """获取允许的角色列表，可以被子类重写"""
        return self.allowed_roles

    def get_permission_denied_message(self, user_role):
        """获取权限拒绝消息，可以被子类重写"""
        return f'您的角色({user_role})无权访问此功能'

    def handle_permission_denied(self, request, user_role):
        """处理权限拒绝，可以被子类重写"""
        if self.raise_exception:
            raise PermissionDenied(self.get_permission_denied_message(user_role))
        else:
            messages.error(request, self.get_permission_denied_message(user_role))
            return redirect('products:list')

    def dispatch(self, request, *args, **kwargs):
        allowed_roles = self.get_allowed_roles()

        if not allowed_roles:
            raise ValueError("必须设置 allowed_roles")

        # 检查登录
        if not request.user.is_authenticated:
            return self.handle_no_permission()

        start_time = time.time()

        # 检查角色缓存
        role_cache_key = f"user_role:{request.user.id}"
        user_role = None
        if self.use_cache:
            user_role = cache.get(role_cache_key)

        if user_role is None:
            user_role = PermissionManager.get_user_role(request.user)
            if self.use_cache:
                cache.set(role_cache_key, user_role, self.cache_timeout)

        check_time = time.time() - start_time
        _log_permission_check(request, f"role:{user_role}", user_role in allowed_roles, check_time)

        if user_role not in allowed_roles and not request.user.is_superuser:
            return self.handle_permission_denied(request, user_role)

        return super().dispatch(request, *args, **kwargs)


class AdminRequiredMixin(RoleRequiredMixin):
    """管理员权限Mixin"""
    allowed_roles = [UserRoles.SUPER_ADMIN, UserRoles.WAREHOUSE_MANAGER]


class SuperAdminRequiredMixin(RoleRequiredMixin):
    """超级管理员权限Mixin"""
    allowed_roles = [UserRoles.SUPER_ADMIN]


# 便利装饰器
def cache_permission_result(timeout: int = 300):
    """权限结果缓存装饰器"""
    def decorator(view_func):
        @wraps(view_func)
        def _wrapped_view(request, *args, **kwargs):
            if not hasattr(request.user, 'id'):
                return view_func(request, *args, **kwargs)

            # 生成缓存键
            cache_key = f"view_permission:{request.user.id}:{request.path}"

            # 检查缓存
            cached_response = cache.get(cache_key)
            if cached_response is not None:
                return cached_response

            # 执行视图
            response = view_func(request, *args, **kwargs)

            # 只缓存成功的响应
            if hasattr(response, 'status_code') and response.status_code == 200:
                cache.set(cache_key, response, timeout)

            return response
        return _wrapped_view
    return decorator


def clear_permission_cache_on_change(view_func):
    """在权限变更时清除相关缓存的装饰器"""
    @wraps(view_func)
    def _wrapped_view(request, *args, **kwargs):
        response = view_func(request, *args, **kwargs)

        # 如果是POST/PUT/DELETE请求且成功，清除权限缓存
        if (request.method in ['POST', 'PUT', 'DELETE'] and
            hasattr(response, 'status_code') and
            200 <= response.status_code < 300):

            # 清除用户权限缓存
            if hasattr(request.user, 'id'):
                cache_pattern = f"permission:{request.user.id}:*"
                # 注意：这里需要Redis支持，Django默认缓存不支持模式删除
                try:
                    from django.core.cache import cache
                    if hasattr(cache, 'delete_pattern'):
                        cache.delete_pattern(cache_pattern)
                    else:
                        # 如果不支持模式删除，清除整个用户的缓存
                        cache.delete_many([
                            f"user_role:{request.user.id}",
                            f"view_permission:{request.user.id}:{request.path}"
                        ])
                except Exception as e:
                    logger.warning(f"Failed to clear permission cache: {e}")

        return response
    return _wrapped_view


def permission_test_mode(view_func):
    """权限测试模式装饰器，用于开发和测试"""
    @wraps(view_func)
    def _wrapped_view(request, *args, **kwargs):
        if not getattr(settings, 'DEBUG', False):
            return view_func(request, *args, **kwargs)

        # 在测试模式下记录权限检查信息
        test_info = {
            'user': request.user.username if request.user.is_authenticated else 'anonymous',
            'path': request.path,
            'method': request.method,
            'timestamp': time.time()
        }

        # 将测试信息添加到响应头
        response = view_func(request, *args, **kwargs)
        if hasattr(response, '__setitem__'):
            response['X-Permission-Test-Info'] = str(test_info)

        return response
    return _wrapped_view


# 工具函数
def check_permission_sync(user, permission_key: str) -> bool:
    """同步权限检查工具函数"""
    try:
        if permission_key.startswith('page.'):
            parts = permission_key.split('.')
            if len(parts) >= 3:
                app_name = parts[1]
                permission_name = parts[2]
                return PermissionManager.check_page_permission(user, app_name, permission_name)
        elif permission_key.startswith('app.'):
            parts = permission_key.split('.')
            if len(parts) >= 3:
                app_name = parts[1]
                action = parts[2]
                return PermissionManager.user_has_permission(user, app_name, action)
        else:
            return PermissionManager.check_custom_permission(user, permission_key)
    except Exception as e:
        logger.error(f"Permission check failed for {permission_key}: {e}")
        return False

    return False


def get_user_permissions_summary(user) -> dict:
    """获取用户权限摘要"""
    try:
        role = PermissionManager.get_user_role(user)
        permissions = PermissionManager.get_user_permissions(user)

        return {
            'user_id': user.id,
            'username': user.username,
            'role': role,
            'role_display': UserRoles.get_display_name(role),
            'permissions_count': len(permissions),
            'permissions': permissions,
            'is_superuser': user.is_superuser,
            'cache_info': {
                'role_cached': cache.get(f"user_role:{user.id}") is not None,
                'permissions_cached': cache.get(f"user_permissions:{user.id}") is not None
            }
        }
    except Exception as e:
        logger.error(f"Failed to get user permissions summary: {e}")
        return {'error': str(e)}
