"""
审计工具函数模块
提供审计日志记录的工具函数
"""
import json
from django.contrib.contenttypes.models import ContentType
from django.core.serializers.json import DjangoJSONEncoder


def get_client_ip(request):
    """获取客户端IP地址"""
    if not request:
        return None
        
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0].strip()
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip


def get_user_agent(request):
    """获取用户代理信息"""
    if not request:
        return ''
    return request.META.get('HTTP_USER_AGENT', '')


def get_model_fields_dict(instance, exclude_fields=None):
    """
    获取模型实例的字段字典
    
    Args:
        instance: 模型实例
        exclude_fields: 要排除的字段列表
    
    Returns:
        dict: 字段名和值的字典
    """
    if exclude_fields is None:
        exclude_fields = ['id', 'created_at', 'updated_at']
    
    fields_dict = {}
    
    for field in instance._meta.fields:
        if field.name not in exclude_fields:
            value = getattr(instance, field.name)
            
            # 处理外键字段
            if hasattr(field, 'related_model') and value:
                try:
                    related_obj = getattr(instance, field.name)
                    fields_dict[field.name] = str(related_obj)
                except:
                    fields_dict[field.name] = str(value)
            else:
                # 处理特殊类型的字段
                if hasattr(value, 'isoformat'):  # 日期时间字段
                    fields_dict[field.name] = value.isoformat()
                elif isinstance(value, (dict, list)):  # JSON字段
                    fields_dict[field.name] = value
                else:
                    fields_dict[field.name] = str(value) if value is not None else None
    
    return fields_dict


def calculate_field_changes(old_instance, new_instance, exclude_fields=None):
    """
    计算两个模型实例之间的字段变更
    
    Args:
        old_instance: 旧的模型实例
        new_instance: 新的模型实例
        exclude_fields: 要排除的字段列表
    
    Returns:
        dict: 变更详情字典
    """
    if exclude_fields is None:
        exclude_fields = ['id', 'created_at', 'updated_at']
    
    old_fields = get_model_fields_dict(old_instance, exclude_fields)
    new_fields = get_model_fields_dict(new_instance, exclude_fields)
    
    changes = {}
    
    # 检查每个字段的变更
    for field_name in old_fields.keys():
        old_value = old_fields.get(field_name)
        new_value = new_fields.get(field_name)
        
        if old_value != new_value:
            changes[field_name] = {
                'old': old_value,
                'new': new_value
            }
    
    return changes


def serialize_for_audit(obj):
    """
    序列化对象用于审计日志
    
    Args:
        obj: 要序列化的对象
    
    Returns:
        str: JSON字符串
    """
    try:
        return json.dumps(obj, cls=DjangoJSONEncoder, ensure_ascii=False)
    except (TypeError, ValueError):
        return str(obj)


def get_content_type_for_model(model_class):
    """
    获取模型的ContentType
    
    Args:
        model_class: 模型类
    
    Returns:
        ContentType: 内容类型对象
    """
    return ContentType.objects.get_for_model(model_class)


def format_audit_message(action, model_name, instance_repr, user=None):
    """
    格式化审计消息
    
    Args:
        action: 操作类型
        model_name: 模型名称
        instance_repr: 实例字符串表示
        user: 操作用户
    
    Returns:
        str: 格式化的消息
    """
    user_name = user.username if user else '系统'
    action_map = {
        'CREATE': '创建',
        'UPDATE': '更新',
        'DELETE': '删除',
        'VIEW': '查看'
    }
    action_text = action_map.get(action, action)
    
    return f"{user_name} {action_text} {model_name}: {instance_repr}"


def is_significant_change(changes, significant_fields=None):
    """
    判断是否为重要变更
    
    Args:
        changes: 变更字典
        significant_fields: 重要字段列表
    
    Returns:
        bool: 是否为重要变更
    """
    if not changes:
        return False
    
    if significant_fields is None:
        # 默认所有变更都是重要的
        return True
    
    # 检查是否有重要字段发生变更
    for field_name in changes.keys():
        if field_name in significant_fields:
            return True
    
    return False


def clean_sensitive_data(data, sensitive_fields=None):
    """
    清理敏感数据
    
    Args:
        data: 数据字典
        sensitive_fields: 敏感字段列表
    
    Returns:
        dict: 清理后的数据
    """
    if sensitive_fields is None:
        sensitive_fields = ['password', 'token', 'secret', 'key']
    
    if not isinstance(data, dict):
        return data
    
    cleaned_data = data.copy()
    
    for field_name in sensitive_fields:
        if field_name in cleaned_data:
            cleaned_data[field_name] = '***'
    
    return cleaned_data


def get_request_context(request):
    """
    获取请求上下文信息
    
    Args:
        request: Django请求对象
    
    Returns:
        dict: 请求上下文字典
    """
    if not request:
        return {}
    
    context = {
        'method': request.method,
        'path': request.path,
        'ip_address': get_client_ip(request),
        'user_agent': get_user_agent(request),
    }
    
    # 添加查询参数
    if request.GET:
        context['query_params'] = dict(request.GET)
    
    # 添加POST数据（排除敏感信息）
    if request.method in ['POST', 'PUT', 'PATCH'] and hasattr(request, 'POST'):
        post_data = dict(request.POST)
        context['post_data'] = clean_sensitive_data(post_data)
    
    return context
