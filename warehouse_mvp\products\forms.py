from django import forms
from .models import Product

class ProductForm(forms.ModelForm):
    """商品表单"""
    
    class Meta:
        model = Product
        fields = ['name', 'description', 'price']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '请输入商品名称'
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': '请输入商品描述'
            }),
            'price': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0',
                'placeholder': '请输入价格'
            })
        }
        labels = {
            'name': '商品名称',
            'description': '商品描述',
            'price': '价格'
        }
    

    def clean_price(self):
        """验证价格"""
        price = self.cleaned_data.get('price')
        if price is not None and price < 0:
            raise forms.ValidationError('价格不能为负数。')
        return price
