from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.db import transaction
from users.models import UserProfile
from products.models import Product
from inventory.models import Inventory
from transactions.models import Transaction
import random
from decimal import Decimal

class Command(BaseCommand):
    help = '创建测试数据，包括用户、商品、库存和交易记录'

    def add_arguments(self, parser):
        parser.add_argument(
            '--users',
            type=int,
            default=5,
            help='创建的用户数量（默认5个）'
        )
        parser.add_argument(
            '--products',
            type=int,
            default=20,
            help='创建的商品数量（默认20个）'
        )
        parser.add_argument(
            '--transactions',
            type=int,
            default=50,
            help='创建的交易记录数量（默认50个）'
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('开始创建测试数据...'))
        
        try:
            with transaction.atomic():
                # 创建测试用户
                self.create_users(options['users'])
                
                # 创建测试商品
                self.create_products(options['products'])
                
                # 创建测试交易记录
                self.create_transactions(options['transactions'])
                
                self.stdout.write(self.style.SUCCESS('测试数据创建完成！'))
                self.print_summary()
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'创建测试数据失败：{str(e)}'))

    def create_users(self, count):
        """创建测试用户"""
        self.stdout.write('创建测试用户...')
        
        # 确保admin用户存在并有UserProfile
        admin_user, created = User.objects.get_or_create(
            username='admin',
            defaults={
                'email': '<EMAIL>',
                'first_name': '系统',
                'last_name': '管理员',
                'is_staff': True,
                'is_superuser': True
            }
        )
        if created:
            admin_user.set_password('admin123')
            admin_user.save()
        
        admin_profile, created = UserProfile.objects.get_or_create(
            user=admin_user,
            defaults={'role': 'ADMIN'}
        )
        
        # 创建测试用户
        user_data = [
            ('manager1', '仓库', '经理', '<EMAIL>', 'ADMIN'),
            ('user1', '张', '三', '<EMAIL>', 'USER'),
            ('user2', '李', '四', '<EMAIL>', 'USER'),
            ('user3', '王', '五', '<EMAIL>', 'USER'),
            ('operator1', '操作', '员', '<EMAIL>', 'USER'),
        ]
        
        for i, (username, first_name, last_name, email, role) in enumerate(user_data[:count]):
            user, created = User.objects.get_or_create(
                username=username,
                defaults={
                    'email': email,
                    'first_name': first_name,
                    'last_name': last_name,
                    'is_active': True
                }
            )
            if created:
                user.set_password('password123')
                user.save()
                
                UserProfile.objects.get_or_create(
                    user=user,
                    defaults={'role': role}
                )
                
        self.stdout.write(f'  创建了 {count + 1} 个用户（包括admin）')

    def create_products(self, count):
        """创建测试商品"""
        self.stdout.write('创建测试商品...')
        
        product_data = [
            ('苹果手机', 'PHONE001', '最新款智能手机', 5999.00),
            ('笔记本电脑', 'LAPTOP001', '高性能办公笔记本', 8999.00),
            ('无线耳机', 'EARPHONE001', '蓝牙无线耳机', 299.00),
            ('智能手表', 'WATCH001', '运动健康手表', 1999.00),
            ('平板电脑', 'TABLET001', '10寸高清平板', 2999.00),
            ('充电宝', 'POWERBANK001', '20000mAh大容量', 199.00),
            ('数据线', 'CABLE001', 'Type-C数据线', 29.00),
            ('手机壳', 'CASE001', '透明防摔手机壳', 39.00),
            ('蓝牙音箱', 'SPEAKER001', '便携式蓝牙音箱', 399.00),
            ('键盘', 'KEYBOARD001', '机械键盘', 599.00),
            ('鼠标', 'MOUSE001', '无线鼠标', 199.00),
            ('显示器', 'MONITOR001', '27寸4K显示器', 2999.00),
            ('摄像头', 'CAMERA001', '高清网络摄像头', 299.00),
            ('路由器', 'ROUTER001', '千兆无线路由器', 399.00),
            ('硬盘', 'HDD001', '1TB移动硬盘', 499.00),
            ('内存卡', 'SDCARD001', '128GB高速内存卡', 199.00),
            ('打印机', 'PRINTER001', '激光打印机', 1299.00),
            ('扫描仪', 'SCANNER001', '高速文档扫描仪', 899.00),
            ('投影仪', 'PROJECTOR001', '便携式投影仪', 2999.00),
            ('游戏手柄', 'GAMEPAD001', '无线游戏手柄', 299.00),
        ]
        
        for i, (name, sku, description, price) in enumerate(product_data[:count]):
            product, created = Product.objects.get_or_create(
                sku=sku,
                defaults={
                    'name': name,
                    'description': description,
                    'price': Decimal(str(price)),
                    'is_active': True
                }
            )
            
            if created:
                # 为每个商品创建初始库存
                initial_quantity = random.randint(10, 100)
                inventory, inv_created = Inventory.objects.get_or_create(
                    product=product,
                    defaults={
                        'quantity': initial_quantity,
                        'min_stock': random.randint(5, 20),
                        'max_stock': random.randint(200, 500)
                    }
                )
                
        self.stdout.write(f'  创建了 {count} 个商品及其库存记录')

    def create_transactions(self, count):
        """创建测试交易记录"""
        self.stdout.write('创建测试交易记录...')
        
        products = list(Product.objects.all())
        users = list(User.objects.all())
        
        if not products or not users:
            self.stdout.write(self.style.WARNING('  没有商品或用户，跳过交易记录创建'))
            return
        
        transaction_reasons = {
            'IN': ['采购入库', '退货入库', '调拨入库', '盘盈入库', '生产入库'],
            'OUT': ['销售出库', '调拨出库', '报损出库', '盘亏出库', '生产出库']
        }
        
        created_count = 0
        for i in range(count):
            product = random.choice(products)
            user = random.choice(users)
            transaction_type = random.choice(['IN', 'OUT'])
            
            # 获取当前库存
            try:
                inventory = Inventory.objects.get(product=product)
                current_stock = inventory.quantity
                
                if transaction_type == 'OUT' and current_stock <= 0:
                    # 如果是出库但库存为0，改为入库
                    transaction_type = 'IN'
                
                if transaction_type == 'IN':
                    quantity = random.randint(1, 50)
                else:  # OUT
                    # 出库数量不超过当前库存
                    max_out = min(current_stock, 30)
                    if max_out <= 0:
                        continue
                    quantity = random.randint(1, max_out)
                
                reason = random.choice(transaction_reasons[transaction_type])
                
                # 创建交易记录
                trans = Transaction.objects.create(
                    product=product,
                    transaction_type=transaction_type,
                    quantity=quantity,
                    reason=reason,
                    operator=user,
                    notes=f'测试数据 - {reason}'
                )
                
                # 更新库存
                if transaction_type == 'IN':
                    inventory.quantity += quantity
                else:
                    inventory.quantity -= quantity
                inventory.save()
                
                created_count += 1
                
            except Inventory.DoesNotExist:
                continue
        
        self.stdout.write(f'  创建了 {created_count} 条交易记录')

    def print_summary(self):
        """打印数据摘要"""
        self.stdout.write('\n' + '='*50)
        self.stdout.write(self.style.SUCCESS('测试数据创建摘要：'))
        self.stdout.write('='*50)
        
        user_count = User.objects.count()
        admin_count = UserProfile.objects.filter(role='ADMIN').count()
        user_normal_count = UserProfile.objects.filter(role='USER').count()
        
        product_count = Product.objects.count()
        active_product_count = Product.objects.filter(is_active=True).count()
        
        inventory_count = Inventory.objects.count()
        low_stock_count = sum(1 for inv in Inventory.objects.all() if inv.is_low_stock)
        
        transaction_count = Transaction.objects.count()
        in_count = Transaction.objects.filter(transaction_type='IN').count()
        out_count = Transaction.objects.filter(transaction_type='OUT').count()
        
        self.stdout.write(f'用户总数: {user_count} (管理员: {admin_count}, 普通用户: {user_normal_count})')
        self.stdout.write(f'商品总数: {product_count} (启用: {active_product_count})')
        self.stdout.write(f'库存记录: {inventory_count} (低库存: {low_stock_count})')
        self.stdout.write(f'交易记录: {transaction_count} (入库: {in_count}, 出库: {out_count})')
        
        self.stdout.write('\n默认登录账户:')
        self.stdout.write('  管理员: admin / admin123')
        self.stdout.write('  普通用户: user1 / password123')
        
        self.stdout.write('\n' + '='*50)
