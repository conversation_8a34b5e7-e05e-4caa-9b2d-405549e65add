from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.contrib.auth.models import User
from django.contrib import messages
from django.shortcuts import render, redirect
from django.urls import path, reverse
from django.utils.html import format_html
from django.utils.safestring import mark_safe
from django.http import JsonResponse, HttpResponseRedirect
from django.db import transaction
from django.core.exceptions import ValidationError
import json
from .models import (
    UserProfile, AuditLog, PagePermission, RolePagePermission,
    CustomPermission, PermissionTemplate
)
from .permissions import UserRoles, PermissionManager

# Register your models here.

# 内联用户资料
class UserProfileInline(admin.StackedInline):
    model = UserProfile
    can_delete = False
    verbose_name_plural = '用户资料'
    readonly_fields = ['created_at', 'updated_at']

# 扩展用户管理
class UserAdmin(BaseUserAdmin):
    inlines = (UserProfileInline,)
    list_display = BaseUserAdmin.list_display + ('get_role', 'get_role_display')
    list_filter = BaseUserAdmin.list_filter + ('userprofile__role',)

    def get_role(self, obj):
        try:
            return obj.userprofile.role
        except UserProfile.DoesNotExist:
            return '-'
    get_role.short_description = '角色代码'

    def get_role_display(self, obj):
        try:
            return obj.userprofile.get_role_display_name()
        except UserProfile.DoesNotExist:
            return '-'
    get_role_display.short_description = '角色名称'

# 重新注册User模型
admin.site.unregister(User)
admin.site.register(User, UserAdmin)

@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    list_display = ['user', 'get_role_display_name', 'created_at', 'updated_at']
    list_filter = ['role', 'created_at']
    search_fields = ['user__username', 'user__email']
    readonly_fields = ['created_at', 'updated_at']

    def get_role_display_name(self, obj):
        return obj.get_role_display_name()
    get_role_display_name.short_description = '角色名称'


@admin.register(AuditLog)
class AuditLogAdmin(admin.ModelAdmin):
    """审计日志管理"""
    list_display = [
        'timestamp', 'user', 'action', 'content_type',
        'object_repr', 'ip_address', 'get_changes_summary'
    ]
    list_filter = ['action', 'content_type', 'timestamp']
    search_fields = ['user__username', 'object_repr', 'ip_address']
    readonly_fields = [
        'timestamp', 'user', 'action', 'content_type', 'object_id',
        'object_repr', 'changes', 'ip_address', 'user_agent', 'extra_data'
    ]
    date_hierarchy = 'timestamp'
    ordering = ['-timestamp']

    def get_changes_summary(self, obj):
        """获取变更摘要"""
        return obj.get_changes_display()[:100] + "..." if len(obj.get_changes_display()) > 100 else obj.get_changes_display()
    get_changes_summary.short_description = '变更摘要'

    def has_add_permission(self, request):
        """禁止手动添加审计日志"""
        return False

    def has_change_permission(self, request, obj=None):
        """禁止修改审计日志"""
        return False

    def has_delete_permission(self, request, obj=None):
        """禁止删除审计日志"""
        return False


@admin.register(PagePermission)
class PagePermissionAdmin(admin.ModelAdmin):
    """页面权限管理"""
    list_display = [
        'name', 'app_name', 'category', 'permission_level',
        'is_active', 'is_system', 'sort_order', 'created_at'
    ]
    list_filter = [
        'app_name', 'category', 'permission_level', 'is_active', 'is_system'
    ]
    search_fields = ['name', 'description', 'url_pattern', 'app_name']
    readonly_fields = ['created_at', 'updated_at', 'created_by', 'updated_by']
    ordering = ['category', 'sort_order', 'name']

    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'description', 'category')
        }),
        ('URL配置', {
            'fields': ('url_pattern', 'url_name', 'app_name', 'view_name')
        }),
        ('权限设置', {
            'fields': ('permission_level', 'is_active', 'is_system', 'sort_order', 'priority')
        }),
        ('审计信息', {
            'fields': ('created_at', 'updated_at', 'created_by', 'updated_by'),
            'classes': ('collapse',)
        })
    )

    actions = ['activate_permissions', 'deactivate_permissions', 'test_url_patterns']

    def activate_permissions(self, request, queryset):
        """批量激活权限"""
        updated = queryset.update(is_active=True)
        self.message_user(request, f'成功激活 {updated} 个权限')
    activate_permissions.short_description = '激活选中的权限'

    def deactivate_permissions(self, request, queryset):
        """批量停用权限"""
        updated = queryset.update(is_active=False)
        self.message_user(request, f'成功停用 {updated} 个权限')
    deactivate_permissions.short_description = '停用选中的权限'

    def test_url_patterns(self, request, queryset):
        """测试URL模式"""
        invalid_patterns = []
        for permission in queryset:
            try:
                import re
                re.compile(permission.url_pattern)
            except re.error:
                invalid_patterns.append(permission.name)

        if invalid_patterns:
            self.message_user(
                request,
                f'发现无效的URL模式: {", ".join(invalid_patterns)}',
                level=messages.ERROR
            )
        else:
            self.message_user(request, '所有URL模式都有效')
    test_url_patterns.short_description = '测试URL模式有效性'

    def save_model(self, request, obj, form, change):
        """保存时记录操作人"""
        if not change:
            obj.created_by = request.user
        obj.updated_by = request.user
        super().save_model(request, obj, form, change)

        # 记录权限变更审计日志
        PermissionManager.log_permission_change(
            user=request.user,
            action='page_permission_change',
            permission_data={
                'permission_name': obj.name,
                'app_name': obj.app_name,
                'is_active': obj.is_active,
                'action_type': 'create' if not change else 'update'
            },
            operator=request.user,
            request=request
        )


@admin.register(RolePagePermission)
class RolePagePermissionAdmin(admin.ModelAdmin):
    """角色页面权限管理"""
    list_display = [
        'get_role_display', 'get_permission_name', 'get_app_name',
        'is_granted', 'grant_source', 'is_valid_permission', 'granted_at'
    ]
    list_filter = [
        'role', 'is_granted', 'grant_source',
        'page_permission__app_name', 'page_permission__category'
    ]
    search_fields = [
        'page_permission__name', 'page_permission__description',
        'page_permission__app_name', 'notes'
    ]
    readonly_fields = ['granted_at', 'granted_by']
    ordering = ['role', 'page_permission__category', 'page_permission__sort_order']

    fieldsets = (
        ('权限分配', {
            'fields': ('role', 'page_permission', 'is_granted', 'grant_source')
        }),
        ('有效期设置', {
            'fields': ('valid_from', 'valid_until'),
            'classes': ('collapse',)
        }),
        ('备注信息', {
            'fields': ('notes',)
        }),
        ('审计信息', {
            'fields': ('granted_at', 'granted_by'),
            'classes': ('collapse',)
        })
    )

    actions = [
        'grant_permissions', 'revoke_permissions', 'apply_permission_template',
        'check_permission_conflicts'
    ]

    def get_role_display(self, obj):
        """获取角色显示名称"""
        return UserRoles.get_display_name(obj.role)
    get_role_display.short_description = '角色'

    def get_permission_name(self, obj):
        """获取权限名称"""
        return obj.page_permission.name
    get_permission_name.short_description = '权限名称'

    def get_app_name(self, obj):
        """获取应用名称"""
        return obj.page_permission.app_name
    get_app_name.short_description = '应用'

    def is_valid_permission(self, obj):
        """检查权限是否有效"""
        is_valid = obj.is_valid()
        if is_valid:
            return format_html('<span style="color: green;">✓ 有效</span>')
        else:
            return format_html('<span style="color: red;">✗ 无效</span>')
    is_valid_permission.short_description = '权限状态'

    def grant_permissions(self, request, queryset):
        """批量授权"""
        updated = queryset.update(is_granted=True)
        self.message_user(request, f'成功授权 {updated} 个权限')

        # 清除相关用户权限缓存
        for role_perm in queryset:
            PermissionManager._clear_role_users_cache(role_perm.role)
    grant_permissions.short_description = '授权选中的权限'

    def revoke_permissions(self, request, queryset):
        """批量撤销权限"""
        updated = queryset.update(is_granted=False)
        self.message_user(request, f'成功撤销 {updated} 个权限')

        # 清除相关用户权限缓存
        for role_perm in queryset:
            PermissionManager._clear_role_users_cache(role_perm.role)
    revoke_permissions.short_description = '撤销选中的权限'

    def apply_permission_template(self, request, queryset):
        """应用权限模板"""
        if 'apply' in request.POST:
            template_id = request.POST.get('template_id')
            if template_id:
                try:
                    template = PermissionTemplate.objects.get(id=template_id)
                    roles = set(queryset.values_list('role', flat=True))

                    applied_count = 0
                    for role in roles:
                        applied_permissions = template.apply_to_role(role, request.user)
                        applied_count += len(applied_permissions)

                    self.message_user(
                        request,
                        f'成功应用模板 "{template.name}"，共分配 {applied_count} 个权限'
                    )
                    return HttpResponseRedirect(request.get_full_path())
                except PermissionTemplate.DoesNotExist:
                    self.message_user(request, '权限模板不存在', level=messages.ERROR)

        # 显示模板选择页面
        templates = PermissionTemplate.objects.filter(is_active=True)
        context = {
            'title': '应用权限模板',
            'queryset': queryset,
            'templates': templates,
            'action_checkbox_name': admin.ACTION_CHECKBOX_NAME,
        }
        return render(request, 'admin/users/apply_permission_template.html', context)
    apply_permission_template.short_description = '应用权限模板'

    def check_permission_conflicts(self, request, queryset):
        """检查权限冲突"""
        conflicts = []
        for role_perm in queryset:
            # 检查是否有用户自定义权限与此角色权限冲突
            custom_perms = CustomPermission.objects.filter(
                permission_key__contains=role_perm.page_permission.get_full_permission_key(),
                is_granted__isnull=False
            )

            for custom_perm in custom_perms:
                if custom_perm.is_valid() and custom_perm.is_granted != role_perm.is_granted:
                    conflicts.append({
                        'role_permission': role_perm,
                        'custom_permission': custom_perm,
                        'user': custom_perm.user
                    })

        if conflicts:
            conflict_messages = []
            for conflict in conflicts:
                conflict_messages.append(
                    f"用户 {conflict['user'].username} 的自定义权限与角色权限冲突"
                )
            self.message_user(
                request,
                f"发现 {len(conflicts)} 个权限冲突: " + "; ".join(conflict_messages),
                level=messages.WARNING
            )
        else:
            self.message_user(request, '未发现权限冲突')
    check_permission_conflicts.short_description = '检查权限冲突'

    def save_model(self, request, obj, form, change):
        """保存时记录操作人"""
        if not change:
            obj.granted_by = request.user
        super().save_model(request, obj, form, change)

        # 清除相关用户权限缓存
        PermissionManager._clear_role_users_cache(obj.role)

        # 记录权限变更审计日志
        PermissionManager.log_permission_change(
            user=request.user,
            action='role_permission_change',
            permission_data={
                'role': obj.role,
                'permission_name': obj.page_permission.name,
                'is_granted': obj.is_granted,
                'action_type': 'create' if not change else 'update'
            },
            operator=request.user,
            request=request
        )


@admin.register(CustomPermission)
class CustomPermissionAdmin(admin.ModelAdmin):
    """用户自定义权限管理"""
    list_display = [
        'user', 'permission_type', 'permission_key', 'is_granted',
        'priority', 'is_valid_permission', 'grant_reason', 'granted_at'
    ]
    list_filter = [
        'permission_type', 'is_granted', 'grant_reason',
        'user__userprofile__role'
    ]
    search_fields = [
        'user__username', 'user__email', 'permission_key',
        'description', 'notes'
    ]
    readonly_fields = ['granted_at', 'granted_by']
    ordering = ['-priority', '-granted_at']

    fieldsets = (
        ('用户和权限', {
            'fields': ('user', 'permission_type', 'permission_key', 'permission_value')
        }),
        ('权限设置', {
            'fields': ('is_granted', 'priority', 'grant_reason')
        }),
        ('有效期设置', {
            'fields': ('valid_from', 'valid_until'),
            'classes': ('collapse',)
        }),
        ('描述信息', {
            'fields': ('description', 'notes')
        }),
        ('审计信息', {
            'fields': ('granted_at', 'granted_by'),
            'classes': ('collapse',)
        })
    )

    actions = [
        'grant_permissions', 'revoke_permissions', 'check_conflicts',
        'cleanup_expired_permissions'
    ]

    def is_valid_permission(self, obj):
        """检查权限是否有效"""
        is_valid = obj.is_valid()
        if is_valid:
            return format_html('<span style="color: green;">✓ 有效</span>')
        else:
            return format_html('<span style="color: red;">✗ 无效</span>')
    is_valid_permission.short_description = '权限状态'

    def grant_permissions(self, request, queryset):
        """批量授权"""
        updated = queryset.update(is_granted=True)
        self.message_user(request, f'成功授权 {updated} 个自定义权限')

        # 清除相关用户权限缓存
        for custom_perm in queryset:
            PermissionManager.clear_user_permission_cache(custom_perm.user)
    grant_permissions.short_description = '授权选中的权限'

    def revoke_permissions(self, request, queryset):
        """批量撤销权限"""
        updated = queryset.update(is_granted=False)
        self.message_user(request, f'成功撤销 {updated} 个自定义权限')

        # 清除相关用户权限缓存
        for custom_perm in queryset:
            PermissionManager.clear_user_permission_cache(custom_perm.user)
    revoke_permissions.short_description = '撤销选中的权限'

    def check_conflicts(self, request, queryset):
        """检查权限冲突"""
        conflicts_found = 0
        for custom_perm in queryset:
            conflicts = PermissionManager.detect_permission_conflicts(custom_perm.user)
            conflicts_found += len(conflicts)

        if conflicts_found > 0:
            self.message_user(
                request,
                f'发现 {conflicts_found} 个权限冲突，请检查用户权限设置',
                level=messages.WARNING
            )
        else:
            self.message_user(request, '未发现权限冲突')
    check_conflicts.short_description = '检查权限冲突'

    def cleanup_expired_permissions(self, request, queryset):
        """清理过期权限"""
        from django.utils import timezone
        now = timezone.now()

        expired_count = queryset.filter(
            valid_until__lt=now
        ).update(is_granted=False)

        self.message_user(request, f'清理了 {expired_count} 个过期权限')
    cleanup_expired_permissions.short_description = '清理过期权限'

    def save_model(self, request, obj, form, change):
        """保存时记录操作人"""
        if not change:
            obj.granted_by = request.user
        super().save_model(request, obj, form, change)

        # 清除用户权限缓存
        PermissionManager.clear_user_permission_cache(obj.user)

        # 记录权限变更审计日志
        PermissionManager.log_permission_change(
            user=obj.user,
            action='custom_permission_change',
            permission_data={
                'permission_key': obj.permission_key,
                'permission_type': obj.permission_type,
                'is_granted': obj.is_granted,
                'priority': obj.priority,
                'action_type': 'create' if not change else 'update'
            },
            operator=request.user,
            request=request
        )


@admin.register(PermissionTemplate)
class PermissionTemplateAdmin(admin.ModelAdmin):
    """权限模板管理"""
    list_display = [
        'name', 'category', 'get_target_roles', 'usage_count',
        'is_active', 'is_system', 'last_used', 'created_at'
    ]
    list_filter = ['category', 'is_active', 'is_system']
    search_fields = ['name', 'description']
    readonly_fields = ['usage_count', 'last_used', 'created_at', 'updated_at', 'created_by']
    ordering = ['category', 'name']

    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'description', 'category')
        }),
        ('适用范围', {
            'fields': ('target_roles',)
        }),
        ('权限配置', {
            'fields': ('permissions_config',),
            'description': '权限配置JSON格式，例如: {"page.products.list": {"granted": true}}'
        }),
        ('模板设置', {
            'fields': ('is_active', 'is_system')
        }),
        ('使用统计', {
            'fields': ('usage_count', 'last_used'),
            'classes': ('collapse',)
        }),
        ('审计信息', {
            'fields': ('created_at', 'updated_at', 'created_by'),
            'classes': ('collapse',)
        })
    )

    actions = [
        'activate_templates', 'deactivate_templates', 'apply_to_roles',
        'validate_template_config'
    ]

    def get_target_roles(self, obj):
        """获取适用角色显示"""
        if not obj.target_roles:
            return '无'

        role_names = []
        for role_code in obj.target_roles:
            role_names.append(UserRoles.get_display_name(role_code))

        return ', '.join(role_names)
    get_target_roles.short_description = '适用角色'

    def activate_templates(self, request, queryset):
        """批量激活模板"""
        updated = queryset.update(is_active=True)
        self.message_user(request, f'成功激活 {updated} 个权限模板')
    activate_templates.short_description = '激活选中的模板'

    def deactivate_templates(self, request, queryset):
        """批量停用模板"""
        updated = queryset.update(is_active=False)
        self.message_user(request, f'成功停用 {updated} 个权限模板')
    deactivate_templates.short_description = '停用选中的模板'

    def apply_to_roles(self, request, queryset):
        """应用模板到角色"""
        if 'apply' in request.POST:
            role_code = request.POST.get('role_code')
            if role_code:
                applied_count = 0
                for template in queryset:
                    if template.is_active:
                        applied_permissions = template.apply_to_role(role_code, request.user)
                        applied_count += len(applied_permissions)

                role_name = UserRoles.get_display_name(role_code)
                self.message_user(
                    request,
                    f'成功将 {queryset.count()} 个模板应用到角色 "{role_name}"，共分配 {applied_count} 个权限'
                )
                return HttpResponseRedirect(request.get_full_path())

        # 显示角色选择页面
        context = {
            'title': '应用模板到角色',
            'queryset': queryset,
            'roles': UserRoles.CHOICES,
            'action_checkbox_name': admin.ACTION_CHECKBOX_NAME,
        }
        return render(request, 'admin/users/apply_template_to_roles.html', context)
    apply_to_roles.short_description = '应用模板到角色'

    def validate_template_config(self, request, queryset):
        """验证模板配置"""
        invalid_templates = []
        for template in queryset:
            try:
                # 验证JSON格式
                if not isinstance(template.permissions_config, dict):
                    invalid_templates.append(f"{template.name}: 配置不是有效的字典格式")
                    continue

                # 验证权限键格式
                for permission_key, config in template.permissions_config.items():
                    if not permission_key.startswith('page.'):
                        invalid_templates.append(f"{template.name}: 权限键 '{permission_key}' 格式无效")
                        break

                    if not isinstance(config, dict) or 'granted' not in config:
                        invalid_templates.append(f"{template.name}: 权限配置 '{permission_key}' 缺少 'granted' 字段")
                        break

            except Exception as e:
                invalid_templates.append(f"{template.name}: {str(e)}")

        if invalid_templates:
            self.message_user(
                request,
                f'发现配置错误: {"; ".join(invalid_templates)}',
                level=messages.ERROR
            )
        else:
            self.message_user(request, '所有模板配置都有效')
    validate_template_config.short_description = '验证模板配置'

    def save_model(self, request, obj, form, change):
        """保存时记录操作人"""
        if not change:
            obj.created_by = request.user
        super().save_model(request, obj, form, change)
