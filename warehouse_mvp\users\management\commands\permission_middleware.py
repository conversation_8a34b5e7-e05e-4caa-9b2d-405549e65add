"""
权限中间件管理命令
用于监控、管理和调试页面权限中间件
"""
from django.core.management.base import BaseCommand
from django.core.cache import cache
from django.contrib.auth.models import User
from django.apps import apps
from django.conf import settings
import json
import time


class Command(BaseCommand):
    help = '管理页面权限中间件'

    def add_arguments(self, parser):
        parser.add_argument(
            'action',
            choices=['stats', 'clear-cache', 'test-user', 'validate-config'],
            help='要执行的操作'
        )
        
        parser.add_argument(
            '--user-id',
            type=int,
            help='用户ID（用于clear-cache和test-user操作）'
        )
        
        parser.add_argument(
            '--username',
            type=str,
            help='用户名（用于test-user操作）'
        )
        
        parser.add_argument(
            '--path',
            type=str,
            help='测试路径（用于test-user操作）'
        )
        
        parser.add_argument(
            '--format',
            choices=['table', 'json'],
            default='table',
            help='输出格式'
        )

    def handle(self, *args, **options):
        action = options['action']
        
        if action == 'stats':
            self.show_stats(options['format'])
        elif action == 'clear-cache':
            self.clear_cache(options.get('user_id'))
        elif action == 'test-user':
            self.test_user_permission(options)
        elif action == 'validate-config':
            self.validate_config()

    def show_stats(self, format_type):
        """显示中间件统计信息"""
        try:
            # 尝试获取中间件实例的统计信息
            from users.middleware import PagePermissionMiddleware
            
            # 由于中间件是在请求处理过程中实例化的，我们需要模拟获取统计信息
            stats = {
                'cache_info': self._get_cache_info(),
                'middleware_config': self._get_middleware_config(),
                'permission_cache_keys': self._get_permission_cache_keys()
            }
            
            if format_type == 'json':
                self.stdout.write(json.dumps(stats, indent=2, ensure_ascii=False))
            else:
                self._print_stats_table(stats)
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'获取统计信息失败: {e}')
            )

    def _get_cache_info(self):
        """获取缓存信息"""
        try:
            # 获取权限相关的缓存键数量
            permission_keys = 0
            if hasattr(cache, 'keys'):
                all_keys = cache.keys('page_perm:*')
                permission_keys = len(all_keys) if all_keys else 0
            
            return {
                'permission_cache_keys': permission_keys,
                'cache_backend': str(type(cache)),
                'cache_timeout': getattr(settings, 'PAGE_PERMISSION_CACHE_TIMEOUT', 300)
            }
        except Exception as e:
            return {'error': str(e)}

    def _get_middleware_config(self):
        """获取中间件配置"""
        return {
            'cache_enabled': getattr(settings, 'PAGE_PERMISSION_ENABLE_CACHE', True),
            'cache_timeout': getattr(settings, 'PAGE_PERMISSION_CACHE_TIMEOUT', 300),
            'debug_mode': getattr(settings, 'DEBUG', False),
            'middleware_installed': 'users.middleware.PagePermissionMiddleware' in settings.MIDDLEWARE
        }

    def _get_permission_cache_keys(self):
        """获取权限缓存键列表"""
        try:
            if hasattr(cache, 'keys'):
                keys = cache.keys('page_perm:*')
                return list(keys)[:10] if keys else []  # 只显示前10个
            return []
        except Exception:
            return []

    def _print_stats_table(self, stats):
        """以表格形式打印统计信息"""
        self.stdout.write(self.style.SUCCESS('=== 页面权限中间件统计信息 ==='))
        
        # 缓存信息
        cache_info = stats.get('cache_info', {})
        self.stdout.write('\n📊 缓存信息:')
        self.stdout.write(f'  权限缓存键数量: {cache_info.get("permission_cache_keys", "N/A")}')
        self.stdout.write(f'  缓存后端: {cache_info.get("cache_backend", "N/A")}')
        self.stdout.write(f'  缓存超时: {cache_info.get("cache_timeout", "N/A")}秒')
        
        # 中间件配置
        config = stats.get('middleware_config', {})
        self.stdout.write('\n⚙️ 中间件配置:')
        self.stdout.write(f'  缓存启用: {"✅" if config.get("cache_enabled") else "❌"}')
        self.stdout.write(f'  调试模式: {"✅" if config.get("debug_mode") else "❌"}')
        self.stdout.write(f'  中间件已安装: {"✅" if config.get("middleware_installed") else "❌"}')
        
        # 缓存键示例
        cache_keys = stats.get('permission_cache_keys', [])
        if cache_keys:
            self.stdout.write('\n🔑 缓存键示例:')
            for key in cache_keys:
                self.stdout.write(f'  {key}')

    def clear_cache(self, user_id=None):
        """清除权限缓存"""
        try:
            if user_id:
                # 清除特定用户的缓存
                pattern = f'page_perm:{user_id}:*'
                if hasattr(cache, 'delete_pattern'):
                    deleted = cache.delete_pattern(pattern)
                    self.stdout.write(
                        self.style.SUCCESS(f'已清除用户 {user_id} 的 {deleted} 个权限缓存')
                    )
                else:
                    # 如果不支持模式删除，尝试手动删除
                    if hasattr(cache, 'keys'):
                        keys = cache.keys(pattern)
                        if keys:
                            cache.delete_many(keys)
                            self.stdout.write(
                                self.style.SUCCESS(f'已清除用户 {user_id} 的 {len(keys)} 个权限缓存')
                            )
                        else:
                            self.stdout.write(
                                self.style.WARNING(f'用户 {user_id} 没有权限缓存')
                            )
                    else:
                        self.stdout.write(
                            self.style.ERROR('缓存后端不支持模式删除')
                        )
            else:
                # 清除所有权限缓存
                if hasattr(cache, 'delete_pattern'):
                    deleted = cache.delete_pattern('page_perm:*')
                    self.stdout.write(
                        self.style.SUCCESS(f'已清除所有权限缓存 ({deleted} 个)')
                    )
                else:
                    cache.clear()
                    self.stdout.write(
                        self.style.SUCCESS('已清除所有缓存')
                    )
                    
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'清除缓存失败: {e}')
            )

    def test_user_permission(self, options):
        """测试用户权限"""
        user_id = options.get('user_id')
        username = options.get('username')
        path = options.get('path', '/')
        
        # 获取用户
        try:
            if user_id:
                user = User.objects.get(id=user_id)
            elif username:
                user = User.objects.get(username=username)
            else:
                self.stdout.write(
                    self.style.ERROR('请提供 --user-id 或 --username 参数')
                )
                return
        except User.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f'用户不存在: {user_id or username}')
            )
            return
        
        # 测试权限
        try:
            from users.permissions import PermissionManager
            from django.urls import resolve
            
            self.stdout.write(f'\n🧪 测试用户权限:')
            self.stdout.write(f'  用户: {user.username} (ID: {user.id})')
            self.stdout.write(f'  路径: {path}')
            
            # 解析URL
            try:
                resolved = resolve(path)
                app_name = resolved.app_name
                url_name = resolved.url_name
                
                self.stdout.write(f'  应用: {app_name}')
                self.stdout.write(f'  URL名称: {url_name}')
                
                # 检查权限
                start_time = time.time()
                has_permission = PermissionManager.check_page_permission(user, app_name, url_name)
                check_time = time.time() - start_time
                
                self.stdout.write(f'  权限检查结果: {"✅ 允许" if has_permission else "❌ 拒绝"}')
                self.stdout.write(f'  检查耗时: {check_time*1000:.2f}ms')
                
                # 检查用户角色
                user_role = PermissionManager.get_user_role(user)
                self.stdout.write(f'  用户角色: {user_role}')
                
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'URL解析失败: {e}')
                )
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'权限测试失败: {e}')
            )

    def validate_config(self):
        """验证中间件配置"""
        self.stdout.write('🔍 验证中间件配置...\n')
        
        issues = []
        
        # 检查中间件是否已安装
        if 'users.middleware.PagePermissionMiddleware' not in settings.MIDDLEWARE:
            issues.append('❌ PagePermissionMiddleware 未在 MIDDLEWARE 中配置')
        else:
            self.stdout.write('✅ PagePermissionMiddleware 已正确配置')
        
        # 检查缓存配置
        if not hasattr(settings, 'CACHES') or not settings.CACHES:
            issues.append('❌ 缓存未配置，权限缓存功能将不可用')
        else:
            self.stdout.write('✅ 缓存配置正常')
        
        # 检查权限相关设置
        cache_timeout = getattr(settings, 'PAGE_PERMISSION_CACHE_TIMEOUT', None)
        if cache_timeout is None:
            self.stdout.write('⚠️ PAGE_PERMISSION_CACHE_TIMEOUT 未设置，将使用默认值 300 秒')
        else:
            self.stdout.write(f'✅ 权限缓存超时设置: {cache_timeout} 秒')
        
        cache_enabled = getattr(settings, 'PAGE_PERMISSION_ENABLE_CACHE', None)
        if cache_enabled is None:
            self.stdout.write('⚠️ PAGE_PERMISSION_ENABLE_CACHE 未设置，将使用默认值 True')
        else:
            self.stdout.write(f'✅ 权限缓存启用状态: {cache_enabled}')
        
        # 检查模板文件
        try:
            from django.template.loader import get_template
            get_template('errors/permission_denied.html')
            self.stdout.write('✅ 权限拒绝模板文件存在')
        except Exception:
            issues.append('❌ 权限拒绝模板文件 errors/permission_denied.html 不存在')
        
        # 总结
        if issues:
            self.stdout.write(f'\n❌ 发现 {len(issues)} 个配置问题:')
            for issue in issues:
                self.stdout.write(f'  {issue}')
        else:
            self.stdout.write('\n✅ 所有配置检查通过！')
