<!-- 商品管理页面共享内容 -->
{% load static %}

<!-- 页面头部 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="mb-1">
            <i class="bi bi-box text-primary me-2"></i>
            商品管理
        </h2>
        <p class="text-muted mb-0">管理商品信息，包括添加、编辑和删除商品</p>
    </div>
    <!-- 移动到搜索框下方 -->
</div>

<!-- 搜索和过滤 -->
<div class="card mb-4">
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-8">
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="bi bi-search"></i>
                    </span>
                    <input type="text"
                           class="form-control"
                           name="search"
                           id="search"
                           placeholder="搜索商品名称、编码或描述..."
                           value="{{ request.GET.search }}">
                </div>
            </div>
            <div class="col-md-4">
                <button type="submit" class="btn btn-outline-primary w-100">
                    <i class="bi bi-search"></i> 搜索
                </button>
            </div>
        </form>
    </div>
</div>

<!-- 操作按钮区域 - 在搜索框下方 -->
<div class="mb-3">
    <div class="d-flex gap-2 align-items-center">
        {% if perms.products.add_product %}
        <button type="button"
                class="btn btn-primary"
                onclick="openEditModal('{% url 'products:create' %}', '新商品', '添加商品')">
            <i class="bi bi-plus-lg"></i> 添加商品
        </button>

        <button type="button"
                class="btn btn-success"
                onclick="openBatchUploadModal()"
                title="批量上传商品Excel文件">
            <i class="bi bi-upload"></i> 批量上传
        </button>

        <button type="button"
                class="btn btn-outline-secondary"
                onclick="downloadProductTemplate()"
                title="下载商品批量上传模板">
            <i class="bi bi-download"></i> 下载模板
        </button>
        {% endif %}

        {% if perms.products.change_product %}
        <button type="button"
                class="btn btn-outline-primary"
                id="batch-edit-btn"
                onclick="editSelectedProduct()"
                style="display: none;">
            <i class="bi bi-pencil"></i> 编辑
        </button>
        {% endif %}

        {% if perms.products.delete_product %}
        <button type="button"
                class="btn btn-outline-danger"
                id="batch-delete-btn"
                onclick="confirmBatchDelete()"
                style="display: none;">
            <i class="bi bi-trash"></i> 删除 (<span id="selected-count">0</span>)
        </button>
        {% endif %}

        <!-- 选中状态提示 -->
        <span id="selection-info" class="text-muted ms-2" style="display: none;">
            <i class="bi bi-info-circle"></i> 已选中 <span id="selected-count-info">0</span> 个商品
        </span>
    </div>
</div>

<!-- 商品列表 -->
<div class="card card-data">
    <div class="card-body">
        {% if products %}
            <div class="unified-table-container">
                <table class="unified-table table-theme-primary" id="product-table">
                    <colgroup>
                        {% if perms.products.delete_product %}
                        <!-- 有删除权限：8列（移除状态列） -->
                        <col style="width: 60px;"><!-- 复选框 -->
                        <col style="width: 30%;"><!-- 商品名称 -->
                        <col style="width: 25%;"><!-- 商品描述 -->
                        <col style="width: 15%;"><!-- 商品编码 -->
                        <col style="width: 12%;"><!-- 价格 -->
                        <col style="width: 15%;"><!-- 创建时间 -->
                        <col style="width: 10%;"><!-- 创建人 -->
                        <col style="width: 18%;"><!-- 最后修改 -->
                        {% else %}
                        <!-- 无删除权限：7列（移除状态列） -->
                        <col style="width: 30%;"><!-- 商品名称 -->
                        <col style="width: 25%;"><!-- 商品描述 -->
                        <col style="width: 15%;"><!-- 商品编码 -->
                        <col style="width: 12%;"><!-- 价格 -->
                        <col style="width: 18%;"><!-- 创建时间 -->
                        <col style="width: 12%;"><!-- 创建人 -->
                        <col style="width: 20%;"><!-- 最后修改 -->
                        {% endif %}
                    </colgroup>
                    <thead>
                        <tr>
                            {% if perms.products.delete_product %}
                            <th class="checkbox-column">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="select-all" onchange="toggleSelectAll()">
                                    <label class="form-check-label" for="select-all">
                                        <span class="visually-hidden">全选</span>
                                    </label>
                                </div>
                            </th>
                            {% endif %}
                            <th>商品名称</th>
                            <th>商品描述</th>
                            <th>商品编码</th>
                            <th>价格</th>
                            <th>创建时间</th>
                            <th>创建人</th>
                            <th>最后修改</th>
                        </tr>
                    </thead>
                    <tbody id="product-table-body">
                        {% include 'products/partials/product_table.html' %}
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            <div id="product-pagination-container">
                {% include 'products/partials/pagination.html' %}
            </div>
        {% else %}
            <div class="text-center py-5">
                <i class="bi bi-box display-1 text-muted"></i>
                <h4 class="text-muted mt-3">暂无商品</h4>
                <p class="text-muted">点击上方"添加商品"按钮开始添加商品</p>
            </div>
        {% endif %}
    </div>
</div>

<style>
/* 商品管理页面样式 */
/* 状态相关样式已移除（商品状态功能已移除） */

/* 表格行悬停效果 - 动画已移除 */
.unified-table tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
    /* 移除transform和box-shadow动画效果 */
}

/* 按钮组样式 - 动画已移除 */
.btn-group .btn {
    /* 移除transition动画效果 */
}

.btn-group .btn:hover {
    /* 移除transform和box-shadow动画效果 */
}

/* 消息提示样式优化 - 动画已移除 */
.alert.message-alert {
    position: relative;
    z-index: 1050;
    margin-bottom: 1rem;
    border-left: 4px solid;
    /* 移除slideInFromRight动画效果 */
}

.alert.alert-success.message-alert {
    border-left-color: #28a745;
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.alert.alert-danger.message-alert {
    border-left-color: #dc3545;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

.alert.alert-warning.message-alert {
    border-left-color: #ffc107;
    background-color: #fff3cd;
    border-color: #ffeaa7;
}
</style>

<script>
// 商品页面特定的初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('🛍️ 商品管理页面已加载');

    // 根据权限设置body类
    {% if not perms.products.delete_product %}
    document.body.classList.add('no-delete-permission');
    {% endif %}

    // 初始化编辑模态框功能
    if (window.initializeEditModal) {
        window.initializeEditModal();
    }

    // 确保商品管理功能已初始化
    if (window.WarehouseJS && window.WarehouseJS.initializeProductManagement) {
        window.WarehouseJS.initializeProductManagement();
    }

    // 绑定分页事件
    if (window.WarehouseJS && window.WarehouseJS.bindPaginationEvents) {
        window.WarehouseJS.bindPaginationEvents();
    }

    // 初始化批量删除功能
    if (window.updateBatchDeleteButton) {
        window.updateBatchDeleteButton();
    }

    // 应用权限相关的CSS类
    if (window.applyPermissionClasses) {
        window.applyPermissionClasses();
    }

    // 强制修复表格布局
    if (window.forceFixTableLayout) {
        window.forceFixTableLayout();
    }
});
</script>
