{% extends "products/base.html" %}
{% load static %}

{% block title %}权限管理仪表板 - 仓库管理系统{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.css" rel="stylesheet">
<style>
.dashboard-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    /* 移除transform动画效果 */
}

.dashboard-card:hover {
    /* 移除transform和box-shadow动画效果 */
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.stat-card {
    text-align: center;
    border-left: 4px solid var(--primary-color);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stat-label {
    color: #6c757d;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.chart-container {
    position: relative;
    height: 300px;
    margin: 1rem 0;
}

.recent-activity {
    max-height: 400px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    border-bottom: 1px solid #e9ecef;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    font-size: 1.2rem;
}

.activity-icon.role-change {
    background-color: #e3f2fd;
    color: #1976d2;
}

.activity-icon.permission-grant {
    background-color: #e8f5e8;
    color: #388e3c;
}

.activity-icon.permission-revoke {
    background-color: #ffebee;
    color: #d32f2f;
}

.activity-content {
    flex: 1;
}

.activity-title {
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.activity-time {
    font-size: 0.8rem;
    color: #6c757d;
}

.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.quick-action-btn {
    display: flex;
    align-items: center;
    padding: 1rem;
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    text-decoration: none;
    color: #495057;
    /* 移除transition动画效果 */
}

.quick-action-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    text-decoration: none;
}

.quick-action-icon {
    font-size: 1.5rem;
    margin-right: 0.75rem;
}

.permission-category {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: #f8f9fa;
    border-radius: 4px;
    margin-bottom: 0.5rem;
}

.category-name {
    font-weight: 500;
    text-transform: capitalize;
}

.category-stats {
    display: flex;
    gap: 1rem;
}

.category-stat {
    font-size: 0.9rem;
    color: #6c757d;
}

@media (max-width: 768px) {
    .dashboard-card {
        padding: 1rem;
    }
    
    .stat-number {
        font-size: 2rem;
    }
    
    .quick-actions {
        grid-template-columns: 1fr;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">权限管理仪表板</h1>
        <div class="btn-group">
            <button type="button" class="btn btn-outline-primary" onclick="refreshDashboard()">
                <i class="fas fa-sync-alt"></i> 刷新
            </button>
            <a href="{% url 'admin:index' %}" class="btn btn-outline-secondary">
                <i class="fas fa-cog"></i> Django Admin
            </a>
        </div>
    </div>

    <!-- 统计卡片 -->
    <div class="row">
        <div class="col-md-2 col-sm-4 col-6">
            <div class="dashboard-card stat-card">
                <div class="stat-number">{{ total_users }}</div>
                <div class="stat-label">总用户数</div>
            </div>
        </div>
        <div class="col-md-2 col-sm-4 col-6">
            <div class="dashboard-card stat-card">
                <div class="stat-number">{{ total_permissions }}</div>
                <div class="stat-label">总权限数</div>
            </div>
        </div>
        <div class="col-md-2 col-sm-4 col-6">
            <div class="dashboard-card stat-card">
                <div class="stat-number">{{ active_permissions }}</div>
                <div class="stat-label">活跃权限</div>
            </div>
        </div>
        <div class="col-md-2 col-sm-4 col-6">
            <div class="dashboard-card stat-card">
                <div class="stat-number">{{ total_role_permissions }}</div>
                <div class="stat-label">角色权限</div>
            </div>
        </div>
        <div class="col-md-2 col-sm-4 col-6">
            <div class="dashboard-card stat-card">
                <div class="stat-number">{{ total_custom_permissions }}</div>
                <div class="stat-label">自定义权限</div>
            </div>
        </div>
        <div class="col-md-2 col-sm-4 col-6">
            <div class="dashboard-card stat-card">
                <div class="stat-number">{{ total_templates }}</div>
                <div class="stat-label">权限模板</div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 角色分布图表 -->
        <div class="col-lg-6">
            <div class="dashboard-card">
                <h5 class="card-title mb-3">
                    <i class="fas fa-users text-primary"></i> 角色分布
                </h5>
                <div class="chart-container">
                    <canvas id="roleChart"></canvas>
                </div>
            </div>
        </div>

        <!-- 权限分类统计 -->
        <div class="col-lg-6">
            <div class="dashboard-card">
                <h5 class="card-title mb-3">
                    <i class="fas fa-shield-alt text-primary"></i> 权限分类
                </h5>
                <div class="permission-categories">
                    {% for category in permission_categories %}
                    <div class="permission-category">
                        <span class="category-name">{{ category.category|default:"未分类" }}</span>
                        <div class="category-stats">
                            <span class="category-stat">总计: {{ category.count }}</span>
                            <span class="category-stat">活跃: {{ category.active_count }}</span>
                        </div>
                    </div>
                    {% empty %}
                    <p class="text-muted">暂无权限分类数据</p>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 最近活动 -->
        <div class="col-lg-8">
            <div class="dashboard-card">
                <h5 class="card-title mb-3">
                    <i class="fas fa-history text-primary"></i> 最近活动
                </h5>
                <div class="recent-activity">
                    {% for log in recent_changes %}
                    <div class="activity-item">
                        <div class="activity-icon {% if log.action == 'ROLE_CHANGED' %}role-change{% elif log.action == 'PERMISSION_GRANTED' %}permission-grant{% else %}permission-revoke{% endif %}">
                            {% if log.action == 'ROLE_CHANGED' %}
                                <i class="fas fa-user-tag"></i>
                            {% elif log.action == 'PERMISSION_GRANTED' %}
                                <i class="fas fa-check"></i>
                            {% else %}
                                <i class="fas fa-times"></i>
                            {% endif %}
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">{{ log.object_repr }}</div>
                            <div class="activity-time">
                                {{ log.user.username }} · {{ log.timestamp|timesince }}前
                            </div>
                        </div>
                    </div>
                    {% empty %}
                    <p class="text-muted">暂无最近活动</p>
                    {% endfor %}
                </div>
            </div>
        </div>

        <!-- 快速操作 -->
        <div class="col-lg-4">
            <div class="dashboard-card">
                <h5 class="card-title mb-3">
                    <i class="fas fa-bolt text-primary"></i> 快速操作
                </h5>
                <div class="quick-actions">
                    <a href="{% url 'users:permission_matrix' %}" class="quick-action-btn">
                        <i class="fas fa-table quick-action-icon"></i>
                        <div>
                            <div>权限矩阵</div>
                            <small class="text-muted">查看角色权限分配</small>
                        </div>
                    </a>
                    <a href="{% url 'users:user_role_management' %}" class="quick-action-btn">
                        <i class="fas fa-user-cog quick-action-icon"></i>
                        <div>
                            <div>用户管理</div>
                            <small class="text-muted">管理用户角色</small>
                        </div>
                    </a>
                    <a href="{% url 'users:permission_preview' %}" class="quick-action-btn">
                        <i class="fas fa-eye quick-action-icon"></i>
                        <div>
                            <div>权限预览</div>
                            <small class="text-muted">预览用户权限</small>
                        </div>
                    </a>
                    <a href="{% url 'admin:users_pagepermission_changelist' %}" class="quick-action-btn">
                        <i class="fas fa-plus quick-action-icon"></i>
                        <div>
                            <div>添加权限</div>
                            <small class="text-muted">创建新权限</small>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
<script>
// 角色分布图表
const roleData = {
    labels: [
        {% for role_code, role_data in role_stats.items %}
        '{{ role_data.name }}'{% if not forloop.last %},{% endif %}
        {% endfor %}
    ],
    datasets: [{
        data: [
            {% for role_code, role_data in role_stats.items %}
            {{ role_data.user_count }}{% if not forloop.last %},{% endif %}
            {% endfor %}
        ],
        backgroundColor: [
            '#FF6384',
            '#36A2EB', 
            '#FFCE56',
            '#4BC0C0',
            '#9966FF',
            '#FF9F40'
        ]
    }]
};

const roleChart = new Chart(document.getElementById('roleChart'), {
    type: 'doughnut',
    data: roleData,
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// 刷新仪表板
function refreshDashboard() {
    location.reload();
}

// 定期刷新统计数据
setInterval(function() {
    fetch('{% url "users:ajax_get_dashboard_stats" %}')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 更新统计数字
                updateStats(data.stats);
            }
        })
        .catch(error => console.error('Error:', error));
}, 30000); // 30秒刷新一次

function updateStats(stats) {
    // 这里可以添加动态更新统计数据的逻辑
    console.log('Stats updated:', stats);
}
</script>
{% endblock %}
