{% extends 'products/base.html' %}

{% block title %}用户管理 - 仓库管理系统{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="bi bi-people"></i> 用户管理</h2>
    <div class="badge bg-warning text-dark">
        <i class="bi bi-shield-check"></i> 管理员专用
    </div>
</div>

<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card text-center">
            <div class="card-body">
                <i class="bi bi-people display-6 text-primary"></i>
                <h5 class="card-title mt-2">{{ total_users }}</h5>
                <p class="card-text text-muted">总用户数</p>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card text-center">
            <div class="card-body">
                <i class="bi bi-shield-check display-6 text-danger"></i>
                <h5 class="card-title mt-2">{{ admin_users }}</h5>
                <p class="card-text text-muted">管理员</p>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card text-center">
            <div class="card-body">
                <i class="bi bi-person display-6 text-success"></i>
                <h5 class="card-title mt-2">{{ normal_users }}</h5>
                <p class="card-text text-muted">普通用户</p>
            </div>
        </div>
    </div>
</div>

<!-- 用户列表 -->
<div class="card">
    <div class="card-body">
        {% if user_profiles %}
            <div class="unified-table-container">
                <table class="unified-table table-theme-primary">
                    <thead>
                        <tr>
                            <th>用户信息</th>
                            <th>角色</th>
                            <th>状态</th>
                            <th>注册时间</th>
                            <th>最后登录</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for profile in user_profiles %}
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar-circle me-3">
                                        <i class="bi bi-person-circle fs-2 text-muted"></i>
                                    </div>
                                    <div>
                                        <strong>{{ profile.user.username }}</strong>
                                        {% if profile.user.email %}
                                            <br><small class="text-muted">{{ profile.user.email }}</small>
                                        {% endif %}
                                        {% if profile.user.first_name or profile.user.last_name %}
                                            <br><small class="text-muted">{{ profile.user.first_name }} {{ profile.user.last_name }}</small>
                                        {% endif %}
                                    </div>
                                </div>
                            </td>
                            <td>
                                {% if profile.role == 'ADMIN' %}
                                    <span class="badge bg-danger">
                                        <i class="bi bi-shield-check"></i> 管理员
                                    </span>
                                {% else %}
                                    <span class="badge bg-primary">
                                        <i class="bi bi-person"></i> 普通用户
                                    </span>
                                {% endif %}
                            </td>
                            <td>
                                {% if profile.user.is_active %}
                                    <span class="badge bg-success">
                                        <i class="bi bi-check-circle"></i> 活跃
                                    </span>
                                {% else %}
                                    <span class="badge bg-secondary">
                                        <i class="bi bi-x-circle"></i> 禁用
                                    </span>
                                {% endif %}
                            </td>
                            <td>
                                <small>{{ profile.created_at|date:"Y-m-d H:i" }}</small>
                            </td>
                            <td>
                                {% if profile.user.last_login %}
                                    <small>{{ profile.user.last_login|date:"Y-m-d H:i" }}</small>
                                {% else %}
                                    <small class="text-muted">从未登录</small>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm" role="group">
                                    {% if profile.user != request.user %}
                                        {% if profile.user.is_active %}
                                            <button class="btn btn-outline-warning" title="禁用用户">
                                                <i class="bi bi-person-x"></i>
                                            </button>
                                        {% else %}
                                            <button class="btn btn-outline-success" title="启用用户">
                                                <i class="bi bi-person-check"></i>
                                            </button>
                                        {% endif %}
                                        
                                        {% if profile.role == 'USER' %}
                                            <button class="btn btn-outline-danger" title="设为管理员">
                                                <i class="bi bi-shield-plus"></i>
                                            </button>
                                        {% else %}
                                            <button class="btn btn-outline-primary" title="设为普通用户">
                                                <i class="bi bi-shield-minus"></i>
                                            </button>
                                        {% endif %}
                                    {% else %}
                                        <span class="badge bg-info">当前用户</span>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            {% if is_paginated %}
                <nav aria-label="用户分页">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1">首页</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}">上一页</a>
                            </li>
                        {% endif %}

                        <li class="page-item active">
                            <span class="page-link">第 {{ page_obj.number }} 页，共 {{ page_obj.paginator.num_pages }} 页</span>
                        </li>

                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}">下一页</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">末页</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            {% endif %}
        {% else %}
            <div class="text-center py-5">
                <i class="bi bi-people display-1 text-muted"></i>
                <h4 class="text-muted mt-3">暂无用户</h4>
                <p class="text-muted">系统中还没有注册用户</p>
            </div>
        {% endif %}
    </div>
</div>

<div class="alert alert-warning mt-4">
    <i class="bi bi-exclamation-triangle"></i>
    <strong>注意：</strong>此页面仅管理员可访问。用户角色和状态的修改功能在MVP版本中暂未实现，仅作展示。
</div>
{% endblock %}
