"""
页面权限中间件测试模块
"""
from django.test import TestCase, RequestFactory, override_settings
from django.contrib.auth.models import User, AnonymousUser
from django.http import HttpResponse, JsonResponse
from django.urls import reverse
from django.core.cache import cache
from django.contrib.sessions.middleware import SessionMiddleware
from django.contrib.messages.storage.fallback import FallbackStorage
from unittest.mock import patch, MagicMock

from .middleware import PagePermissionMiddleware
from .models import UserProfile, PagePermission, RolePagePermission
from .permissions import UserRoles


class PagePermissionMiddlewareTestCase(TestCase):
    def setUp(self):
        self.factory = RequestFactory()
        self.middleware = PagePermissionMiddleware(lambda req: HttpResponse('OK'))
        
        # 创建测试用户
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.user_profile = UserProfile.objects.create(
            user=self.user,
            role=UserRoles.WAREHOUSE_OPERATOR
        )
        
        # 创建管理员用户
        self.admin_user = User.objects.create_user(
            username='admin',
            email='<EMAIL>',
            password='adminpass123'
        )
        
        self.admin_profile = UserProfile.objects.create(
            user=self.admin_user,
            role=UserRoles.SUPER_ADMIN
        )
        
        # 创建测试页面权限
        self.page_permission = PagePermission.objects.create(
            name='products_list',
            description='商品列表页面权限',
            url_pattern='^/products/$',
            url_name='products:list',
            app_name='products',
            category='products',
            permission_level='role_based'
        )
        
        # 创建角色权限分配
        RolePagePermission.objects.create(
            role=UserRoles.SUPER_ADMIN,
            page_permission=self.page_permission,
            is_granted=True,
            granted_by=self.admin_user
        )

    def _add_session_and_messages(self, request):
        """为请求添加session和messages支持"""
        middleware = SessionMiddleware(lambda req: None)
        middleware.process_request(request)
        request.session.save()
        
        messages = FallbackStorage(request)
        setattr(request, '_messages', messages)

    def test_should_skip_permission_check_exempt_urls(self):
        """测试豁免URL跳过权限检查"""
        exempt_paths = [
            '/users/login/',
            '/admin/',
            '/static/css/style.css',
            '/media/images/logo.png',
            '/favicon.ico'
        ]
        
        for path in exempt_paths:
            request = self.factory.get(path)
            self.assertTrue(
                self.middleware._should_skip_permission_check(request),
                f"Path {path} should be exempt from permission check"
            )

    def test_should_skip_permission_check_exempt_patterns(self):
        """测试豁免URL模式跳过权限检查"""
        exempt_paths = [
            '/api/public/status',
            '/health/check',
            '/status/alive',
            '/debug/info'
        ]
        
        for path in exempt_paths:
            request = self.factory.get(path)
            self.assertTrue(
                self.middleware._should_skip_permission_check(request),
                f"Path {path} should match exempt pattern"
            )

    def test_handle_anonymous_user_public_urls(self):
        """测试匿名用户访问公开URL"""
        request = self.factory.get('/')
        request.user = AnonymousUser()
        self._add_session_and_messages(request)
        
        response = self.middleware._handle_anonymous_user(request)
        self.assertIsNone(response)  # 允许访问

    def test_handle_anonymous_user_protected_urls(self):
        """测试匿名用户访问受保护URL"""
        request = self.factory.get('/products/')
        request.user = AnonymousUser()
        self._add_session_and_messages(request)
        
        response = self.middleware._handle_anonymous_user(request)
        self.assertEqual(response.status_code, 302)  # 重定向到登录页面

    def test_handle_anonymous_user_ajax_request(self):
        """测试匿名用户AJAX请求"""
        request = self.factory.get('/products/')
        request.user = AnonymousUser()
        request.META['HTTP_X_REQUESTED_WITH'] = 'XMLHttpRequest'
        self._add_session_and_messages(request)
        
        response = self.middleware._handle_anonymous_user(request)
        self.assertEqual(response.status_code, 401)
        self.assertIsInstance(response, JsonResponse)

    def test_resolve_url_info(self):
        """测试URL信息解析"""
        request = self.factory.get('/products/')
        
        with patch('users.middleware.resolve') as mock_resolve:
            mock_resolved = MagicMock()
            mock_resolved.app_name = 'products'
            mock_resolved.url_name = 'list'
            mock_resolved.view_name = 'ProductListView'
            mock_resolved.namespace = None
            mock_resolved.kwargs = {}
            mock_resolve.return_value = mock_resolved
            
            url_info = self.middleware._resolve_url_info(request)
            
            self.assertEqual(url_info['app_name'], 'products')
            self.assertEqual(url_info['url_name'], 'list')
            self.assertEqual(url_info['view_name'], 'ProductListView')

    def test_generate_cache_key(self):
        """测试缓存键生成"""
        url_info = {
            'app_name': 'products',
            'url_name': 'list'
        }
        
        cache_key = self.middleware._generate_cache_key(123, url_info)
        self.assertEqual(cache_key, 'page_perm:123:products:list')

    def test_perform_permission_check_with_permission(self):
        """测试有权限的权限检查"""
        url_info = {
            'app_name': 'products',
            'url_name': 'products_list'
        }
        
        with patch('users.permissions.PermissionManager.check_page_permission', return_value=True):
            result = self.middleware._perform_permission_check(self.admin_user, url_info)
            self.assertTrue(result)

    def test_perform_permission_check_without_permission(self):
        """测试无权限的权限检查"""
        url_info = {
            'app_name': 'products',
            'url_name': 'products_list'
        }
        
        with patch('users.permissions.PermissionManager.check_page_permission', return_value=False):
            result = self.middleware._perform_permission_check(self.user, url_info)
            self.assertFalse(result)

    def test_handle_permission_denied_ajax(self):
        """测试AJAX请求的权限拒绝处理"""
        request = self.factory.get('/products/')
        request.user = self.user
        request.META['HTTP_X_REQUESTED_WITH'] = 'XMLHttpRequest'
        self._add_session_and_messages(request)
        
        url_info = {
            'app_name': 'products',
            'url_name': 'products_list'
        }
        
        response = self.middleware._handle_permission_denied(request, url_info)
        self.assertEqual(response.status_code, 403)
        self.assertIsInstance(response, JsonResponse)

    def test_handle_permission_denied_normal_request(self):
        """测试普通请求的权限拒绝处理"""
        request = self.factory.get('/products/')
        request.user = self.user
        self._add_session_and_messages(request)
        
        url_info = {
            'app_name': 'products',
            'url_name': 'products_list'
        }
        
        with patch('users.middleware.render') as mock_render:
            mock_render.return_value = HttpResponse('Permission Denied', status=403)
            response = self.middleware._handle_permission_denied(request, url_info)
            self.assertEqual(response.status_code, 403)

    @override_settings(PAGE_PERMISSION_ENABLE_CACHE=True)
    def test_permission_caching(self):
        """测试权限缓存功能"""
        request = self.factory.get('/products/')
        request.user = self.admin_user
        self._add_session_and_messages(request)
        
        url_info = {
            'app_name': 'products',
            'url_name': 'products_list'
        }
        
        cache_key = self.middleware._generate_cache_key(self.admin_user.id, url_info)
        
        # 清除缓存
        cache.delete(cache_key)
        
        with patch('users.permissions.PermissionManager.check_page_permission', return_value=True) as mock_check:
            # 第一次检查 - 应该调用权限检查
            result1 = self.middleware._perform_permission_check(self.admin_user, url_info)
            self.assertTrue(result1)
            self.assertEqual(mock_check.call_count, 1)
            
            # 设置缓存
            cache.set(cache_key, True, 300)
            
            # 第二次检查 - 应该使用缓存，不调用权限检查
            cached_result = cache.get(cache_key)
            self.assertTrue(cached_result)

    def test_clear_cache_specific_user(self):
        """测试清除特定用户缓存"""
        # 设置一些测试缓存
        cache.set('page_perm:123:products:list', True, 300)
        cache.set('page_perm:123:inventory:view', False, 300)
        cache.set('page_perm:456:products:list', True, 300)
        
        # 清除用户123的缓存
        self.middleware.clear_cache(user_id=123)
        
        # 验证缓存清除结果
        self.assertIsNone(cache.get('page_perm:123:products:list'))
        self.assertIsNone(cache.get('page_perm:123:inventory:view'))
        # 其他用户的缓存应该保留
        self.assertTrue(cache.get('page_perm:456:products:list'))

    def test_get_stats(self):
        """测试获取统计信息"""
        # 模拟一些统计数据
        self.middleware._stats = {
            'total_checks': 100,
            'cache_hits': 80,
            'cache_misses': 20,
            'permission_denials': 5,
            'avg_check_time': 0.001
        }
        
        stats = self.middleware.get_stats()
        
        self.assertEqual(stats['total_checks'], 100)
        self.assertEqual(stats['cache_hit_rate'], 80.0)
        self.assertEqual(stats['denial_rate'], 5.0)
        self.assertEqual(stats['avg_check_time_ms'], 1.0)

    def test_middleware_integration(self):
        """测试中间件集成"""
        request = self.factory.get('/products/')
        request.user = self.admin_user
        self._add_session_and_messages(request)
        
        # 模拟豁免URL检查
        with patch.object(self.middleware, '_should_skip_permission_check', return_value=False):
            with patch.object(self.middleware, '_check_page_permission', return_value=None):
                response = self.middleware(request)
                self.assertEqual(response.status_code, 200)

    def test_get_client_ip(self):
        """测试获取客户端IP"""
        # 测试X-Forwarded-For头
        request = self.factory.get('/')
        request.META['HTTP_X_FORWARDED_FOR'] = '***********, ********'
        
        ip = self.middleware._get_client_ip(request)
        self.assertEqual(ip, '***********')
        
        # 测试REMOTE_ADDR
        request = self.factory.get('/')
        request.META['REMOTE_ADDR'] = '127.0.0.1'
        
        ip = self.middleware._get_client_ip(request)
        self.assertEqual(ip, '127.0.0.1')

    def test_update_stats(self):
        """测试统计信息更新"""
        initial_checks = self.middleware._stats['total_checks']
        
        self.middleware._update_stats(0.002)
        
        self.assertEqual(self.middleware._stats['total_checks'], initial_checks + 1)
        self.assertGreater(self.middleware._stats['avg_check_time'], 0)

    def tearDown(self):
        """清理测试数据"""
        cache.clear()
        super().tearDown()
