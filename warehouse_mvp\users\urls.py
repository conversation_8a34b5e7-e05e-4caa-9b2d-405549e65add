from django.urls import path
from . import views

app_name = 'users'

urlpatterns = [
    path('login/', views.user_login, name='login'),
    path('register/', views.user_register, name='register'),
    path('logout/', views.user_logout, name='logout'),
    path('management/', views.UserManagementView.as_view(), name='management'),
    path('roles/', views.RoleManagementView.as_view(), name='role_management'),
    path('assign-role/', views.assign_user_role, name='assign_role'),
    path('init-permissions/', views.init_permission_system, name='init_permissions'),
    path('audit-log/', views.AuditLogView.as_view(), name='audit_log'),

    # 权限管理仪表板
    path('permissions/', views.PermissionDashboardView.as_view(), name='permission_dashboard'),
    path('permissions/matrix/', views.PermissionMatrixView.as_view(), name='permission_matrix'),
    path('permissions/user-roles/', views.UserRoleManagementView.as_view(), name='user_role_management'),
    path('permissions/preview/', views.PermissionPreviewView.as_view(), name='permission_preview'),

    # 菜单权限管理
    path('permissions/menu/', views.MenuManagementView.as_view(), name='menu_management'),

    # AJAX API
    path('api/change-role/', views.ajax_change_user_role, name='ajax_change_user_role'),
    path('api/toggle-permission/', views.ajax_toggle_role_permission, name='ajax_toggle_role_permission'),
    path('api/search-permissions/', views.ajax_search_permissions, name='ajax_search_permissions'),
    path('api/user-permissions/', views.ajax_get_user_permissions, name='ajax_get_user_permissions'),
    path('api/dashboard-stats/', views.ajax_get_dashboard_stats, name='ajax_get_dashboard_stats'),
    path('api/toggle-menu-permission/', views.ajax_toggle_menu_permission, name='ajax_toggle_menu_permission'),
    path('api/menu-permissions/', views.ajax_get_menu_permissions, name='ajax_get_menu_permissions'),
]
