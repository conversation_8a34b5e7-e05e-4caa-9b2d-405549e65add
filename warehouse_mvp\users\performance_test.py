"""
页面权限中间件性能测试
"""
import time
import statistics
from django.test import TestCase, RequestFactory
from django.contrib.auth.models import User
from django.core.cache import cache
from unittest.mock import patch

from .middleware import PagePermissionMiddleware
from .models import UserProfile
from .permissions import UserRoles


class MiddlewarePerformanceTest(TestCase):
    def setUp(self):
        self.factory = RequestFactory()
        self.middleware = PagePermissionMiddleware(lambda req: None)
        
        # 创建测试用户
        self.user = User.objects.create_user(
            username='perftest',
            email='<EMAIL>',
            password='testpass123'
        )
        
        UserProfile.objects.create(
            user=self.user,
            role=UserRoles.WAREHOUSE_OPERATOR
        )

    def test_middleware_performance_without_cache(self):
        """测试不使用缓存时的性能"""
        self.middleware.enable_cache = False
        
        times = []
        for i in range(100):
            request = self.factory.get('/products/')
            request.user = self.user
            
            start_time = time.time()
            
            with patch.object(self.middleware, '_should_skip_permission_check', return_value=False):
                with patch.object(self.middleware, '_resolve_url_info') as mock_resolve:
                    mock_resolve.return_value = {
                        'app_name': 'products',
                        'url_name': 'list',
                        'path': '/products/'
                    }
                    
                    with patch('users.permissions.PermissionManager.check_page_permission', return_value=True):
                        self.middleware._check_page_permission(request)
            
            end_time = time.time()
            times.append(end_time - start_time)
        
        avg_time = statistics.mean(times)
        max_time = max(times)
        min_time = min(times)
        
        print(f"\n=== 无缓存性能测试结果 ===")
        print(f"平均响应时间: {avg_time*1000:.2f}ms")
        print(f"最大响应时间: {max_time*1000:.2f}ms")
        print(f"最小响应时间: {min_time*1000:.2f}ms")
        
        # 断言平均响应时间应该小于10ms
        self.assertLess(avg_time, 0.01, "无缓存时平均响应时间应该小于10ms")

    def test_middleware_performance_with_cache(self):
        """测试使用缓存时的性能"""
        self.middleware.enable_cache = True
        cache.clear()
        
        # 预热缓存
        request = self.factory.get('/products/')
        request.user = self.user
        
        url_info = {
            'app_name': 'products',
            'url_name': 'list',
            'path': '/products/'
        }
        
        cache_key = self.middleware._generate_cache_key(self.user.id, url_info)
        cache.set(cache_key, True, 300)
        
        # 测试缓存性能
        times = []
        for i in range(100):
            request = self.factory.get('/products/')
            request.user = self.user
            
            start_time = time.time()
            
            with patch.object(self.middleware, '_should_skip_permission_check', return_value=False):
                with patch.object(self.middleware, '_resolve_url_info') as mock_resolve:
                    mock_resolve.return_value = url_info
                    
                    # 这次应该使用缓存，不调用权限检查
                    self.middleware._check_page_permission(request)
            
            end_time = time.time()
            times.append(end_time - start_time)
        
        avg_time = statistics.mean(times)
        max_time = max(times)
        min_time = min(times)
        
        print(f"\n=== 有缓存性能测试结果 ===")
        print(f"平均响应时间: {avg_time*1000:.2f}ms")
        print(f"最大响应时间: {max_time*1000:.2f}ms")
        print(f"最小响应时间: {min_time*1000:.2f}ms")
        
        # 断言平均响应时间应该小于5ms
        self.assertLess(avg_time, 0.005, "有缓存时平均响应时间应该小于5ms")

    def test_url_pattern_matching_performance(self):
        """测试URL模式匹配性能"""
        patterns = [
            r'^/api/public/',
            r'^/health/',
            r'^/status/',
            r'^/debug/',
            r'^/admin/.*',
            r'^/static/.*',
            r'^/media/.*'
        ]
        
        test_paths = [
            '/api/public/status',
            '/health/check',
            '/products/list',
            '/users/profile',
            '/admin/users/',
            '/static/css/style.css',
            '/media/images/logo.png'
        ]
        
        times = []
        for _ in range(1000):
            start_time = time.time()
            
            for path in test_paths:
                for pattern in patterns:
                    self.middleware._match_url_pattern(path, pattern)
            
            end_time = time.time()
            times.append(end_time - start_time)
        
        avg_time = statistics.mean(times)
        
        print(f"\n=== URL模式匹配性能测试结果 ===")
        print(f"1000次匹配平均时间: {avg_time*1000:.2f}ms")
        print(f"单次匹配平均时间: {(avg_time/len(test_paths)/len(patterns))*1000000:.2f}μs")
        
        # 断言1000次匹配应该在100ms内完成
        self.assertLess(avg_time, 0.1, "1000次URL模式匹配应该在100ms内完成")

    def test_cache_key_generation_performance(self):
        """测试缓存键生成性能"""
        url_info = {
            'app_name': 'products',
            'url_name': 'list',
            'path': '/products/'
        }
        
        times = []
        for _ in range(10000):
            start_time = time.time()
            self.middleware._generate_cache_key(self.user.id, url_info)
            end_time = time.time()
            times.append(end_time - start_time)
        
        avg_time = statistics.mean(times)
        
        print(f"\n=== 缓存键生成性能测试结果 ===")
        print(f"10000次生成平均时间: {avg_time*1000:.2f}ms")
        print(f"单次生成平均时间: {(avg_time)*1000000:.2f}μs")
        
        # 断言单次生成应该在1μs内完成
        self.assertLess(avg_time, 0.000001, "单次缓存键生成应该在1μs内完成")

    def test_memory_usage(self):
        """测试内存使用情况"""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss
        
        # 创建大量请求来测试内存使用
        for i in range(1000):
            request = self.factory.get(f'/products/{i}/')
            request.user = self.user
            
            with patch.object(self.middleware, '_should_skip_permission_check', return_value=False):
                with patch.object(self.middleware, '_resolve_url_info') as mock_resolve:
                    mock_resolve.return_value = {
                        'app_name': 'products',
                        'url_name': 'detail',
                        'path': f'/products/{i}/'
                    }
                    
                    with patch('users.permissions.PermissionManager.check_page_permission', return_value=True):
                        self.middleware._check_page_permission(request)
        
        final_memory = process.memory_info().rss
        memory_increase = final_memory - initial_memory
        
        print(f"\n=== 内存使用测试结果 ===")
        print(f"初始内存: {initial_memory / 1024 / 1024:.2f}MB")
        print(f"最终内存: {final_memory / 1024 / 1024:.2f}MB")
        print(f"内存增长: {memory_increase / 1024 / 1024:.2f}MB")
        
        # 断言内存增长应该小于50MB
        self.assertLess(memory_increase / 1024 / 1024, 50, "1000次请求的内存增长应该小于50MB")

    def test_concurrent_access_simulation(self):
        """模拟并发访问测试"""
        import threading
        import queue
        
        results = queue.Queue()
        
        def worker():
            times = []
            for _ in range(50):
                request = self.factory.get('/products/')
                request.user = self.user
                
                start_time = time.time()
                
                with patch.object(self.middleware, '_should_skip_permission_check', return_value=False):
                    with patch.object(self.middleware, '_resolve_url_info') as mock_resolve:
                        mock_resolve.return_value = {
                            'app_name': 'products',
                            'url_name': 'list',
                            'path': '/products/'
                        }
                        
                        with patch('users.permissions.PermissionManager.check_page_permission', return_value=True):
                            self.middleware._check_page_permission(request)
                
                end_time = time.time()
                times.append(end_time - start_time)
            
            results.put(times)
        
        # 创建10个线程模拟并发
        threads = []
        for _ in range(10):
            thread = threading.Thread(target=worker)
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 收集所有结果
        all_times = []
        while not results.empty():
            times = results.get()
            all_times.extend(times)
        
        avg_time = statistics.mean(all_times)
        
        print(f"\n=== 并发访问测试结果 ===")
        print(f"总请求数: {len(all_times)}")
        print(f"平均响应时间: {avg_time*1000:.2f}ms")
        print(f"95%分位数: {statistics.quantiles(all_times, n=20)[18]*1000:.2f}ms")
        
        # 断言95%的请求应该在20ms内完成
        p95 = statistics.quantiles(all_times, n=20)[18]
        self.assertLess(p95, 0.02, "95%的并发请求应该在20ms内完成")

    def tearDown(self):
        """清理测试数据"""
        cache.clear()
        super().tearDown()


if __name__ == '__main__':
    import django
    from django.conf import settings
    from django.test.utils import get_runner
    
    if not settings.configured:
        settings.configure(
            DEBUG=True,
            DATABASES={
                'default': {
                    'ENGINE': 'django.db.backends.sqlite3',
                    'NAME': ':memory:',
                }
            },
            INSTALLED_APPS=[
                'django.contrib.auth',
                'django.contrib.contenttypes',
                'users',
            ],
            SECRET_KEY='test-secret-key'
        )
    
    django.setup()
    
    TestRunner = get_runner(settings)
    test_runner = TestRunner()
    failures = test_runner.run_tests(["users.performance_test"])
    
    if failures:
        exit(1)
