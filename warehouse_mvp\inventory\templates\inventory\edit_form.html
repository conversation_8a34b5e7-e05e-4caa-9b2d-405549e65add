<form method="post" action="{% url 'inventory:edit' inventory.pk %}" data-submit-url="{% url 'inventory:edit' inventory.pk %}">
    {% csrf_token %}
    
    <!-- 商品信息显示 -->
    <div class="mb-3">
        <label class="form-label fw-bold">商品信息</label>
        <div class="p-3 bg-light rounded">
            <div class="row">
                <div class="col-md-6">
                    <strong>{{ inventory.product.name }}</strong>
                    <br><small class="text-muted">编码: {{ inventory.product.sku }}</small>
                </div>
                <div class="col-md-6 text-end">
                    <span class="badge bg-primary">单价: ¥{{ inventory.product.price }}</span>
                </div>
            </div>
            {% if inventory.product.description %}
                <div class="mt-2">
                    <small class="text-muted">{{ inventory.product.description }}</small>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- 库存数量表单 -->
    <div class="mb-3">
        {{ form.quantity.label_tag }}
        {{ form.quantity }}
        {% if form.quantity.help_text %}
            <div class="form-text">{{ form.quantity.help_text }}</div>
        {% endif %}
        {% if form.quantity.errors %}
            <div class="text-danger small">
                {% for error in form.quantity.errors %}
                    {{ error }}
                {% endfor %}
            </div>
        {% endif %}
    </div>

    <!-- 当前状态显示 -->
    <div class="mb-3">
        <label class="form-label fw-bold">当前状态</label>
        <div class="p-3 bg-light rounded">
            <div class="row text-center">
                <div class="col-md-4">
                    <div class="h5 mb-1">{{ inventory.quantity }}</div>
                    <small class="text-muted">当前库存</small>
                </div>
                <div class="col-md-4">
                    <div class="h5 mb-1">¥{% widthratio inventory.quantity 1 inventory.product.price %}</div>
                    <small class="text-muted">库存价值</small>
                </div>
                <div class="col-md-4">
                    <div class="h5 mb-1">{{ inventory.updated_at|date:"m-d H:i" }}</div>
                    <small class="text-muted">最后更新</small>
                </div>
            </div>
        </div>
    </div>

    <!-- 表单错误显示 -->
    {% if form.non_field_errors %}
        <div class="alert alert-danger">
            {% for error in form.non_field_errors %}
                {{ error }}
            {% endfor %}
        </div>
    {% endif %}

    <!-- 提示信息 -->
    <div class="alert alert-info">
        <i class="bi bi-info-circle"></i>
        <strong>提示：</strong>
        库存数量不能为负数，修改后会立即生效。
    </div>
</form>
