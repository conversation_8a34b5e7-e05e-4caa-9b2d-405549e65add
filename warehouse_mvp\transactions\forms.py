from django import forms
from django.core.exceptions import ValidationError
from .models import Transaction
from products.models import Product
from inventory.models import Inventory


class ProductChoiceField(forms.ModelChoiceField):
    """支持商品名称搜索的自定义选择字段"""

    def label_from_instance(self, obj):
        """返回商品名称(SKU)格式的标签"""
        return f"{obj.name} ({obj.sku})"

class TransactionForm(forms.ModelForm):
    """进出库记录表单"""

    # 使用自定义的商品选择字段
    product = ProductChoiceField(
        queryset=Product.objects.all(),
        widget=forms.Select(attrs={
            'class': 'form-select product-search',
            'id': 'id_product',
            'data-search-url': '/products/api/search/',
        }),
        label='商品',
        help_text='输入商品名称进行搜索'
    )

    class Meta:
        model = Transaction
        fields = ['product', 'transaction_type', 'quantity', 'reason', 'notes']
        widgets = {
            'transaction_type': forms.Select(attrs={
                'class': 'form-select',
                'id': 'id_transaction_type'
            }),
            'quantity': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '1',
                'placeholder': '请输入数量'
            }),
            'reason': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '请输入原因'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': '备注信息（可选）'
            })
        }
        labels = {
            'transaction_type': '操作类型',
            'quantity': '数量',
            'reason': '原因',
            'notes': '备注'
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # 商品查询集已在字段定义中设置
    
    def clean(self):
        """表单验证"""
        cleaned_data = super().clean()
        product = cleaned_data.get('product')
        transaction_type = cleaned_data.get('transaction_type')
        quantity = cleaned_data.get('quantity')
        
        if product and transaction_type and quantity:
            try:
                inventory = Inventory.objects.get(product=product)
                
                # 如果是出库操作，检查库存是否足够
                if transaction_type == 'OUT':
                    if inventory.quantity < quantity:
                        raise ValidationError(
                            f'库存不足！当前库存：{inventory.quantity}，请求出库：{quantity}'
                        )
                    
                    # 检查出库后是否会导致负库存
                    if inventory.quantity - quantity < 0:
                        raise ValidationError(
                            '出库数量过大，会导致库存为负数！'
                        )
                
                # 入库操作无需检查上限，可以无限入库
                        
            except Inventory.DoesNotExist:
                raise ValidationError(f'商品 {product.name} 的库存记录不存在！')
        
        return cleaned_data
    
    def get_current_stock(self):
        """获取当前库存信息（用于前端显示）"""
        product = self.cleaned_data.get('product') if hasattr(self, 'cleaned_data') else None
        if product:
            try:
                inventory = Inventory.objects.get(product=product)
                return {
                    'current': inventory.quantity,
                    'min_stock': inventory.min_stock,
                    'max_stock': inventory.max_stock,
                    'status': inventory.stock_status
                }
            except Inventory.DoesNotExist:
                return None
        return None
