"""
批量交易处理服务
提供优化的批量出入库操作处理，使用Django的bulk操作替代逐个数据库操作
"""
from django.db import transaction
from django.db.models import F
from inventory.models import Inventory
from .models import Transaction
from users.signals import set_bulk_operation_context, clear_bulk_operation_context
from users.services import AuditService


class BatchTransactionService:
    """批量交易处理服务"""
    
    @staticmethod
    def create_batch_optimized(transactions_data, user):
        """
        优化的批量创建交易记录
        
        Args:
            transactions_data: 交易数据列表
            user: 操作用户
            
        Returns:
            dict: 包含创建统计和详情的结果
        """
        created_count = 0
        skipped_count = 0
        details = []
        created_transactions = []
        
        try:
            with transaction.atomic():
                # 设置批量操作上下文，禁用单个审计日志
                set_bulk_operation_context(True)
                
                # 第一步：准备Transaction对象列表
                transactions_to_create = []
                inventory_updates = {}
                
                for data in transactions_data:
                    try:
                        # 验证数据
                        product = data['product']
                        transaction_type = 'IN' if data['transaction_type'] == '入库' else 'OUT'
                        quantity = data['quantity']
                        
                        # 获取或初始化库存信息
                        if product.id not in inventory_updates:
                            try:
                                inventory = Inventory.objects.select_for_update().get(product=product)
                                inventory_updates[product.id] = {
                                    'inventory': inventory,
                                    'original_quantity': inventory.quantity,
                                    'current_quantity': inventory.quantity,
                                    'transactions': []
                                }
                            except Inventory.DoesNotExist:
                                if transaction_type == 'OUT':
                                    raise Exception(f'商品 {product.name} 库存记录不存在，无法执行出库操作')
                                # 为入库操作创建新的库存记录
                                inventory = Inventory(
                                    product=product,
                                    quantity=0,
                                    min_stock=10,
                                    max_stock=1000
                                )
                                inventory_updates[product.id] = {
                                    'inventory': inventory,
                                    'original_quantity': 0,
                                    'current_quantity': 0,
                                    'transactions': [],
                                    'is_new': True
                                }
                        
                        # 计算库存变化
                        inventory_info = inventory_updates[product.id]
                        stock_before = inventory_info['current_quantity']
                        
                        if transaction_type == 'IN':
                            stock_after = stock_before + quantity
                        else:  # OUT
                            if stock_before < quantity:
                                raise Exception(f'库存不足，当前库存：{stock_before}，需要出库：{quantity}')
                            stock_after = stock_before - quantity
                        
                        # 更新当前库存数量
                        inventory_info['current_quantity'] = stock_after
                        
                        # 创建Transaction对象
                        transaction_obj = Transaction(
                            product=product,
                            transaction_type=transaction_type,
                            quantity=quantity,
                            stock_before=stock_before,
                            stock_after=stock_after,
                            reason=data['reason'],
                            notes=data.get('notes', ''),
                            operator=user,
                            created_by=user,
                            updated_by=user
                        )
                        
                        transactions_to_create.append(transaction_obj)
                        inventory_info['transactions'].append(transaction_obj)
                        
                    except Exception as e:
                        skipped_count += 1
                        details.append(f'✗ {data.get("sku", "未知商品")}：{str(e)}')
                        continue
                
                # 第二步：批量创建Transaction记录
                if transactions_to_create:
                    created_transactions = Transaction.objects.bulk_create(transactions_to_create)
                    created_count = len(created_transactions)
                
                # 第三步：批量更新库存
                inventories_to_update = []
                inventories_to_create = []
                
                for product_id, inventory_info in inventory_updates.items():
                    inventory = inventory_info['inventory']
                    inventory.quantity = inventory_info['current_quantity']
                    
                    if inventory_info.get('is_new'):
                        inventories_to_create.append(inventory)
                    else:
                        inventories_to_update.append(inventory)
                
                # 批量创建新库存记录
                if inventories_to_create:
                    Inventory.objects.bulk_create(inventories_to_create)
                
                # 批量更新现有库存记录
                if inventories_to_update:
                    Inventory.objects.bulk_update(inventories_to_update, ['quantity'])
                
                # 第四步：创建批量审计日志
                if created_transactions:
                    BatchTransactionService.create_batch_audit_log(created_transactions, user)
                
                # 生成成功详情
                for product_id, inventory_info in inventory_updates.items():
                    for trans in inventory_info['transactions']:
                        details.append(f'✓ {trans.product.name}：{trans.get_transaction_type_display()} {trans.quantity}')
                
        except Exception as e:
            # 如果发生错误，记录错误并重新抛出
            details.append(f'✗ 批量操作失败：{str(e)}')
            raise
        finally:
            # 清理批量操作上下文
            clear_bulk_operation_context()
        
        return {
            'created_count': created_count,
            'skipped_count': skipped_count,
            'details': details,
            'transactions': created_transactions
        }
    
    @staticmethod
    def create_batch_audit_log(transactions, user):
        """
        创建批量操作的汇总审计日志

        Args:
            transactions: 交易记录列表
            user: 操作用户
        """
        if not transactions:
            return

        # 使用AuditService的专用批量审计日志方法
        return AuditService.log_batch_transaction(
            transactions=transactions,
            user=user,
            extra_data={
                'batch_processor': 'BatchTransactionService',
                'optimization_version': '1.0'
            }
        )
