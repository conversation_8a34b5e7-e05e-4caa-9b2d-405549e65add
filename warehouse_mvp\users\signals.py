"""
Django信号处理器模块
自动捕获模型变更并记录审计日志
"""
import threading
from django.db.models.signals import post_save, post_delete, pre_save
from django.dispatch import receiver
from django.contrib.auth.models import User
from products.models import Product
from inventory.models import Inventory
from transactions.models import Transaction
from .models import UserProfile
from .services import AuditService


# 存储更新前的实例数据
_pre_save_instances = {}

# 存储admin操作标记，避免重复记录
_admin_logged_actions = set()

# 线程本地存储，用于批量操作上下文管理
_bulk_operation_context = threading.local()


def set_bulk_operation_context(enabled=True):
    """
    设置批量操作上下文

    Args:
        enabled (bool): 是否启用批量操作模式
    """
    _bulk_operation_context.enabled = enabled


def is_bulk_operation():
    """
    检查是否在批量操作中

    Returns:
        bool: 如果当前在批量操作中返回True，否则返回False
    """
    return getattr(_bulk_operation_context, 'enabled', False)


def clear_bulk_operation_context():
    """清理批量操作上下文"""
    if hasattr(_bulk_operation_context, 'enabled'):
        delattr(_bulk_operation_context, 'enabled')


def _get_admin_user_from_log_entry(instance):
    """尝试从Django admin日志中获取操作用户"""
    try:
        from django.contrib.admin.models import LogEntry, DELETION
        from django.contrib.contenttypes.models import ContentType

        content_type = ContentType.objects.get_for_model(instance)

        # 查找最近的删除日志
        recent_log = LogEntry.objects.filter(
            content_type=content_type,
            object_id=str(instance.pk),
            action_flag=DELETION
        ).order_by('-action_time').first()

        if recent_log:
            return recent_log.user

    except Exception:
        pass

    return None


def _is_admin_action_logged(instance):
    """检查admin操作是否已经记录"""
    key = f"{instance.__class__.__name__}_{instance.pk}_delete"
    if key in _admin_logged_actions:
        _admin_logged_actions.discard(key)  # 使用后移除
        return True
    return False


def _mark_admin_action_logged(instance, action='delete'):
    """标记admin操作已记录"""
    key = f"{instance.__class__.__name__}_{instance.pk}_{action}"
    _admin_logged_actions.add(key)


@receiver(pre_save, sender=Product)
@receiver(pre_save, sender=Inventory)
@receiver(pre_save, sender=Transaction)
@receiver(pre_save, sender=UserProfile)
def store_pre_save_instance(sender, instance, **kwargs):
    """
    在保存前存储实例的原始数据，用于计算变更
    """
    if instance.pk:  # 只对更新操作存储原始数据
        try:
            original_instance = sender.objects.get(pk=instance.pk)
            _pre_save_instances[f"{sender.__name__}_{instance.pk}"] = original_instance
        except sender.DoesNotExist:
            pass


@receiver(post_save, sender=Product)
def log_product_changes(sender, instance, created, **kwargs):
    """记录商品变更"""
    user = AuditService.get_current_user()
    
    if created:
        # 记录创建操作
        AuditService.log_create(
            instance=instance,
            user=user,
            extra_data={
                'model': 'Product',
                'operation': 'create_product'
            }
        )
        
        # 更新审计字段
        if user and not instance.created_by:
            instance.created_by = user
            instance.updated_by = user
            # 使用update避免触发信号循环
            Product.objects.filter(pk=instance.pk).update(
                created_by=user,
                updated_by=user
            )
    else:
        # 记录更新操作
        key = f"Product_{instance.pk}"
        if key in _pre_save_instances:
            old_instance = _pre_save_instances.pop(key)
            AuditService.log_update(
                old_instance=old_instance,
                new_instance=instance,
                user=user,
                extra_data={
                    'model': 'Product',
                    'operation': 'update_product'
                }
            )
        
        # 更新审计字段
        if user and instance.updated_by != user:
            Product.objects.filter(pk=instance.pk).update(updated_by=user)


@receiver(post_delete, sender=Product)
def log_product_deletion(sender, instance, **kwargs):
    """记录商品删除"""
    import logging
    logger = logging.getLogger(__name__)

    user = AuditService.get_current_user()
    logger.info(f"信号处理器: 商品删除, 当前用户={user}")

    # 如果无法获取当前用户，尝试从Django admin日志中获取
    if user is None:
        user = _get_admin_user_from_log_entry(instance)
        logger.info(f"信号处理器: 从admin日志获取用户={user}")

    # 只有在admin中没有手动记录时才记录（避免重复）
    if not _is_admin_action_logged(instance):
        logger.info(f"信号处理器: 创建审计日志, 用户={user}")
        AuditService.log_delete(
            instance=instance,
            user=user,
            extra_data={
                'model': 'Product',
                'operation': 'delete_product',
                'fallback_logging': user is None
            }
        )
    else:
        logger.info("信号处理器: admin已记录，跳过")


@receiver(post_save, sender=Inventory)
def log_inventory_changes(sender, instance, created, **kwargs):
    """记录库存变更"""
    # 检查是否在批量操作中，如果是则跳过单个审计日志创建
    if is_bulk_operation():
        return

    user = AuditService.get_current_user()

    if created:
        # 记录创建操作
        AuditService.log_create(
            instance=instance,
            user=user,
            extra_data={
                'model': 'Inventory',
                'operation': 'create_inventory'
            }
        )

        # 更新审计字段
        if user and not instance.created_by:
            Inventory.objects.filter(pk=instance.pk).update(
                created_by=user,
                updated_by=user
            )
    else:
        # 记录更新操作
        key = f"Inventory_{instance.pk}"
        if key in _pre_save_instances:
            old_instance = _pre_save_instances.pop(key)
            AuditService.log_update(
                old_instance=old_instance,
                new_instance=instance,
                user=user,
                extra_data={
                    'model': 'Inventory',
                    'operation': 'update_inventory'
                }
            )

        # 更新审计字段
        if user and instance.updated_by != user:
            Inventory.objects.filter(pk=instance.pk).update(updated_by=user)


@receiver(post_delete, sender=Inventory)
def log_inventory_deletion(sender, instance, **kwargs):
    """记录库存删除"""
    user = AuditService.get_current_user()

    # 如果无法获取当前用户，尝试从Django admin日志中获取
    if user is None:
        user = _get_admin_user_from_log_entry(instance)

    # 只有在admin中没有手动记录时才记录（避免重复）
    if not _is_admin_action_logged(instance):
        AuditService.log_delete(
            instance=instance,
            user=user,
            extra_data={
                'model': 'Inventory',
                'operation': 'delete_inventory',
                'fallback_logging': user is None
            }
        )


@receiver(post_save, sender=Transaction)
def log_transaction_changes(sender, instance, created, **kwargs):
    """记录进出库记录变更"""
    # 检查是否在批量操作中，如果是则跳过单个审计日志创建
    if is_bulk_operation():
        return

    user = AuditService.get_current_user()

    if created:
        # 记录创建操作
        AuditService.log_create(
            instance=instance,
            user=user,
            extra_data={
                'model': 'Transaction',
                'operation': 'create_transaction',
                'transaction_type': instance.transaction_type,
                'quantity': instance.quantity
            }
        )

        # 更新审计字段
        if user and not instance.created_by:
            Transaction.objects.filter(pk=instance.pk).update(
                created_by=user,
                updated_by=user
            )
    else:
        # 记录更新操作
        key = f"Transaction_{instance.pk}"
        if key in _pre_save_instances:
            old_instance = _pre_save_instances.pop(key)
            AuditService.log_update(
                old_instance=old_instance,
                new_instance=instance,
                user=user,
                extra_data={
                    'model': 'Transaction',
                    'operation': 'update_transaction'
                }
            )

        # 更新审计字段
        if user and instance.updated_by != user:
            Transaction.objects.filter(pk=instance.pk).update(updated_by=user)


@receiver(post_delete, sender=Transaction)
def log_transaction_deletion(sender, instance, **kwargs):
    """记录进出库记录删除"""
    user = AuditService.get_current_user()
    
    AuditService.log_delete(
        instance=instance,
        user=user,
        extra_data={
            'model': 'Transaction',
            'operation': 'delete_transaction'
        }
    )


@receiver(post_save, sender=UserProfile)
def log_user_profile_changes(sender, instance, created, **kwargs):
    """记录用户资料变更"""
    user = AuditService.get_current_user()
    
    if created:
        # 记录创建操作
        AuditService.log_create(
            instance=instance,
            user=user,
            extra_data={
                'model': 'UserProfile',
                'operation': 'create_user_profile'
            }
        )
    else:
        # 记录更新操作
        key = f"UserProfile_{instance.pk}"
        if key in _pre_save_instances:
            old_instance = _pre_save_instances.pop(key)
            AuditService.log_update(
                old_instance=old_instance,
                new_instance=instance,
                user=user,
                extra_data={
                    'model': 'UserProfile',
                    'operation': 'update_user_profile',
                    'target_user': instance.user.username,
                    'role_change': old_instance.role != instance.role
                }
            )


@receiver(post_delete, sender=UserProfile)
def log_user_profile_deletion(sender, instance, **kwargs):
    """记录用户资料删除"""
    user = AuditService.get_current_user()
    
    AuditService.log_delete(
        instance=instance,
        user=user,
        extra_data={
            'model': 'UserProfile',
            'operation': 'delete_user_profile'
        }
    )


# 用户登录/登出日志记录
from django.contrib.auth.signals import user_logged_in, user_logged_out


@receiver(user_logged_in)
def log_user_login(sender, request, user, **kwargs):
    """记录用户登录"""
    AuditService.log_custom_action(
        action='LOGIN',
        instance=user,
        user=user,
        request=request,
        extra_data={
            'operation': 'user_login',
            'login_time': True
        }
    )


@receiver(user_logged_out)
def log_user_logout(sender, request, user, **kwargs):
    """记录用户登出"""
    if user:  # 确保用户存在
        AuditService.log_custom_action(
            action='LOGOUT',
            instance=user,
            user=user,
            request=request,
            extra_data={
                'operation': 'user_logout',
                'logout_time': True
            }
        )
