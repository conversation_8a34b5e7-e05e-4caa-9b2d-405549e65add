{% load static %}
{% load permission_tags %}
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}仓库管理系统{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet">
    <!-- 自定义样式文件 -->
    <link href="{% static 'css/warehouse.css' %}" rel="stylesheet">
    {% block extra_css %}{% endblock %}
</head>
<body>
    <div class="app-container">
        <!-- 侧边栏 -->
        <nav class="sidebar" id="sidebar">
            <!-- 侧边栏头部 -->
            <div class="sidebar-header">
                <a class="sidebar-brand" href="{% url 'products:list' %}">
                    <i class="bi bi-box-seam brand-icon"></i>
                    <span class="brand-text">仓库管理系统</span>
                </a>
            </div>

            <!-- 侧边栏导航 -->
            <div class="sidebar-nav">
                {% get_user_menu_permissions as menu_perms %}
                <ul class="nav flex-column" id="main-navigation">
                    {% menu_item 'products:list' 'bi bi-box' '商品管理' 'products.view' %}
                    {% menu_item 'inventory:list' 'bi bi-clipboard-data' '库存查看' 'inventory.view' %}
                    {% menu_item 'transactions:list' 'bi bi-arrow-left-right' '进出库记录' 'transactions.view' %}
                </ul>
            </div>

            <!-- 侧边栏底部用户区域 -->
            <div class="sidebar-footer">
                {% if user.is_authenticated %}
                <div class="dropdown">
                    <a class="nav-link dropdown-toggle user-dropdown" href="#" id="sidebarUserDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="bi bi-person-circle user-avatar"></i>
                        <div class="user-info">
                            <span class="user-name">{{ user.username }}</span>
                            {% if user.userprofile.role == 'ADMIN' %}
                                <span class="badge bg-danger admin-badge">管理员</span>
                            {% endif %}
                        </div>
                    </a>
                    <ul class="dropdown-menu user-menu">
                        <li class="dropdown-header">
                            <i class="bi bi-person-circle"></i> {{ user.username }}
                            <small class="text-muted d-block">{{ user.email|default:"未设置邮箱" }}</small>
                            {% user_role_display as role_name %}
                            {% if role_name %}
                                <span class="badge bg-primary mt-1">{{ role_name }}</span>
                            {% endif %}
                        </li>
                        <li><hr class="dropdown-divider"></li>

                        <!-- 权限管理菜单 -->
                        {% can_manage_users as can_manage %}
                        {% if can_manage %}
                            <li><a class="dropdown-item" href="{% url 'users:permission_dashboard' %}">
                                <i class="bi bi-speedometer2 text-primary"></i> 权限仪表板
                            </a></li>
                        {% endif %}

                        {% dropdown_menu_item 'users:management' 'bi bi-people text-users' '用户管理' 'users.view' %}
                        {% dropdown_menu_item 'users:role_management' 'bi bi-shield-check text-warning' '角色管理' 'users.change' %}
                        {% dropdown_menu_item 'users:audit_log' 'bi bi-journal-text text-info' '操作日志' 'users.view_audit' %}

                        <!-- 检查是否有任何管理权限 -->
                        {% can_manage_users as has_user_perms %}
                        {% can_view_audit_log as has_audit_perms %}
                        {% if has_user_perms or has_audit_perms %}
                            <li><hr class="dropdown-divider"></li>
                        {% endif %}

                        <!-- 管理后台 -->
                        {% is_super_admin as is_admin %}
                        {% if is_admin %}
                            <li><a class="dropdown-item" href="/admin/">
                                <i class="bi bi-gear text-secondary"></i> 管理后台
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                        {% endif %}

                        <li><a class="dropdown-item text-danger" href="{% url 'users:logout' %}">
                            <i class="bi bi-box-arrow-right"></i> 退出登录
                        </a></li>
                    </ul>
                </div>
                {% else %}
                <div class="sidebar-login">
                    <a class="nav-link" href="{% url 'users:login' %}">
                        <i class="bi bi-box-arrow-in-right"></i>
                        <span class="nav-text">登录</span>
                    </a>
                </div>
                {% endif %}
            </div>
        </nav>

        <!-- 主内容区域 -->
        <div class="main-content" id="main-content">
            <!-- 侧边栏切换按钮 -->
            <button class="sidebar-toggle" id="sidebar-toggle" type="button">
                <i class="bi bi-list"></i>
            </button>

            <div class="container mt-4">
        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            {% endfor %}
        {% endif %}

            {% block content %}
            {% endblock %}
            </div>
        </div>
    </div>

    <!-- 通用编辑模态框 -->
    <div class="modal fade" id="editModal" tabindex="-1" aria-labelledby="editModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editModalLabel">
                        <i class="bi bi-pencil"></i> 编辑项目
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
                </div>
                <div class="modal-body">
                    <div id="editModalContent">
                        <!-- 动态加载的表单内容 -->
                        <div class="text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <p class="mt-2 text-muted">正在加载表单...</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="bi bi-x-circle"></i> 取消
                    </button>
                    <button type="button" class="btn btn-primary" id="saveEditBtn">
                        <i class="bi bi-check-circle"></i> 保存更改
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Chart.js 图表库 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js"></script>
    <!-- jQuery (Select2 dependency) -->
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <!-- Select2 JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <!-- 自定义JavaScript文件 -->
    <script src="{% static 'js/warehouse.js' %}?v=5.0"></script>

    <script>
        // 初始化动态菜单控制
        document.addEventListener('DOMContentLoaded', function() {
            if (window.WarehouseJS && window.WarehouseJS.initializeDynamicMenuControl) {
                window.WarehouseJS.initializeDynamicMenuControl();
            }

            // 初始化权限管理功能（如果在权限管理页面）
            if (window.WarehouseJS && window.WarehouseJS.initializePermissionManagement) {
                window.WarehouseJS.initializePermissionManagement();
            }
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
