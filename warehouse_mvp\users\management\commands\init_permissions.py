from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from users.models import PagePermission, RolePagePermission, PermissionTemplate
from users.permissions import UserRoles


class Command(BaseCommand):
    help = '初始化页面权限数据'

    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='强制重新创建权限数据',
        )

    def handle(self, *args, **options):
        force = options['force']
        
        self.stdout.write('开始初始化页面权限数据...')
        
        # 创建基础页面权限
        self.create_page_permissions(force)
        
        # 创建角色权限分配
        self.create_role_permissions(force)
        
        # 创建权限模板
        self.create_permission_templates(force)
        
        self.stdout.write(
            self.style.SUCCESS('页面权限数据初始化完成！')
        )

    def create_page_permissions(self, force):
        """创建页面权限"""
        permissions_data = [
            # 商品管理权限
            {
                'name': 'products_list',
                'description': '商品列表页面访问权限',
                'url_pattern': '^/products/$',
                'url_name': 'products:list',
                'app_name': 'products',
                'view_name': 'ProductListView',
                'category': 'products',
                'permission_level': 'role_based',
                'sort_order': 1
            },
            {
                'name': 'products_create',
                'description': '商品创建页面访问权限',
                'url_pattern': '^/products/create/$',
                'url_name': 'products:create',
                'app_name': 'products',
                'view_name': 'ProductCreateView',
                'category': 'products',
                'permission_level': 'role_based',
                'sort_order': 2
            },
            {
                'name': 'products_detail',
                'description': '商品详情页面访问权限',
                'url_pattern': '^/products/\\d+/$',
                'url_name': 'products:detail',
                'app_name': 'products',
                'view_name': 'ProductDetailView',
                'category': 'products',
                'permission_level': 'role_based',
                'sort_order': 3
            },
            {
                'name': 'products_edit',
                'description': '商品编辑页面访问权限',
                'url_pattern': '^/products/\\d+/edit/$',
                'url_name': 'products:edit',
                'app_name': 'products',
                'view_name': 'ProductEditView',
                'category': 'products',
                'permission_level': 'role_based',
                'sort_order': 4
            },
            
            # 库存管理权限
            {
                'name': 'inventory_list',
                'description': '库存列表页面访问权限',
                'url_pattern': '^/inventory/$',
                'url_name': 'inventory:list',
                'app_name': 'inventory',
                'view_name': 'InventoryListView',
                'category': 'inventory',
                'permission_level': 'role_based',
                'sort_order': 1
            },
            {
                'name': 'inventory_adjust',
                'description': '库存调整页面访问权限',
                'url_pattern': '^/inventory/adjust/$',
                'url_name': 'inventory:adjust',
                'app_name': 'inventory',
                'view_name': 'InventoryAdjustView',
                'category': 'inventory',
                'permission_level': 'role_based',
                'sort_order': 2
            },
            
            # 交易管理权限
            {
                'name': 'transactions_list',
                'description': '交易列表页面访问权限',
                'url_pattern': '^/transactions/$',
                'url_name': 'transactions:list',
                'app_name': 'transactions',
                'view_name': 'TransactionListView',
                'category': 'transactions',
                'permission_level': 'role_based',
                'sort_order': 1
            },
            
            # 用户管理权限
            {
                'name': 'users_list',
                'description': '用户列表页面访问权限',
                'url_pattern': '^/users/$',
                'url_name': 'users:list',
                'app_name': 'users',
                'view_name': 'UserListView',
                'category': 'users',
                'permission_level': 'role_based',
                'sort_order': 1
            },
            {
                'name': 'users_profile',
                'description': '用户资料页面访问权限',
                'url_pattern': '^/users/profile/$',
                'url_name': 'users:profile',
                'app_name': 'users',
                'view_name': 'UserProfileView',
                'category': 'users',
                'permission_level': 'authenticated',
                'sort_order': 2
            },
        ]
        
        created_count = 0
        for perm_data in permissions_data:
            permission, created = PagePermission.objects.get_or_create(
                name=perm_data['name'],
                app_name=perm_data['app_name'],
                defaults=perm_data
            )
            
            if created or force:
                if force and not created:
                    for key, value in perm_data.items():
                        setattr(permission, key, value)
                    permission.save()
                created_count += 1
                self.stdout.write(f'  创建页面权限: {permission.name}')
        
        self.stdout.write(f'共创建/更新了 {created_count} 个页面权限')

    def create_role_permissions(self, force):
        """创建角色权限分配"""
        # 获取管理员用户作为授权人
        try:
            admin_user = User.objects.get(username='admin')
        except User.DoesNotExist:
            admin_user = None
        
        # 角色权限配置
        role_permissions = {
            UserRoles.SUPER_ADMIN: [
                'products_list', 'products_create', 'products_detail', 'products_edit',
                'inventory_list', 'inventory_adjust',
                'transactions_list',
                'users_list', 'users_profile'
            ],
            UserRoles.WAREHOUSE_MANAGER: [
                'products_list', 'products_create', 'products_detail', 'products_edit',
                'inventory_list', 'inventory_adjust',
                'transactions_list',
                'users_profile'
            ],
            UserRoles.WAREHOUSE_OPERATOR: [
                'products_list', 'products_detail',
                'inventory_list',
                'transactions_list',
                'users_profile'
            ],
            UserRoles.VIEWER: [
                'products_list', 'products_detail',
                'inventory_list',
                'users_profile'
            ]
        }
        
        created_count = 0
        for role_code, permission_names in role_permissions.items():
            for permission_name in permission_names:
                try:
                    page_permission = PagePermission.objects.get(name=permission_name)
                    
                    role_perm, created = RolePagePermission.objects.get_or_create(
                        role=role_code,
                        page_permission=page_permission,
                        defaults={
                            'is_granted': True,
                            'grant_source': 'default',
                            'granted_by': admin_user,
                            'notes': '系统初始化创建'
                        }
                    )
                    
                    if created or force:
                        if force and not created:
                            role_perm.is_granted = True
                            role_perm.grant_source = 'default'
                            role_perm.granted_by = admin_user
                            role_perm.save()
                        created_count += 1
                        role_name = UserRoles.get_display_name(role_code)
                        self.stdout.write(f'  分配权限: {role_name} -> {permission_name}')
                        
                except PagePermission.DoesNotExist:
                    self.stdout.write(
                        self.style.WARNING(f'页面权限不存在: {permission_name}')
                    )
        
        self.stdout.write(f'共创建/更新了 {created_count} 个角色权限分配')

    def create_permission_templates(self, force):
        """创建权限模板"""
        templates_data = [
            {
                'name': '基础商品管理权限',
                'description': '包含商品基本查看和管理权限',
                'category': 'products',
                'target_roles': [UserRoles.WAREHOUSE_MANAGER, UserRoles.WAREHOUSE_OPERATOR],
                'permissions_config': {
                    'page.products.products_list': {'granted': True},
                    'page.products.products_detail': {'granted': True}
                }
            },
            {
                'name': '完整商品管理权限',
                'description': '包含商品的所有管理权限',
                'category': 'products',
                'target_roles': [UserRoles.SUPER_ADMIN, UserRoles.WAREHOUSE_MANAGER],
                'permissions_config': {
                    'page.products.products_list': {'granted': True},
                    'page.products.products_create': {'granted': True},
                    'page.products.products_detail': {'granted': True},
                    'page.products.products_edit': {'granted': True}
                }
            },
            {
                'name': '库存管理权限',
                'description': '包含库存查看和调整权限',
                'category': 'inventory',
                'target_roles': [UserRoles.WAREHOUSE_MANAGER, UserRoles.WAREHOUSE_OPERATOR],
                'permissions_config': {
                    'page.inventory.inventory_list': {'granted': True},
                    'page.inventory.inventory_adjust': {'granted': True}
                }
            }
        ]
        
        # 获取管理员用户作为创建人
        try:
            admin_user = User.objects.get(username='admin')
        except User.DoesNotExist:
            admin_user = None
        
        created_count = 0
        for template_data in templates_data:
            template, created = PermissionTemplate.objects.get_or_create(
                name=template_data['name'],
                defaults={
                    **template_data,
                    'created_by': admin_user
                }
            )
            
            if created or force:
                if force and not created:
                    for key, value in template_data.items():
                        setattr(template, key, value)
                    template.save()
                created_count += 1
                self.stdout.write(f'  创建权限模板: {template.name}')
        
        self.stdout.write(f'共创建/更新了 {created_count} 个权限模板')
