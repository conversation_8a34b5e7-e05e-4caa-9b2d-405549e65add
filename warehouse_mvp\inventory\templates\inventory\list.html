{% extends 'products/base.html' %}
{% load inventory_extras %}

{% block title %}库存查看 - 仓库管理系统{% endblock %}

{% block extra_css %}
<style>
    /* 库存编辑样式 */
    .inventory-quantity-container {
        position: relative;
    }

    .quantity-display {
        display: flex;
        align-items: center;
    }

    .edit-quantity-btn {
        opacity: 0.7;
        /* 移除transition动画效果 */
    }

    .inventory-quantity-container:hover .edit-quantity-btn {
        opacity: 1;
    }

    .edit-quantity-btn:hover {
        opacity: 1 !important;
    }

    .quantity-edit {
        min-width: 150px;
    }

    .quantity-input {
        text-align: center;
        font-weight: bold;
    }

    .quantity-input:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(var(--primary-color-rgb), 0.25);
    }

    /* 表格行动画 - 已移除 */
    .table tbody tr {
        /* 移除transition动画效果 */
    }

    .table tbody tr.updating {
        background-color: rgba(var(--primary-color-rgb), 0.1);
    }

    /* 成功更新动画 - 已移除 */
</style>
{% endblock %}

{% block content %}
{% include 'inventory/_content.html' %}
{% endblock %}

{% block extra_js %}
<!-- JavaScript已移至_content.html中，避免重复 -->
{% endblock %}
