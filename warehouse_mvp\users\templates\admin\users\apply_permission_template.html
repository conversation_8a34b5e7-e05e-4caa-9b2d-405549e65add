{% extends "admin/base_site.html" %}
{% load i18n admin_urls static admin_modify %}

{% block title %}{{ title }} | {{ site_title|default:_('Django site admin') }}{% endblock %}

{% block breadcrumbs %}
<div class="breadcrumbs">
    <a href="{% url 'admin:index' %}">{% trans 'Home' %}</a>
    &rsaquo; <a href="{% url 'admin:users_rolepagepermission_changelist' %}">角色页面权限</a>
    &rsaquo; {{ title }}
</div>
{% endblock %}

{% block content %}
<div class="module aligned">
    <h1>{{ title }}</h1>
    
    <div class="form-row">
        <div class="field-box">
            <h3>选择权限模板</h3>
            <p>将选中的权限模板应用到相关角色。</p>
            
            <form method="post">
                {% csrf_token %}
                
                <!-- 保持选中的权限 -->
                {% for obj in queryset %}
                    <input type="hidden" name="{{ action_checkbox_name }}" value="{{ obj.pk }}">
                {% endfor %}
                
                <div class="form-row">
                    <label for="template_id">权限模板:</label>
                    <select name="template_id" id="template_id" required>
                        <option value="">请选择权限模板</option>
                        {% for template in templates %}
                            <option value="{{ template.id }}">
                                {{ template.name }} ({{ template.category }})
                                {% if template.target_roles %}
                                    - 适用角色: 
                                    {% for role in template.target_roles %}
                                        {{ role }}{% if not forloop.last %}, {% endif %}
                                    {% endfor %}
                                {% endif %}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="form-row">
                    <h4>将应用到以下角色权限:</h4>
                    <ul>
                        {% for obj in queryset %}
                            <li>{{ obj.get_role_display }} - {{ obj.page_permission.name }}</li>
                        {% endfor %}
                    </ul>
                </div>
                
                <div class="submit-row">
                    <input type="submit" name="apply" value="应用模板" class="default">
                    <a href="{% url 'admin:users_rolepagepermission_changelist' %}" class="button cancel-link">取消</a>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const templateSelect = document.getElementById('template_id');
    
    templateSelect.addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        if (selectedOption.value) {
            // 可以在这里添加模板预览功能
            console.log('选择了模板:', selectedOption.text);
        }
    });
});
</script>
{% endblock %}
