"""
权限相关的模板标签
"""
from django import template
from django.core.cache import cache
from django.conf import settings
from django.contrib.auth.models import User
from ..permissions import PermissionManager, UserRoles
from ..models import PagePermission, RolePagePermission

register = template.Library()


@register.simple_tag(takes_context=True)
def has_permission(context, permission_key, app_name=None):
    """
    检查当前用户是否有指定权限

    用法:
    {% has_permission 'products.view' as can_view_products %}
    {% has_permission 'view' 'products' as can_view_products %}
    """
    request = context.get('request')
    if not request or not request.user.is_authenticated:
        return False

    if app_name:
        return PermissionManager.user_has_permission(request.user, app_name, permission_key)
    else:
        # 如果没有app_name，尝试从permission_key中解析
        if '.' in permission_key:
            parts = permission_key.split('.')
            if len(parts) == 2:
                return PermissionManager.user_has_permission(request.user, parts[0], parts[1])
        # 默认使用products应用
        return PermissionManager.user_has_permission(request.user, 'products', permission_key)


@register.simple_tag(takes_context=True)
def has_page_permission(context, url_name, app_name=None):
    """
    检查当前用户是否有访问指定页面的权限
    
    用法:
    {% has_page_permission 'list' 'products' as can_access_products %}
    {% has_page_permission 'products:list' as can_access_products %}
    """
    request = context.get('request')
    if not request or not request.user.is_authenticated:
        return False
    
    # 构建完整的URL名称
    if app_name and ':' not in url_name:
        full_url_name = f"{app_name}:{url_name}"
    else:
        full_url_name = url_name
    
    return PermissionManager.check_page_permission(request.user, {
        'url_name': full_url_name,
        'app_name': app_name or full_url_name.split(':')[0] if ':' in full_url_name else None
    })


@register.simple_tag(takes_context=True)
def user_role_display(context):
    """
    获取当前用户的角色显示名称
    
    用法:
    {% user_role_display as role_name %}
    """
    request = context.get('request')
    if not request or not request.user.is_authenticated:
        return ''
    
    try:
        user_profile = request.user.userprofile
        return UserRoles.get_display_name(user_profile.role)
    except:
        return ''


@register.simple_tag(takes_context=True)
def user_role_code(context):
    """
    获取当前用户的角色代码
    
    用法:
    {% user_role_code as role_code %}
    """
    request = context.get('request')
    if not request or not request.user.is_authenticated:
        return ''
    
    try:
        user_profile = request.user.userprofile
        return user_profile.role
    except:
        return ''


@register.simple_tag(takes_context=True)
def is_super_admin(context):
    """
    检查当前用户是否是超级管理员
    
    用法:
    {% is_super_admin as is_admin %}
    """
    request = context.get('request')
    if not request or not request.user.is_authenticated:
        return False
    
    try:
        user_profile = request.user.userprofile
        return user_profile.role == UserRoles.SUPER_ADMIN
    except:
        return False


@register.simple_tag(takes_context=True)
def can_manage_users(context):
    """
    检查当前用户是否可以管理用户

    用法:
    {% can_manage_users as can_manage %}
    """
    request = context.get('request')
    if not request or not request.user.is_authenticated:
        return False

    return PermissionManager.user_has_permission(request.user, 'users', 'change')


@register.simple_tag(takes_context=True)
def can_view_audit_log(context):
    """
    检查当前用户是否可以查看审计日志

    用法:
    {% can_view_audit_log as can_view %}
    """
    request = context.get('request')
    if not request or not request.user.is_authenticated:
        return False

    return PermissionManager.user_has_permission(request.user, 'users', 'view_audit')


@register.inclusion_tag('users/menu_item.html', takes_context=True)
def menu_item(context, url_name, icon_class, text, permission_key=None, app_name=None, css_class=''):
    """
    渲染带权限控制的菜单项
    
    用法:
    {% menu_item 'products:list' 'bi-box' '商品管理' 'products.view' %}
    """
    request = context.get('request')
    
    # 检查权限
    has_access = True
    if permission_key and request and request.user.is_authenticated:
        if app_name:
            has_access = PermissionManager.user_has_permission(request.user, app_name, permission_key)
        else:
            # 如果没有app_name，尝试从permission_key中解析
            if '.' in permission_key:
                parts = permission_key.split('.')
                if len(parts) == 2:
                    has_access = PermissionManager.user_has_permission(request.user, parts[0], parts[1])
                else:
                    has_access = False
            else:
                # 默认使用products应用
                has_access = PermissionManager.user_has_permission(request.user, 'products', permission_key)
    elif permission_key and (not request or not request.user.is_authenticated):
        has_access = False
    
    # 检查当前页面是否激活
    is_active = False
    if request and hasattr(request, 'resolver_match'):
        if ':' in url_name:
            namespace, view_name = url_name.split(':', 1)
            is_active = (request.resolver_match.namespace == namespace)
        else:
            is_active = (request.resolver_match.url_name == url_name)
    
    return {
        'url_name': url_name,
        'icon_class': icon_class,
        'text': text,
        'has_access': has_access,
        'is_active': is_active,
        'css_class': css_class,
        'request': request
    }


@register.inclusion_tag('users/dropdown_menu_item.html', takes_context=True)
def dropdown_menu_item(context, url_name, icon_class, text, permission_key=None, app_name=None, css_class=''):
    """
    渲染带权限控制的下拉菜单项
    
    用法:
    {% dropdown_menu_item 'users:management' 'bi-people' '用户管理' 'users.view' %}
    """
    request = context.get('request')
    
    # 检查权限
    has_access = True
    if permission_key and request and request.user.is_authenticated:
        if app_name:
            has_access = PermissionManager.user_has_permission(request.user, app_name, permission_key)
        else:
            # 如果没有app_name，尝试从permission_key中解析
            if '.' in permission_key:
                parts = permission_key.split('.')
                if len(parts) == 2:
                    has_access = PermissionManager.user_has_permission(request.user, parts[0], parts[1])
                else:
                    has_access = False
            else:
                # 默认使用products应用
                has_access = PermissionManager.user_has_permission(request.user, 'products', permission_key)
    elif permission_key and (not request or not request.user.is_authenticated):
        has_access = False
    
    return {
        'url_name': url_name,
        'icon_class': icon_class,
        'text': text,
        'has_access': has_access,
        'css_class': css_class,
        'request': request
    }


@register.simple_tag
def get_menu_permissions_cache_key(user_id):
    """
    生成菜单权限缓存键
    
    用法:
    {% get_menu_permissions_cache_key user.id as cache_key %}
    """
    return f"menu_permissions:{user_id}"


@register.simple_tag(takes_context=True)
def get_user_menu_permissions(context):
    """
    获取当前用户的菜单权限（带缓存）
    
    用法:
    {% get_user_menu_permissions as menu_perms %}
    """
    request = context.get('request')
    if not request or not request.user.is_authenticated:
        return {}
    
    cache_key = f"menu_permissions:{request.user.id}"
    cached_permissions = cache.get(cache_key)
    
    if cached_permissions is not None:
        return cached_permissions
    
    # 构建菜单权限字典
    permissions = {
        'products': {
            'view': PermissionManager.user_has_permission(request.user, 'products', 'view'),
            'add': PermissionManager.user_has_permission(request.user, 'products', 'add'),
            'change': PermissionManager.user_has_permission(request.user, 'products', 'change'),
            'delete': PermissionManager.user_has_permission(request.user, 'products', 'delete'),
        },
        'inventory': {
            'view': PermissionManager.user_has_permission(request.user, 'inventory', 'view'),
            'change': PermissionManager.user_has_permission(request.user, 'inventory', 'change'),
        },
        'transactions': {
            'view': PermissionManager.user_has_permission(request.user, 'transactions', 'view'),
            'add': PermissionManager.user_has_permission(request.user, 'transactions', 'add'),
        },
        'users': {
            'view': PermissionManager.user_has_permission(request.user, 'users', 'view'),
            'change': PermissionManager.user_has_permission(request.user, 'users', 'change'),
            'view_audit': PermissionManager.user_has_permission(request.user, 'users', 'view_audit'),
        },
        'admin': {
            'access': request.user.is_superuser or PermissionManager.user_has_permission(request.user, 'admin', 'access'),
        }
    }
    
    # 缓存权限信息（5分钟）
    cache.set(cache_key, permissions, 300)
    
    return permissions


@register.simple_tag
def clear_user_menu_cache(user_id):
    """
    清除用户菜单权限缓存
    
    用法:
    {% clear_user_menu_cache user.id %}
    """
    cache_key = f"menu_permissions:{user_id}"
    cache.delete(cache_key)
    return ''


@register.filter
def has_any_permission(permissions_dict, app_name):
    """
    检查是否有应用的任何权限
    
    用法:
    {{ menu_perms|has_any_permission:'products' }}
    """
    if not permissions_dict or app_name not in permissions_dict:
        return False
    
    app_permissions = permissions_dict[app_name]
    return any(app_permissions.values())


@register.simple_tag(takes_context=True)
def navigation_menu(context):
    """
    生成完整的导航菜单数据
    
    用法:
    {% navigation_menu as nav_menu %}
    """
    request = context.get('request')
    if not request:
        return []
    
    # 获取用户权限
    menu_perms = get_user_menu_permissions(context)
    
    # 定义菜单结构
    menu_items = [
        {
            'name': 'products',
            'url': 'products:list',
            'icon': 'bi-box',
            'text': '商品管理',
            'permission': 'products.view',
            'visible': menu_perms.get('products', {}).get('view', False),
            'children': []
        },
        {
            'name': 'inventory',
            'url': 'inventory:list',
            'icon': 'bi-clipboard-data',
            'text': '库存查看',
            'permission': 'inventory.view',
            'visible': menu_perms.get('inventory', {}).get('view', False),
            'children': []
        },
        {
            'name': 'transactions',
            'url': 'transactions:list',
            'icon': 'bi-arrow-left-right',
            'text': '进出库记录',
            'permission': 'transactions.view',
            'visible': menu_perms.get('transactions', {}).get('view', False),
            'children': []
        }
    ]
    
    # 过滤可见的菜单项
    visible_menu = [item for item in menu_items if item['visible']]
    
    return visible_menu
