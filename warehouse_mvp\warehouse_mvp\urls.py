"""warehouse_mvp URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.1/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""

from django.contrib import admin
from django.urls import path, include
from django.shortcuts import redirect
from .spa_views import load_page_content

def home_redirect(request):
    """首页重定向到商品列表"""
    return redirect('products:list')

urlpatterns = [
    path("admin/", admin.site.urls),
    path("", home_redirect, name='home'),
    path("products/", include('products.urls')),
    path("inventory/", include('inventory.urls')),
    path("transactions/", include('transactions.urls')),
    path("users/", include('users.urls')),
    # SPA API
    path("api/spa/load-content/", load_page_content, name='spa_load_content'),
]
