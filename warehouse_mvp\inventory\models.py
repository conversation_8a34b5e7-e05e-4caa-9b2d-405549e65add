from django.db import models
from django.db.models.signals import post_save
from django.dispatch import receiver
from django.contrib.auth.models import User
from products.models import Product

# Create your models here.

class Inventory(models.Model):
    """库存模型"""
    product = models.OneToOneField(Product, on_delete=models.CASCADE, verbose_name='商品')
    quantity = models.PositiveIntegerField(default=0, verbose_name='库存数量')
    min_stock = models.PositiveIntegerField(default=10, verbose_name='最低库存')
    max_stock = models.IntegerField(default=1000, verbose_name='最高库存')

    # 审计字段
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='created_inventories',
        verbose_name='创建人'
    )
    updated_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='updated_inventories',
        verbose_name='最后修改人'
    )

    class Meta:
        verbose_name = '库存'
        verbose_name_plural = '库存'

    def __str__(self):
        return f"{self.product.name} - 库存: {self.quantity}"

    @property
    def is_low_stock(self):
        """判断是否低库存"""
        return self.quantity <= self.min_stock

    @property
    def stock_status(self):
        """库存状态"""
        if self.max_stock == 0:
            return "无库存商品"
        elif self.quantity <= self.min_stock:
            return "低库存"
        elif self.quantity >= self.max_stock:
            return "库存充足"
        else:
            return "正常"


@receiver(post_save, sender=Product)
def create_inventory_for_product(sender, instance, created, **kwargs):
    """当创建商品时自动创建库存记录"""
    if created:
        Inventory.objects.create(
            product=instance,
            quantity=0,
            min_stock=10,
            max_stock=1000
        )
