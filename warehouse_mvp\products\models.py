from django.db import models
from django.contrib.auth.models import User
import re

# Create your models here.

class Product(models.Model):
    """商品模型"""
    name = models.CharField(max_length=200, verbose_name='商品名称')
    sku = models.CharField(max_length=100, unique=True, verbose_name='商品编码')
    description = models.TextField(blank=True, verbose_name='商品描述')
    price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='价格')

    # 审计字段
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='created_products',
        verbose_name='创建人'
    )
    updated_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='updated_products',
        verbose_name='最后修改人'
    )

    class Meta:
        verbose_name = '商品'
        verbose_name_plural = '商品'
        ordering = ['-created_at']

    def generate_sku(self):
        """基于商品名称自动生成唯一的SKU"""
        try:
            from pypinyin import lazy_pinyin, Style
        except ImportError:
            # 如果pypinyin未安装，使用备用方案
            prefix = re.sub(r'[^A-Za-z0-9]', '', self.name.upper())[:6]
        else:
            # 提取商品名称的拼音首字母
            pinyin_list = lazy_pinyin(self.name, style=Style.FIRST_LETTER)
            prefix = ''.join([p.upper() for p in pinyin_list if p.isalpha()])[:6]

        # 如果拼音首字母不足，使用商品名称前几个字符
        if len(prefix) < 3:
            prefix = re.sub(r'[^A-Za-z0-9]', '', self.name.upper())[:6]

        # 确保前缀至少3个字符，不足的用X补充
        prefix = prefix.ljust(3, 'X')[:6]

        # 查找相同前缀的现有SKU，找到最大序号
        existing_skus = Product.objects.filter(
            sku__startswith=prefix
        ).values_list('sku', flat=True)

        max_num = 0
        for sku in existing_skus:
            # 提取SKU末尾的数字
            match = re.search(r'(\d+)$', sku)
            if match:
                max_num = max(max_num, int(match.group(1)))

        # 生成新的SKU：前缀 + 递增的3位数字
        new_sku = f"{prefix}{str(max_num + 1).zfill(3)}"

        # 确保SKU长度不超过100字符（数据库约束）
        if len(new_sku) > 100:
            # 如果超长，截断前缀
            prefix = prefix[:97]  # 为3位数字留空间
            new_sku = f"{prefix}{str(max_num + 1).zfill(3)}"

        return new_sku

    def save(self, *args, **kwargs):
        """重写save方法，自动生成SKU"""
        # 如果SKU为空或者是新创建的商品，自动生成SKU
        if not self.sku:
            self.sku = self.generate_sku()
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.name} ({self.sku})"
