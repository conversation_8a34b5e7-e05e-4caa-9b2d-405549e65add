{% extends 'products/base.html' %}
{% load static %}

{% block title %}角色权限管理 - 仓库管理系统{% endblock %}

{% block extra_css %}
<style>
.permission-matrix {
    font-size: 0.9rem;
}
.permission-matrix th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-align: center;
    padding: 8px;
}
.permission-matrix td {
    text-align: center;
    padding: 6px;
    border: 1px solid #dee2e6;
}
.permission-yes {
    color: #28a745;
    font-weight: bold;
}
.permission-no {
    color: #dc3545;
    font-weight: bold;
}
.role-badge {
    font-size: 0.8rem;
    padding: 4px 8px;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="bi bi-shield-check text-primary"></i>
                        角色权限管理
                    </h2>
                    <p class="text-muted mb-0">管理用户角色和权限分配</p>
                </div>
                <div>
                    <a href="{% url 'users:init_permissions' %}" class="btn btn-warning">
                        <i class="bi bi-arrow-clockwise"></i> 初始化权限系统
                    </a>
                    <a href="{% url 'users:management' %}" class="btn btn-outline-primary">
                        <i class="bi bi-people"></i> 用户管理
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 权限矩阵 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-table"></i> 权限矩阵
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered permission-matrix">
                            <thead>
                                <tr>
                                    <th>功能模块</th>
                                    {% for role_code, role_name in available_roles %}
                                        <th>{{ role_name }}</th>
                                    {% endfor %}
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><strong>商品管理</strong></td>
                                    <td class="permission-yes">增删改查</td>
                                    <td class="permission-yes">增删改查</td>
                                    <td class="permission-no">仅查看</td>
                                    <td class="permission-no">仅查看</td>
                                </tr>
                                <tr>
                                    <td><strong>库存管理</strong></td>
                                    <td class="permission-yes">增删改查</td>
                                    <td class="permission-yes">增删改查</td>
                                    <td class="permission-yes">修改查看</td>
                                    <td class="permission-no">仅查看</td>
                                </tr>
                                <tr>
                                    <td><strong>进出库记录</strong></td>
                                    <td class="permission-yes">增删改查</td>
                                    <td class="permission-yes">增删改查</td>
                                    <td class="permission-yes">增改查看</td>
                                    <td class="permission-no">仅查看</td>
                                </tr>
                                <tr>
                                    <td><strong>用户管理</strong></td>
                                    <td class="permission-yes">增删改查</td>
                                    <td class="permission-no">无权限</td>
                                    <td class="permission-no">无权限</td>
                                    <td class="permission-no">无权限</td>
                                </tr>
                                <tr>
                                    <td><strong>操作日志</strong></td>
                                    <td class="permission-yes">查看</td>
                                    <td class="permission-yes">查看</td>
                                    <td class="permission-no">无权限</td>
                                    <td class="permission-no">无权限</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 用户角色分配 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-person-gear"></i> 用户角色分配
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>用户名</th>
                                    <th>邮箱</th>
                                    <th>当前角色</th>
                                    <th>注册时间</th>
                                    <th>最后登录</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for profile in user_profiles %}
                                <tr>
                                    <td>
                                        <strong>{{ profile.user.username }}</strong>
                                        {% if profile.user.is_superuser %}
                                            <span class="badge bg-danger ms-2">超级用户</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ profile.user.email|default:"-" }}</td>
                                    <td>
                                        <span class="badge role-badge 
                                            {% if profile.role == 'super_admin' %}bg-danger
                                            {% elif profile.role == 'warehouse_manager' %}bg-primary
                                            {% elif profile.role == 'warehouse_operator' %}bg-success
                                            {% else %}bg-secondary{% endif %}">
                                            {{ profile.get_role_display_name }}
                                        </span>
                                    </td>
                                    <td>{{ profile.created_at|date:"Y-m-d H:i" }}</td>
                                    <td>{{ profile.user.last_login|date:"Y-m-d H:i"|default:"-" }}</td>
                                    <td>
                                        {% if profile.user != request.user %}
                                        <select class="form-select form-select-sm" 
                                                onchange="changeUserRole({{ profile.user.id }}, this.value)"
                                                style="width: auto; display: inline-block;">
                                            {% for role_code, role_name in available_roles %}
                                                <option value="{{ role_code }}" 
                                                        {% if profile.role == role_code %}selected{% endif %}>
                                                    {{ role_name }}
                                                </option>
                                            {% endfor %}
                                        </select>
                                        {% else %}
                                        <span class="text-muted">当前用户</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="6" class="text-center text-muted py-4">
                                        暂无用户数据
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页 -->
                    {% if is_paginated %}
                    <nav aria-label="用户列表分页">
                        <ul class="pagination justify-content-center">
                            {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page=1">首页</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}">上一页</a>
                                </li>
                            {% endif %}

                            <li class="page-item active">
                                <span class="page-link">
                                    第 {{ page_obj.number }} 页，共 {{ page_obj.paginator.num_pages }} 页
                                </span>
                            </li>

                            {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}">下一页</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">末页</a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function changeUserRole(userId, newRole) {
    if (!confirm('确定要修改该用户的角色吗？')) {
        // 如果取消，恢复原来的选择
        location.reload();
        return;
    }
    
    fetch('{% url "users:assign_role" %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-CSRFToken': '{{ csrf_token }}'
        },
        body: `user_id=${userId}&role=${newRole}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            location.reload();
        } else {
            alert('操作失败：' + data.message);
            location.reload();
        }
    })
    .catch(error => {
        alert('网络错误：' + error);
        location.reload();
    });
}
</script>
{% endblock %}
