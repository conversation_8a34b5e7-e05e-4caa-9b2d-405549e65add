<!-- 分页导航 -->
{% if page_obj.has_other_pages %}
<nav aria-label="商品分页">
    <ul class="pagination justify-content-center">
        {% if page_obj.has_previous %}
            <li class="page-item">
                <a class="page-link ajax-page-link" href="#" data-page="1" aria-label="首页">
                    <span aria-hidden="true">&laquo;&laquo;</span>
                </a>
            </li>
            <li class="page-item">
                <a class="page-link ajax-page-link" href="#" data-page="{{ page_obj.previous_page_number }}" aria-label="上一页">
                    <span aria-hidden="true">&laquo;</span>
                </a>
            </li>
        {% endif %}

        {% for num in page_obj.paginator.page_range %}
            {% if page_obj.number == num %}
                <li class="page-item active">
                    <span class="page-link">{{ num }}</span>
                </li>
            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                <li class="page-item">
                    <a class="page-link ajax-page-link" href="#" data-page="{{ num }}">{{ num }}</a>
                </li>
            {% endif %}
        {% endfor %}

        {% if page_obj.has_next %}
            <li class="page-item">
                <a class="page-link ajax-page-link" href="#" data-page="{{ page_obj.next_page_number }}" aria-label="下一页">
                    <span aria-hidden="true">&raquo;</span>
                </a>
            </li>
            <li class="page-item">
                <a class="page-link ajax-page-link" href="#" data-page="{{ page_obj.paginator.num_pages }}" aria-label="末页">
                    <span aria-hidden="true">&raquo;&raquo;</span>
                </a>
            </li>
        {% endif %}
    </ul>
</nav>

<div class="text-center text-muted mt-2">
    <small>
        显示第 {{ page_obj.start_index }} - {{ page_obj.end_index }} 条，共 {{ page_obj.paginator.count }} 条记录
    </small>
</div>
{% endif %}
