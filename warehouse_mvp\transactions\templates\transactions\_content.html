<!-- 进出库记录页面共享内容 -->

<!-- 页面头部 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="mb-1">
            <i class="bi bi-arrow-left-right text-info me-2"></i>
            进出库记录
        </h2>
        <p class="text-muted mb-0">查看商品进出库历史记录</p>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card stats-card-enhanced text-center">
            <div class="card-body">
                <div class="stats-card-header">
                    <div class="stats-card-icon">
                        <i class="bi bi-list-ul"></i>
                    </div>
                </div>
                <div class="stats-card-value">{{ total_transactions }}</div>
                <div class="stats-card-label">总记录数</div>
                <div class="progress-enhanced">
                    <div class="progress-bar-enhanced" style="width: 100%"></div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card stats-card-enhanced text-center">
            <div class="card-body">
                <div class="stats-card-header">
                    <div class="stats-card-icon" style="background: var(--inventory-gradient);">
                        <i class="bi bi-arrow-down-circle"></i>
                    </div>
                </div>
                <div class="stats-card-value">{{ in_transactions }}</div>
                <div class="stats-card-label">入库记录</div>
                <div class="progress-enhanced">
                    <div class="progress-bar-enhanced success" style="width: 75%"></div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card stats-card-enhanced text-center">
            <div class="card-body">
                <div class="stats-card-header">
                    <div class="stats-card-icon" style="background: var(--transactions-gradient);">
                        <i class="bi bi-arrow-up-circle"></i>
                    </div>
                </div>
                <div class="stats-card-value">{{ out_transactions }}</div>
                <div class="stats-card-label">出库记录</div>
                <div class="progress-enhanced">
                    <div class="progress-bar-enhanced warning" style="width: 60%"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 交易趋势图表 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="chart-container">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <div class="chart-title">进出库趋势分析</div>
                <div class="btn-group" role="group" aria-label="时间范围选择">
                    <button type="button" class="btn btn-outline-primary btn-sm" data-days="3" onclick="updateTransactionChartRange(3)">
                        近3天
                    </button>
                    <button type="button" class="btn btn-primary btn-sm active" data-days="7" onclick="updateTransactionChartRange(7)">
                        近7天
                    </button>
                    <button type="button" class="btn btn-outline-primary btn-sm" data-days="30" onclick="updateTransactionChartRange(30)">
                        近30天
                    </button>
                </div>
            </div>
            <canvas id="transactionTrendChart"></canvas>
        </div>
    </div>
</div>

<!-- 操作按钮区域 - 参考商品管理页面布局 -->
<div class="mb-3">
    <div class="d-flex gap-2 align-items-center">
        {% if perms.transactions.add_transaction %}
        <button type="button"
                class="btn btn-primary"
                onclick="openTransactionCreateModal()">
            <i class="bi bi-plus-lg"></i> 添加记录
        </button>

        <button type="button"
                class="btn btn-success"
                onclick="openTransactionBatchUploadModal()"
                title="批量上传进出库记录Excel文件">
            <i class="bi bi-upload"></i> 批量上传
        </button>

        <button type="button"
                class="btn btn-outline-secondary"
                onclick="downloadTransactionTemplate()"
                title="下载进出库记录批量上传模板">
            <i class="bi bi-download"></i> 下载模板
        </button>
        {% endif %}
    </div>
</div>

<!-- 搜索和过滤 -->
<div class="card mb-4">
    <div class="card-body">
        <form method="get" class="row g-3" id="transactionFilterForm">
            <div class="col-md-4">
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="bi bi-search"></i>
                    </span>
                    <input type="text"
                           class="form-control"
                           name="search"
                           id="search"
                           placeholder="搜索商品名称或备注..."
                           value="{{ request.GET.search }}">
                </div>
            </div>
            <div class="col-md-2">
                <select class="form-select" name="transaction_type" id="transaction_type">
                    <option value="">全部类型</option>
                    <option value="IN" {% if request.GET.transaction_type == 'IN' %}selected{% endif %}>入库</option>
                    <option value="OUT" {% if request.GET.transaction_type == 'OUT' %}selected{% endif %}>出库</option>
                </select>
            </div>
            <div class="col-md-3">
                <select class="form-select" name="date_range" id="date_range">
                    <option value="">全部时间</option>
                    <option value="today" {% if request.GET.date_range == 'today' %}selected{% endif %}>今天</option>
                    <option value="week" {% if request.GET.date_range == 'week' %}selected{% endif %}>本周</option>
                    <option value="month" {% if request.GET.date_range == 'month' %}selected{% endif %}>本月</option>
                </select>
            </div>
            <div class="col-md-3">
                <button type="submit" class="btn btn-outline-primary w-100">
                    <i class="bi bi-search"></i> 搜索
                </button>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 获取筛选表单和筛选控件
    const filterForm = document.getElementById('transactionFilterForm');
    const searchInput = document.getElementById('search');
    const transactionTypeSelect = document.getElementById('transaction_type');
    const dateRangeSelect = document.getElementById('date_range');
    const tableContainer = document.getElementById('transactionTableContainer');
    const paginationContainer = document.getElementById('transactionPaginationContainer');

    let searchTimeout;
    let isLoading = false;

    // 阻止表单默认提交
    if (filterForm) {
        filterForm.addEventListener('submit', function(e) {
            e.preventDefault();
            performAjaxFilter();
        });
    }

    // 实时搜索功能
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                performAjaxFilter();
            }, 500); // 500ms延迟，避免频繁请求
        });
    }

    // 筛选条件变化时立即提交
    if (transactionTypeSelect) {
        transactionTypeSelect.addEventListener('change', function() {
            performAjaxFilter();
        });
    }

    if (dateRangeSelect) {
        dateRangeSelect.addEventListener('change', function() {
            performAjaxFilter();
        });
    }

    // 执行AJAX筛选
    function performAjaxFilter(page = 1) {
        if (isLoading) return;

        isLoading = true;
        showLoadingState();

        // 构建查询参数
        const formData = new FormData(filterForm);
        const params = new URLSearchParams();

        // 添加表单数据
        for (let [key, value] of formData.entries()) {
            if (value.trim()) {
                params.append(key, value);
            }
        }

        // 添加页码
        params.append('page', page);

        // 更新浏览器URL
        const newUrl = window.location.pathname + '?' + params.toString();
        window.history.pushState({}, '', newUrl);

        // 发送AJAX请求
        fetch('/transactions/api/filter/?' + params.toString(), {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
            },
            credentials: 'same-origin'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 更新表格内容
                tableContainer.innerHTML = data.table_html;

                // 更新分页
                paginationContainer.innerHTML = data.pagination_html;

                // 绑定分页链接事件
                bindPaginationEvents();

                // 显示成功消息
                console.log(`筛选完成，共找到 ${data.total_count} 条记录`);
            } else {
                console.error('筛选失败:', data.error);
                showErrorMessage('筛选失败: ' + data.error);
            }
        })
        .catch(error => {
            console.error('AJAX请求失败:', error);
            showErrorMessage('网络请求失败，请稍后重试');
        })
        .finally(() => {
            hideLoadingState();
            isLoading = false;
        });
    }

    // 绑定分页链接事件
    function bindPaginationEvents() {
        const pageLinks = paginationContainer.querySelectorAll('.ajax-page-link');
        pageLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const page = this.getAttribute('data-page');
                performAjaxFilter(page);
            });
        });
    }

    // 显示加载状态
    function showLoadingState() {
        tableContainer.style.position = 'relative';
        tableContainer.style.opacity = '0.6';

        const loadingOverlay = document.createElement('div');
        loadingOverlay.id = 'ajaxLoadingOverlay';
        loadingOverlay.className = 'position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center bg-white bg-opacity-75';
        loadingOverlay.style.zIndex = '10';
        loadingOverlay.innerHTML = `
            <div class="text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">筛选中...</span>
                </div>
                <div class="mt-2 text-muted">正在筛选数据...</div>
            </div>
        `;

        tableContainer.appendChild(loadingOverlay);
    }

    // 隐藏加载状态
    function hideLoadingState() {
        const loadingOverlay = document.getElementById('ajaxLoadingOverlay');
        if (loadingOverlay) {
            loadingOverlay.remove();
        }
        tableContainer.style.opacity = '1';
    }

    // 显示错误消息
    function showErrorMessage(message) {
        // 可以使用Toast或其他方式显示错误
        console.error(message);
        alert(message); // 临时使用alert，可以后续改为更好的UI
    }

    // 初始化时绑定分页事件
    bindPaginationEvents();
});
</script>

<!-- 记录列表 -->
<div class="card">
    <div class="card-body">
        <!-- AJAX表格容器 -->
        <div id="transactionTableContainer">
            {% include 'transactions/partials/transaction_table.html' %}
        </div>

        <!-- AJAX分页容器 -->
        <div id="transactionPaginationContainer" class="mt-3">
            {% include 'transactions/partials/pagination.html' %}
        </div>
    </div>
</div>

<script>
/**
 * 打开进出库记录创建模态框
 */
function openTransactionCreateModal() {
    // 使用模态框形式创建进出库记录
    if (window.WarehouseJS && window.WarehouseJS.openEditModal) {
        window.WarehouseJS.openEditModal('{% url "transactions:create" %}', '新记录', '添加进出库记录');
    } else {
        console.error('openEditModal函数不可用');
        // 备用方案：跳转到创建页面
        window.location.href = '{% url "transactions:create" %}';
    }
}

document.addEventListener('DOMContentLoaded', function() {
    console.log('📊 进出库记录页面已加载');

    // 初始化交易页面图表（使用warehouse.js中的函数）
    if (window.initializeTransactionCharts) {
        setTimeout(() => {
            window.initializeTransactionCharts();
        }, 100);
    }

    // 初始化统计卡片动画
    if (window.WarehouseJS && window.WarehouseJS.initializeStatsCards) {
        window.WarehouseJS.initializeStatsCards();
    }
});
</script>
