"""
审计日志记录服务模块
提供统一的审计日志记录服务
"""
import threading
from django.contrib.contenttypes.models import ContentType
from django.contrib.auth.models import AnonymousUser
from .models import AuditLog
from .utils import (
    get_client_ip, get_user_agent, get_model_fields_dict,
    calculate_field_changes, format_audit_message, get_request_context
)

# 线程本地存储，用于在信号处理中获取请求对象
_thread_local = threading.local()


class AuditService:
    """审计日志记录服务"""
    
    @staticmethod
    def set_current_request(request):
        """设置当前请求对象到线程本地存储"""
        _thread_local.request = request
    
    @staticmethod
    def get_current_request():
        """从线程本地存储获取当前请求对象"""
        return getattr(_thread_local, 'request', None)
    
    @staticmethod
    def get_current_user():
        """获取当前用户"""
        request = AuditService.get_current_request()
        if request and hasattr(request, 'user') and not isinstance(request.user, AnonymousUser):
            return request.user
        return None
    
    @staticmethod
    def log_create(instance, user=None, request=None, extra_data=None):
        """
        记录创建操作
        
        Args:
            instance: 创建的模型实例
            user: 操作用户
            request: 请求对象
            extra_data: 额外数据
        """
        if user is None:
            user = AuditService.get_current_user()
        
        if request is None:
            request = AuditService.get_current_request()
        
        # 获取实例字段数据作为变更记录
        changes = get_model_fields_dict(instance)
        
        return AuditService._create_audit_log(
            user=user,
            action='CREATE',
            instance=instance,
            changes=changes,
            request=request,
            extra_data=extra_data
        )
    
    @staticmethod
    def log_update(old_instance, new_instance, user=None, request=None, extra_data=None):
        """
        记录更新操作
        
        Args:
            old_instance: 更新前的实例
            new_instance: 更新后的实例
            user: 操作用户
            request: 请求对象
            extra_data: 额外数据
        """
        if user is None:
            user = AuditService.get_current_user()
        
        if request is None:
            request = AuditService.get_current_request()
        
        # 计算字段变更
        changes = calculate_field_changes(old_instance, new_instance)
        
        # 如果没有变更，不记录日志
        if not changes:
            return None
        
        return AuditService._create_audit_log(
            user=user,
            action='UPDATE',
            instance=new_instance,
            changes=changes,
            request=request,
            extra_data=extra_data
        )
    
    @staticmethod
    def log_delete(instance, user=None, request=None, extra_data=None):
        """
        记录删除操作
        
        Args:
            instance: 被删除的模型实例
            user: 操作用户
            request: 请求对象
            extra_data: 额外数据
        """
        if user is None:
            user = AuditService.get_current_user()
        
        if request is None:
            request = AuditService.get_current_request()
        
        # 获取删除前的实例数据
        changes = get_model_fields_dict(instance)
        
        return AuditService._create_audit_log(
            user=user,
            action='DELETE',
            instance=instance,
            changes=changes,
            request=request,
            extra_data=extra_data
        )
    
    @staticmethod
    def log_view(instance, user=None, request=None, extra_data=None):
        """
        记录查看操作（可选，用于敏感数据访问记录）
        
        Args:
            instance: 查看的模型实例
            user: 操作用户
            request: 请求对象
            extra_data: 额外数据
        """
        if user is None:
            user = AuditService.get_current_user()
        
        if request is None:
            request = AuditService.get_current_request()
        
        return AuditService._create_audit_log(
            user=user,
            action='VIEW',
            instance=instance,
            changes={},
            request=request,
            extra_data=extra_data
        )
    
    @staticmethod
    def log_custom_action(action, instance, user=None, request=None, changes=None, extra_data=None):
        """
        记录自定义操作

        Args:
            action: 操作类型
            instance: 相关的模型实例
            user: 操作用户
            request: 请求对象
            changes: 变更数据
            extra_data: 额外数据
        """
        if user is None:
            user = AuditService.get_current_user()

        if request is None:
            request = AuditService.get_current_request()

        return AuditService._create_audit_log(
            user=user,
            action=action,
            instance=instance,
            changes=changes or {},
            request=request,
            extra_data=extra_data
        )

    @staticmethod
    def log_batch_transaction(transactions, user=None, request=None, extra_data=None):
        """
        记录批量交易操作的汇总审计日志

        Args:
            transactions: 交易记录列表
            user: 操作用户
            request: 请求对象
            extra_data: 额外数据

        Returns:
            AuditLog: 创建的审计日志实例
        """
        if not transactions:
            return None

        if user is None:
            user = AuditService.get_current_user()

        if request is None:
            request = AuditService.get_current_request()

        # 统计信息
        in_count = sum(1 for t in transactions if t.transaction_type == 'IN')
        out_count = sum(1 for t in transactions if t.transaction_type == 'OUT')
        total_quantity = sum(t.quantity for t in transactions)

        # 商品详情
        products_affected = []
        for t in transactions:
            products_affected.append({
                'name': t.product.name,
                'sku': t.product.sku,
                'transaction_type': t.get_transaction_type_display(),
                'quantity': t.quantity,
                'stock_before': t.stock_before,
                'stock_after': t.stock_after
            })

        # 批量审计日志数据
        batch_details = {
            'operation_type': 'BATCH_TRANSACTION',
            'total_items': len(transactions),
            'in_count': in_count,
            'out_count': out_count,
            'total_quantity': total_quantity,
            'products_affected': products_affected,
            'performance_improvement': {
                'database_operations_saved': len(transactions) * 3 - 3,  # 3N -> 3
                'audit_logs_saved': len(transactions) * 3 - 1  # 3N -> 1
            },
            'summary': {
                'operation_summary': f'批量处理{len(transactions)}个商品的出入库操作',
                'in_summary': f'入库操作：{in_count}个商品' if in_count > 0 else None,
                'out_summary': f'出库操作：{out_count}个商品' if out_count > 0 else None,
                'total_quantity_summary': f'总数量：{total_quantity}'
            }
        }

        # 合并额外数据
        final_extra_data = {
            'batch_operation': True,
            'optimization_enabled': True
        }
        if extra_data:
            final_extra_data.update(extra_data)

        # 创建汇总审计日志
        return AuditService.log_custom_action(
            action='BATCH_TRANSACTION',
            instance=transactions[0],  # 使用第一个交易作为代表
            user=user,
            request=request,
            changes=batch_details,
            extra_data=final_extra_data
        )
    
    @staticmethod
    def _create_audit_log(user, action, instance, changes, request=None, extra_data=None):
        """
        创建审计日志记录

        Args:
            user: 操作用户
            action: 操作类型
            instance: 模型实例
            changes: 变更数据
            request: 请求对象
            extra_data: 额外数据

        Returns:
            AuditLog: 创建的审计日志实例
        """
        try:
            import logging
            logger = logging.getLogger(__name__)
            logger.info(f"创建审计日志: 用户={user}, 操作={action}, 对象={instance}")

            # 获取请求上下文
            request_context = get_request_context(request) if request else {}

            # 合并额外数据
            final_extra_data = request_context.copy()
            if extra_data:
                final_extra_data.update(extra_data)

            # 创建审计日志
            audit_log = AuditLog.objects.create(
                user=user,
                action=action,
                content_type=ContentType.objects.get_for_model(instance),
                object_id=instance.pk,
                object_repr=str(instance),
                changes=changes,
                ip_address=get_client_ip(request) if request else None,
                user_agent=get_user_agent(request) if request else '',
                extra_data=final_extra_data
            )

            logger.info(f"审计日志创建成功: ID={audit_log.id}, 用户={audit_log.user}")
            return audit_log

        except Exception as e:
            # 审计日志记录失败不应该影响业务逻辑
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"审计日志记录失败: {str(e)}")
            return None


class AuditMiddleware:
    """审计中间件，用于设置当前请求到线程本地存储"""

    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # 设置当前请求到线程本地存储
        AuditService.set_current_request(request)

        try:
            response = self.get_response(request)
        finally:
            # 清理线程本地存储
            if hasattr(_thread_local, 'request'):
                delattr(_thread_local, 'request')

        return response


# 便捷函数
def log_create(instance, user=None, request=None, extra_data=None):
    """便捷的创建日志记录函数"""
    return AuditService.log_create(instance, user, request, extra_data)


def log_update(old_instance, new_instance, user=None, request=None, extra_data=None):
    """便捷的更新日志记录函数"""
    return AuditService.log_update(old_instance, new_instance, user, request, extra_data)


def log_delete(instance, user=None, request=None, extra_data=None):
    """便捷的删除日志记录函数"""
    return AuditService.log_delete(instance, user, request, extra_data)


def log_view(instance, user=None, request=None, extra_data=None):
    """便捷的查看日志记录函数"""
    return AuditService.log_view(instance, user, request, extra_data)
