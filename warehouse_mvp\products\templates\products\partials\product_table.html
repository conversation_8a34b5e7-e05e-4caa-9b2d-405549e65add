<!-- 商品表格内容 -->
{% for product in products %}
        <tr class="product-row"
            data-product-id="{{ product.pk }}"
            data-product-name="{{ product.name }}"
            {% if perms.products.change_product %}ondblclick="editProduct({{ product.pk }}, '{{ product.name }}')"{% endif %}
            style="cursor: {% if perms.products.change_product %}pointer{% else %}default{% endif %};">
            {% if perms.products.delete_product %}
            <td class="checkbox-column">
                <div class="form-check">
                    <input class="form-check-input product-checkbox"
                           type="checkbox"
                           value="{{ product.pk }}"
                           data-name="{{ product.name }}"
                           data-delete-url="{% url 'products:delete' product.pk %}"
                           onchange="updateBatchDeleteButton()">
                </div>
            </td>
            {% endif %}
            <td>
                <div class="d-flex align-items-center">
                    <div class="me-3">
                        <div class="bg-light rounded p-2">
                            <i class="bi bi-box text-primary fs-4"></i>
                        </div>
                    </div>
                    <div>
                        <div class="fw-bold">{{ product.name }}</div>
                        <small class="text-muted">
                            <i class="bi bi-tag"></i> {{ product.sku }}
                        </small>
                    </div>
                </div>
            </td>
            <td>
                <div class="text-muted">
                    {{ product.description|default:"-"|truncatechars:60 }}
                </div>
            </td>
            <td>
                <code class="text-muted">{{ product.sku }}</code>
            </td>
            <td>
                <span class="fw-bold text-success">¥{{ product.price|floatformat:2 }}</span>
            </td>
            <td>
                <div>{{ product.created_at|date:"Y-m-d H:i" }}</div>
            </td>
            <td>
                <div>{{ product.created_by.username|default:"系统" }}</div>
            </td>
            <td>
                <div id="updated-by-{{ product.pk }}">{{ product.updated_by.username|default:"系统" }}</div>
                <small class="text-muted" id="updated-at-{{ product.pk }}">{{ product.updated_at|date:"m-d H:i" }}</small>
            </td>
        </tr>
    {% empty %}
        <tr>
            <td colspan="{% if perms.products.delete_product %}7{% else %}6{% endif %}" class="text-center py-4">
                <div class="text-muted">
                    <i class="bi bi-inbox fs-1"></i>
                    <p class="mt-2">暂无商品数据</p>
                </div>
            </td>
        </tr>
{% endfor %}
