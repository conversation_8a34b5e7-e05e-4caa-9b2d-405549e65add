<!-- 分页组件 -->
{% if page_obj.has_other_pages %}
<nav aria-label="记录分页">
    <ul class="pagination justify-content-center">
        {% if page_obj.has_previous %}
            <li class="page-item">
                <a class="page-link ajax-page-link" href="#" data-page="1">首页</a>
            </li>
            <li class="page-item">
                <a class="page-link ajax-page-link" href="#" data-page="{{ page_obj.previous_page_number }}">上一页</a>
            </li>
        {% endif %}

        <li class="page-item active">
            <span class="page-link">第 {{ page_obj.number }} 页，共 {{ page_obj.paginator.num_pages }} 页</span>
        </li>

        {% if page_obj.has_next %}
            <li class="page-item">
                <a class="page-link ajax-page-link" href="#" data-page="{{ page_obj.next_page_number }}">下一页</a>
            </li>
            <li class="page-item">
                <a class="page-link ajax-page-link" href="#" data-page="{{ page_obj.paginator.num_pages }}">末页</a>
            </li>
        {% endif %}
    </ul>
</nav>
{% endif %}
