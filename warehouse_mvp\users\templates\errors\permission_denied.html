{% extends "products/base.html" %}
{% load static %}

{% block title %}{{ error_title|default:"访问被拒绝" }} - 仓库管理系统{% endblock %}

{% block extra_css %}
<style>
.error-container {
    max-width: 600px;
    margin: 2rem auto;
    padding: 2rem;
    text-align: center;
}

.error-icon {
    font-size: 4rem;
    color: #dc3545;
    margin-bottom: 1rem;
}

.error-title {
    color: #dc3545;
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.error-message {
    color: #6c757d;
    font-size: 1.1rem;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.error-details {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-bottom: 2rem;
    text-align: left;
}

.error-details h5 {
    color: #495057;
    margin-bottom: 0.5rem;
}

.error-details p {
    margin-bottom: 0.25rem;
    color: #6c757d;
    font-size: 0.9rem;
}

.suggested-actions {
    text-align: left;
    margin-bottom: 2rem;
}

.suggested-actions h5 {
    color: #495057;
    margin-bottom: 1rem;
}

.suggested-actions ul {
    list-style: none;
    padding: 0;
}

.suggested-actions li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #e9ecef;
    color: #6c757d;
}

.suggested-actions li:last-child {
    border-bottom: none;
}

.suggested-actions li::before {
    content: "💡";
    margin-right: 0.5rem;
}

.action-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.btn-back {
    background-color: #6c757d;
    border-color: #6c757d;
    color: white;
}

.btn-back:hover {
    background-color: #5a6268;
    border-color: #545b62;
    color: white;
}

.btn-contact {
    background-color: #17a2b8;
    border-color: #17a2b8;
    color: white;
}

.btn-contact:hover {
    background-color: #138496;
    border-color: #117a8b;
    color: white;
}

@media (max-width: 576px) {
    .error-container {
        margin: 1rem;
        padding: 1rem;
    }
    
    .error-icon {
        font-size: 3rem;
    }
    
    .error-title {
        font-size: 1.5rem;
    }
    
    .action-buttons {
        flex-direction: column;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="error-container">
    <div class="error-icon">
        🚫
    </div>
    
    <h1 class="error-title">{{ error_title|default:"访问被拒绝" }}</h1>
    
    <p class="error-message">
        {{ error_message|default:"您没有权限访问此页面。" }}
    </p>
    
    {% if app_name or url_name or user_role %}
    <div class="error-details">
        <h5>详细信息</h5>
        {% if app_name %}
            <p><strong>应用模块:</strong> {{ app_name }}</p>
        {% endif %}
        {% if url_name %}
            <p><strong>页面权限:</strong> {{ url_name }}</p>
        {% endif %}
        {% if user_role %}
            <p><strong>您的角色:</strong> {{ user_role }}</p>
        {% endif %}
        <p><strong>时间:</strong> {{ request.timestamp|default:now|date:"Y-m-d H:i:s" }}</p>
    </div>
    {% endif %}
    
    {% if suggested_actions %}
    <div class="suggested-actions">
        <h5>建议操作</h5>
        <ul>
            {% for action in suggested_actions %}
                <li>{{ action }}</li>
            {% endfor %}
        </ul>
    </div>
    {% endif %}
    
    <div class="action-buttons">
        <a href="javascript:history.back()" class="btn btn-back">
            <i class="fas fa-arrow-left"></i> 返回上页
        </a>
        
        <a href="{% url 'products:list' %}" class="btn btn-primary">
            <i class="fas fa-home"></i> 返回首页
        </a>
        
        {% if user.is_authenticated %}
        <a href="{% url 'users:profile' %}" class="btn btn-contact">
            <i class="fas fa-user"></i> 个人中心
        </a>
        {% endif %}
    </div>
    
    {% if user.is_authenticated %}
    <div class="mt-4">
        <small class="text-muted">
            如果您认为这是一个错误，请联系系统管理员。<br>
            错误代码: PERM-403-{{ app_name|default:"UNKNOWN" }}-{{ url_name|default:"UNKNOWN" }}
        </small>
    </div>
    {% endif %}
</div>

<script>
// 自动记录错误信息（用于调试）
if (console && console.log) {
    console.log('Permission Denied Error:', {
        app_name: '{{ app_name|default:"unknown" }}',
        url_name: '{{ url_name|default:"unknown" }}',
        user_role: '{{ user_role|default:"unknown" }}',
        path: '{{ request.path }}',
        timestamp: new Date().toISOString()
    });
}

// 如果是从AJAX请求重定向过来的，显示额外提示
if (document.referrer && document.referrer.includes('XMLHttpRequest')) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-info mt-3';
    alertDiv.innerHTML = '<i class="fas fa-info-circle"></i> 您刚才的操作因权限不足而被拒绝。';
    document.querySelector('.error-container').appendChild(alertDiv);
}
</script>
{% endblock %}
