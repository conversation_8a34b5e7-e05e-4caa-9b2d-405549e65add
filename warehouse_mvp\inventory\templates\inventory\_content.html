<!-- 库存查看页面共享内容 -->
{% load static %}
{% load inventory_extras %}

<!-- 页面头部 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="bi bi-clipboard-data"></i> 库存查看</h2>
</div>

<!-- 库存统计图表 -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="chart-container">
            <div class="chart-title">库存状态分布</div>
            <canvas id="stockStatusChart"></canvas>
        </div>
    </div>
    <div class="col-md-6">
        <div class="chart-container">
            <div class="chart-title">库存价值分析</div>
            <canvas id="stockValueChart"></canvas>
        </div>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="bi bi-box display-6 text-primary"></i>
                <h5 class="card-title mt-2">{{ total_products }}</h5>
                <p class="card-text text-muted">商品总数</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="bi bi-exclamation-triangle display-6 text-warning"></i>
                <h5 class="card-title mt-2">{{ low_stock_count }}</h5>
                <p class="card-text text-muted">低库存商品</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="bi bi-currency-yen display-6 text-success"></i>
                <h5 class="card-title mt-2">¥{{ total_value|floatformat:2 }}</h5>
                <p class="card-text text-muted">库存总价值</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="bi bi-graph-up display-6 text-info"></i>
                <h5 class="card-title mt-2">实时</h5>
                <p class="card-text text-muted">数据更新</p>
            </div>
        </div>
    </div>
</div>

<!-- 搜索和筛选 -->
<div class="card card-search mb-3">
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-4">
                <label for="search" class="form-label">搜索商品</label>
                <input type="text" name="search" id="search" class="form-control" 
                       placeholder="商品名称或编码..." value="{{ search }}">
            </div>
            <div class="col-md-3">
                <label for="status" class="form-label">库存状态</label>
                <select name="status" id="status" class="form-select">
                    <option value="">全部状态</option>
                    <option value="low" {% if status == 'low' %}selected{% endif %}>低库存</option>
                    <option value="normal" {% if status == 'normal' %}selected{% endif %}>正常库存</option>
                    <option value="high" {% if status == 'high' %}selected{% endif %}>高库存</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="order_by" class="form-label">排序方式</label>
                <select name="order_by" id="order_by" class="form-select">
                    <option value="-updated_at" {% if order_by == '-updated_at' %}selected{% endif %}>最近更新</option>
                    <option value="product__name" {% if order_by == 'product__name' %}selected{% endif %}>商品名称 A-Z</option>
                    <option value="-product__name" {% if order_by == '-product__name' %}selected{% endif %}>商品名称 Z-A</option>
                    <option value="quantity" {% if order_by == 'quantity' %}selected{% endif %}>库存数量 ↑</option>
                    <option value="-quantity" {% if order_by == '-quantity' %}selected{% endif %}>库存数量 ↓</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary" title="查询">
                        <i class="bi bi-search"></i>
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 操作按钮区域 -->
<div class="mb-3">
    <div class="d-flex gap-2 align-items-center">
        {% if perms.inventory.change_inventory %}
        <button type="button"
                class="btn btn-outline-primary"
                id="batch-edit-btn"
                onclick="editSelectedInventory()"
                style="display: none;">
            <i class="bi bi-pencil"></i> 批量编辑
        </button>
        {% endif %}

        {% if perms.inventory.delete_inventory %}
        <button type="button"
                class="btn btn-outline-danger"
                id="batch-delete-btn"
                onclick="confirmBatchDeleteInventory()"
                style="display: none;">
            <i class="bi bi-trash"></i> 删除 (<span id="selected-count">0</span>)
        </button>
        {% endif %}

        <!-- 选择信息显示 -->
        <div id="selection-info" style="display: none;">
            <small class="text-muted">已选择 <span id="selected-count-info">0</span> 项</small>
        </div>
    </div>
</div>

<!-- 库存列表 -->
<div class="card card-data">
    <div class="card-body">
        {% if inventories %}
            <div class="unified-table-container">
                <table class="unified-table table-theme-success" id="inventory-table">
                    <colgroup>
                        {% if perms.inventory.delete_inventory %}
                        <!-- 有删除权限：7列 -->
                        <col style="width: 60px;"><!-- 复选框 -->
                        <col style="width: 25%;"><!-- 商品信息 -->
                        <col style="width: 15%;"><!-- 当前库存 -->
                        <col style="width: 12%;"><!-- 库存状态 -->
                        <col style="width: 15%;"><!-- 库存价值 -->
                        <col style="width: 15%;"><!-- 最后更新 -->
                        <col style="width: 13%;"><!-- 更新人 -->
                        {% else %}
                        <!-- 无删除权限：6列 -->
                        <col style="width: 25%;"><!-- 商品信息 -->
                        <col style="width: 15%;"><!-- 当前库存 -->
                        <col style="width: 12%;"><!-- 库存状态 -->
                        <col style="width: 15%;"><!-- 库存价值 -->
                        <col style="width: 15%;"><!-- 最后更新 -->
                        <col style="width: 18%;"><!-- 更新人 -->
                        {% endif %}
                    </colgroup>
                    <thead>
                        <tr>
                            {% if perms.inventory.delete_inventory %}
                            <th class="checkbox-column">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="select-all">
                                </div>
                            </th>
                            {% endif %}
                            <th>商品信息</th>
                            <th>当前库存</th>
                            <th>库存状态</th>
                            <th>库存价值</th>
                            <th>最后更新</th>
                            <th>更新人</th>
                        </tr>
                    </thead>
                    <tbody id="inventory-table-body">
                        {% include 'inventory/partials/inventory_table.html' %}
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            <div id="inventory-pagination-container">
                {% include 'inventory/partials/pagination.html' %}
            </div>
        {% else %}
            <div class="text-center py-5">
                <i class="bi bi-clipboard-data display-1 text-muted"></i>
                <h4 class="text-muted mt-3">暂无库存数据</h4>
                <p class="text-muted">请先添加商品，系统会自动创建库存记录</p>
            </div>
        {% endif %}
    </div>
</div>

<style>
/* 库存编辑按钮样式 - 动画已移除 */
.edit-btn {
    opacity: 0.7;
}

.edit-btn:hover {
    opacity: 1;
    /* 移除transform动画效果 */
}

/* 库存页面选择框样式 */
.checkbox-column {
    width: 60px;
    text-align: center;
}

.inventory-row:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

.inventory-row.selected {
    background-color: rgba(0, 123, 255, 0.1);
}

/* 无删除权限时隐藏选择框相关元素 */
.no-delete-permission-inventory .checkbox-column,
.no-delete-permission-inventory #batch-delete-btn,
.no-delete-permission-inventory #selection-info {
    display: none !important;
}

/* 确保表格布局稳定 */
#inventory-table {
    table-layout: fixed;
    width: 100%;
}

#inventory-table colgroup col {
    box-sizing: border-box;
}

/* 防止表格列错位 */
#inventory-table th,
#inventory-table td {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    box-sizing: border-box;
}

/* 商品信息列允许换行 */
#inventory-table td:nth-child(2),
#inventory-table th:nth-child(2) {
    white-space: normal;
    word-wrap: break-word;
}

/* 有删除权限时的列宽调整 */
body:not(.no-delete-permission-inventory) #inventory-table td:nth-child(2),
body:not(.no-delete-permission-inventory) #inventory-table th:nth-child(2) {
    white-space: normal;
    word-wrap: break-word;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('📦 库存查看页面已加载');

    // 初始化库存页面图表（使用warehouse.js中的函数）
    if (window.initializeInventoryCharts) {
        setTimeout(() => {
            window.initializeInventoryCharts();
        }, 100);
    }

    // 初始化统计卡片动画
    if (window.WarehouseJS && window.WarehouseJS.initializeStatsCards) {
        window.WarehouseJS.initializeStatsCards();
    }

    // 初始化库存编辑功能
    if (window.WarehouseJS && window.WarehouseJS.initializeInventoryEdit) {
        window.WarehouseJS.initializeInventoryEdit();
    }

    // 绑定分页事件
    if (window.bindPaginationEvents) {
        window.bindPaginationEvents();
    }

    // 重新绑定库存页面事件
    if (window.rebindInventoryEvents) {
        window.rebindInventoryEvents();
    }

    // 绑定筛选器和排序器事件
    if (window.bindInventoryFilters) {
        window.bindInventoryFilters();
    }

    // 初始化批量操作功能
    if (window.updateBatchDeleteButtonInventory) {
        window.updateBatchDeleteButtonInventory();
    }

    // 根据权限设置body类
    {% if not perms.inventory.delete_inventory %}
    document.body.classList.add('no-delete-permission-inventory');
    {% endif %}
});
</script>

<!-- 编辑库存模态框已移除，使用base.html中的通用模态框 -->
