from django.urls import path
from . import views

app_name = 'transactions'

urlpatterns = [
    path('', views.TransactionListView.as_view(), name='list'),
    path('create/', views.TransactionCreateView.as_view(), name='create'),
    path('get-stock/', views.get_product_stock, name='get_stock'),
    path('api/batch-upload/', views.BatchUploadView.as_view(), name='batch_upload'),
    path('api/download-template/', views.download_template, name='download_template'),
    path('api/chart-data/', views.get_transaction_chart_data, name='get_chart_data'),
    path('api/filter/', views.ajax_filter_transactions, name='ajax_filter'),
]
