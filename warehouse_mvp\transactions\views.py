from django.shortcuts import render, get_object_or_404
from django.urls import reverse_lazy
from django.views.generic import ListView, CreateView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib import messages
from django.db import transaction
from django.db.models import Q, Sum, Count
from django.db.models.functions import TruncDate, TruncMonth
from django.http import JsonResponse
from django.utils import timezone
from datetime import timedelta, datetime
from django.core.paginator import Paginator
from django.template.loader import render_to_string
from django.views.decorators.http import require_http_methods
from django.contrib.auth.decorators import login_required
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from django.conf import settings
from .models import Transaction
from .forms import TransactionForm
from .services import BatchTransactionService
from inventory.models import Inventory
from products.models import Product
from users.decorators import PermissionRequiredMixin

# Create your views here.

class TransactionListView(PermissionRequiredMixin, ListView):
    """进出库记录列表视图"""
    model = Transaction
    template_name = 'transactions/list.html'
    context_object_name = 'transactions'
    permission_app = 'transactions'
    permission_action = 'view'
    paginate_by = 20

    def get_queryset(self):
        queryset = Transaction.objects.select_related('product', 'operator').all()

        # 搜索功能
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(product__name__icontains=search) |
                Q(product__sku__icontains=search) |
                Q(reason__icontains=search) |
                Q(operator__username__icontains=search)
            )

        # 类型筛选 - 修复参数名匹配问题
        transaction_type = self.request.GET.get('transaction_type')
        if transaction_type in ['IN', 'OUT']:
            queryset = queryset.filter(transaction_type=transaction_type)

        # 时间范围筛选
        date_range = self.request.GET.get('date_range')
        if date_range:
            from datetime import date, timedelta
            today = date.today()

            if date_range == 'today':
                queryset = queryset.filter(created_at__date=today)
            elif date_range == 'week':
                week_start = today - timedelta(days=today.weekday())
                queryset = queryset.filter(created_at__date__gte=week_start)
            elif date_range == 'month':
                month_start = today.replace(day=1)
                queryset = queryset.filter(created_at__date__gte=month_start)

        return queryset.order_by('-created_at')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search'] = self.request.GET.get('search', '')
        context['transaction_type'] = self.request.GET.get('type', '')

        # 统计信息
        all_transactions = Transaction.objects.all()
        context['total_transactions'] = all_transactions.count()
        context['in_transactions'] = all_transactions.filter(transaction_type='IN').count()
        context['out_transactions'] = all_transactions.filter(transaction_type='OUT').count()

        return context


class TransactionCreateView(PermissionRequiredMixin, CreateView):
    """进出库记录创建视图"""
    model = Transaction
    form_class = TransactionForm
    template_name = 'transactions/form.html'
    success_url = reverse_lazy('transactions:list')
    permission_app = 'transactions'
    permission_action = 'add'

    def get(self, request, *args, **kwargs):
        """处理GET请求，支持AJAX"""
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            # AJAX请求，返回表单HTML
            form = self.get_form()
            form_html = render_to_string('transactions/form_modal.html', {
                'form': form,
                'action_url': reverse_lazy('transactions:create')
            }, request=request)
            return JsonResponse({
                'success': True,
                'form_html': form_html
            })

        # 普通请求处理
        return super().get(request, *args, **kwargs)

    def form_valid(self, form):
        """表单验证成功后的处理"""
        try:
            with transaction.atomic():  # 使用数据库事务
                # 设置操作员
                form.instance.operator = self.request.user

                # 保存交易记录
                response = super().form_valid(form)

                # 更新库存
                self.update_inventory(form.instance)

                # 成功消息
                transaction_type_display = form.instance.get_transaction_type_display()
                success_message = f'{transaction_type_display}操作成功！商品：{form.instance.product.name}，数量：{form.instance.quantity}'

                # 如果是AJAX请求，返回JSON响应
                if self.request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return JsonResponse({
                        'success': True,
                        'message': success_message,
                        'redirect_url': str(self.success_url)
                    })

                messages.success(self.request, success_message)
                return response

        except Exception as e:
            error_message = f'操作失败：{str(e)}'

            # 如果是AJAX请求，返回JSON错误响应
            if self.request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return JsonResponse({
                    'success': False,
                    'error': error_message
                })

            messages.error(self.request, error_message)
            return self.form_invalid(form)

    def form_invalid(self, form):
        """表单验证失败后的处理"""
        # 如果是AJAX请求，返回表单错误
        if self.request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            form_html = render_to_string('transactions/form_modal.html', {
                'form': form,
                'action_url': reverse_lazy('transactions:create')
            }, request=self.request)
            return JsonResponse({
                'success': False,
                'form_html': form_html,
                'errors': form.errors
            })

        # 普通请求处理
        return super().form_invalid(form)

    def update_inventory(self, transaction_obj):
        """更新库存数量并记录操作前后库存"""
        try:
            inventory = Inventory.objects.select_for_update().get(product=transaction_obj.product)

            # 记录操作前库存
            stock_before = inventory.quantity
            transaction_obj.stock_before = stock_before

            if transaction_obj.transaction_type == 'IN':
                # 入库：增加库存
                inventory.quantity += transaction_obj.quantity
            elif transaction_obj.transaction_type == 'OUT':
                # 出库：减少库存
                inventory.quantity -= transaction_obj.quantity

                # 再次检查库存不能为负数（双重保险）
                if inventory.quantity < 0:
                    raise ValueError('库存不足，无法完成出库操作')

            # 记录操作后库存
            stock_after = inventory.quantity
            transaction_obj.stock_after = stock_after

            # 保存库存和交易记录
            inventory.save()
            transaction_obj.save()

        except Inventory.DoesNotExist:
            raise ValueError(f'商品 {transaction_obj.product.name} 的库存记录不存在')


def get_product_stock(request):
    """AJAX获取商品库存信息"""
    product_id = request.GET.get('product_id')
    if product_id:
        try:
            product = Product.objects.get(id=product_id)
            inventory = Inventory.objects.get(product=product)
            return JsonResponse({
                'success': True,
                'current_stock': inventory.quantity,
                'min_stock': inventory.min_stock,
                'max_stock': inventory.max_stock,
                'status': inventory.stock_status,
                'is_low_stock': inventory.is_low_stock
            })
        except (Product.DoesNotExist, Inventory.DoesNotExist):
            return JsonResponse({
                'success': False,
                'error': '商品或库存记录不存在'
            })
    return JsonResponse({
        'success': False,
        'error': '无效的商品ID'
    })


@login_required
@require_http_methods(["GET"])
def download_template(request):
    """下载进出库记录批量上传模板"""
    # 检查权限
    if not request.user.has_perm('transactions.add_transaction'):
        return JsonResponse({'success': False, 'error': '权限不足'}, status=403)

    try:
        import openpyxl
        from django.http import HttpResponse
        from django.utils import timezone

        # 创建Excel工作簿
        workbook = openpyxl.Workbook()

        # 第一个工作表：进出库记录数据模板
        data_sheet = workbook.active
        data_sheet.title = "进出库记录数据"

        # 设置列标题 - 改为基于商品名称
        headers = [
            '商品名称',
            '操作类型',
            '数量',
            '备注'
        ]

        # 写入标题行
        for col, header in enumerate(headers, 1):
            cell = data_sheet.cell(row=1, column=col, value=header)
            cell.font = openpyxl.styles.Font(bold=True, color="FFFFFF")
            cell.fill = openpyxl.styles.PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            cell.alignment = openpyxl.styles.Alignment(horizontal="center", vertical="center")

        # 添加示例数据 - 基于商品名称
        sample_data = [
            # 入库操作 - 先建立库存
            ['苹果手机', '入库', '50', '新到货商品'],
            ['蓝牙耳机', '入库', '30', '新到货商品'],
            ['充电宝', '入库', '40', '客户退货商品'],
            # 出库操作 - 在有库存的基础上执行
            ['苹果手机', '出库', '5', '客户订单'],
            ['蓝牙耳机', '出库', '10', '客户订单']
        ]

        for row, data in enumerate(sample_data, 2):
            for col, value in enumerate(data, 1):
                data_sheet.cell(row=row, column=col, value=value)

        # 设置数据工作表的列宽 - 调整为4列
        column_widths = [25, 12, 10, 30]
        for col, width in enumerate(column_widths, 1):
            data_sheet.column_dimensions[openpyxl.utils.get_column_letter(col)].width = width

        # 第二个工作表：使用说明
        help_sheet = workbook.create_sheet(title="使用说明")

        # 添加使用说明内容
        help_content = [
            ["进出库记录批量上传使用说明", ""],
            ["", ""],
            ["1. 基本步骤", ""],
            ["   ① 切换到「进出库记录数据」工作表", ""],
            ["   ② 删除示例数据行（第2-4行）", ""],
            ["   ③ 填写您的进出库记录信息", ""],
            ["   ④ 保存文件并上传", ""],
            ["", ""],
            ["2. 字段说明", ""],
            ["   商品名称", "必填，商品的名称，必须在系统中存在且唯一"],
            ["   操作类型", "必填，填写'入库'或'出库'"],
            ["   数量", "必填，正整数，出库时会检查库存是否足够"],
            ["   备注", "可选，补充说明信息"],
            ["", ""],
            ["3. 注意事项", ""],
            ["   • 请确保商品名称在系统中存在且唯一", ""],
            ["   • 操作类型只能填写'入库'或'出库'", ""],
            ["   • 建议将入库操作排在出库操作之前，避免库存不足", ""],
            ["   • 出库操作会检查库存是否足够", ""],
            ["   • 数量必须为正整数", ""],
            ["   • 建议一次上传不超过500条记录", ""],
            ["", ""],
            ["4. 示例数据", ""],
            ["   进出库记录数据工作表中包含3行示例数据", ""],
            ["   您可以参考示例格式填写自己的记录信息", ""],
            ["   上传前请删除示例数据或直接修改为您的数据", ""]
        ]

        for row, (col1, col2) in enumerate(help_content, 1):
            help_sheet.cell(row=row, column=1, value=col1)
            help_sheet.cell(row=row, column=2, value=col2)

        # 设置说明工作表样式
        # 标题样式
        title_cell = help_sheet.cell(row=1, column=1)
        title_cell.font = openpyxl.styles.Font(bold=True, size=14, color="366092")

        # 章节标题样式
        for row in [3, 9, 17, 23]:
            cell = help_sheet.cell(row=row, column=1)
            cell.font = openpyxl.styles.Font(bold=True, color="366092")

        # 设置说明工作表的列宽
        help_sheet.column_dimensions['A'].width = 40
        help_sheet.column_dimensions['B'].width = 60

        # 创建HTTP响应
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = 'attachment; filename="进出库记录批量上传模板.xlsx"'

        # 保存工作簿到响应
        workbook.save(response)

        return response

    except Exception as e:
        return JsonResponse({'success': False, 'error': f'生成模板失败：{str(e)}'}, status=500)


@method_decorator(login_required, name='dispatch')
@method_decorator(csrf_exempt, name='dispatch')
class BatchUploadView(PermissionRequiredMixin, CreateView):
    """进出库记录批量上传视图"""
    model = Transaction
    permission_app = 'transactions'
    permission_action = 'add'

    def post(self, request, *args, **kwargs):
        """处理批量上传请求"""
        try:
            # 检查是否有上传的文件
            if 'file' not in request.FILES:
                return JsonResponse({
                    'success': False,
                    'error': '请选择要上传的Excel文件'
                })

            uploaded_file = request.FILES['file']

            # 验证文件格式
            if not self.validate_file_format(uploaded_file):
                return JsonResponse({
                    'success': False,
                    'error': '文件格式不正确，请上传.xlsx或.xls格式的Excel文件'
                })

            # 验证文件大小（10MB限制）
            if uploaded_file.size > 10 * 1024 * 1024:
                return JsonResponse({
                    'success': False,
                    'error': '文件大小不能超过10MB'
                })

            # 解析Excel文件
            transactions_data = self.parse_excel_file(uploaded_file)

            if not transactions_data:
                return JsonResponse({
                    'success': False,
                    'error': 'Excel文件中没有找到有效的进出库记录数据'
                })

            # 验证数据
            validation_result = self.validate_transactions_data(transactions_data)
            if not validation_result['valid']:
                return JsonResponse({
                    'success': False,
                    'error': validation_result['error']
                })

            # 批量创建进出库记录
            result = self.create_transactions_batch(transactions_data, request.user)

            return JsonResponse({
                'success': True,
                'message': '批量上传处理完成',
                'created_count': result['created_count'],
                'skipped_count': result['skipped_count'],
                'details': result['details']
            })

        except Exception as e:
            return JsonResponse({
                'success': False,
                'error': f'批量上传失败：{str(e)}'
            }, status=500)

    def validate_file_format(self, uploaded_file):
        """验证文件格式"""
        allowed_extensions = ['.xlsx', '.xls']
        file_extension = uploaded_file.name.lower().split('.')[-1]
        return f'.{file_extension}' in allowed_extensions

    def parse_excel_file(self, uploaded_file):
        """解析Excel文件"""
        try:
            import openpyxl

            # 读取Excel文件
            workbook = openpyxl.load_workbook(uploaded_file, data_only=True)

            # 尝试找到数据工作表
            if '进出库记录数据' in workbook.sheetnames:
                worksheet = workbook['进出库记录数据']
            else:
                worksheet = workbook.active

            transactions_data = []

            # 跳过标题行，从第2行开始读取
            for row in worksheet.iter_rows(min_row=2, values_only=True):
                # 跳过空行
                if not any(row):
                    continue

                # 提取数据 - 改为基于商品名称
                product_name = str(row[0]).strip() if row[0] else ''
                transaction_type = str(row[1]).strip() if row[1] else ''
                quantity = row[2]
                notes = str(row[3]).strip() if row[3] else ''

                # 基本验证 - 验证商品名称、操作类型和数量
                if not product_name or not transaction_type or not quantity:
                    continue

                transactions_data.append({
                    'product_name': product_name,
                    'transaction_type': transaction_type,
                    'quantity': quantity,
                    'reason': '',  # 批量上传时reason字段设为空值
                    'notes': notes
                })

            return transactions_data

        except Exception as e:
            raise Exception(f'解析Excel文件失败：{str(e)}')

    def validate_transactions_data(self, transactions_data):
        """验证进出库记录数据"""
        try:
            if len(transactions_data) > 500:
                return {
                    'valid': False,
                    'error': f'一次最多只能上传500条记录，当前有{len(transactions_data)}条'
                }

            # 创建临时库存计算器，用于模拟批量操作对库存的影响
            temp_inventory = {}

            for i, data in enumerate(transactions_data, 1):
                # 验证商品是否存在
                try:
                    product = Product.objects.get(name=data['product_name'])
                    data['product'] = product
                except Product.DoesNotExist:
                    return {
                        'valid': False,
                        'error': f'第{i}行：商品名称"{data["product_name"]}"不存在'
                    }
                except Product.MultipleObjectsReturned:
                    return {
                        'valid': False,
                        'error': f'第{i}行：存在多个名称为"{data["product_name"]}"的商品，请确保商品名称唯一'
                    }

                # 验证操作类型
                if data['transaction_type'] not in ['入库', '出库']:
                    return {
                        'valid': False,
                        'error': f'第{i}行：操作类型必须是"入库"或"出库"'
                    }

                # 验证数量
                try:
                    quantity = int(data['quantity'])
                    if quantity <= 0:
                        return {
                            'valid': False,
                            'error': f'第{i}行：数量必须是正整数'
                        }
                    data['quantity'] = quantity
                except (ValueError, TypeError):
                    return {
                        'valid': False,
                        'error': f'第{i}行：数量格式不正确'
                    }

                # 初始化临时库存（如果是第一次遇到这个商品）
                if product.id not in temp_inventory:
                    try:
                        inventory = Inventory.objects.get(product=product)
                        temp_inventory[product.id] = inventory.quantity
                    except Inventory.DoesNotExist:
                        temp_inventory[product.id] = 0

                # 验证出库库存是否足够（考虑当前批次中之前的操作）
                if data['transaction_type'] == '出库':
                    current_stock = temp_inventory[product.id]
                    if current_stock < quantity:
                        return {
                            'valid': False,
                            'error': f'第{i}行：商品"{product.name}"库存不足，当前库存：{current_stock}，需要出库：{quantity}'
                        }
                    # 模拟出库操作，减少临时库存
                    temp_inventory[product.id] -= quantity
                else:
                    # 模拟入库操作，增加临时库存
                    temp_inventory[product.id] += quantity

            return {'valid': True}

        except Exception as e:
            return {
                'valid': False,
                'error': f'数据验证失败：{str(e)}'
            }

    def create_transactions_batch(self, transactions_data, user):
        """
        批量创建进出库记录
        根据配置选择使用优化版本或原版本
        """
        import time
        start_time = time.time()

        try:
            # 检查是否启用批量优化
            if getattr(settings, 'BATCH_TRANSACTION_OPTIMIZATION', True):
                result = self.create_transactions_batch_optimized(transactions_data, user)
                optimization_used = True
            else:
                result = self.create_transactions_batch_legacy(transactions_data, user)
                optimization_used = False

            # 性能监控
            if getattr(settings, 'BATCH_TRANSACTION_MONITORING', True):
                end_time = time.time()
                processing_time = end_time - start_time

                # 记录性能指标
                import logging
                logger = logging.getLogger(__name__)
                logger.info(f"批量交易处理完成: "
                          f"优化版本={optimization_used}, "
                          f"处理数量={result['created_count']}, "
                          f"处理时间={processing_time:.2f}秒, "
                          f"跳过数量={result['skipped_count']}")

                # 添加性能信息到结果中
                result['performance'] = {
                    'optimization_used': optimization_used,
                    'processing_time': round(processing_time, 2),
                    'items_per_second': round(result['created_count'] / processing_time, 2) if processing_time > 0 else 0
                }

            return result

        except Exception as e:
            # 如果优化版本失败，尝试回退到原版本
            if getattr(settings, 'BATCH_TRANSACTION_OPTIMIZATION', True):
                import logging
                logger = logging.getLogger(__name__)
                logger.warning(f"批量优化版本失败，回退到原版本: {str(e)}")

                try:
                    return self.create_transactions_batch_legacy(transactions_data, user)
                except Exception as fallback_error:
                    logger.error(f"原版本也失败: {str(fallback_error)}")
                    raise fallback_error
            else:
                raise e

    def create_transactions_batch_optimized(self, transactions_data, user):
        """使用BatchTransactionService的优化版本"""
        return BatchTransactionService.create_batch_optimized(transactions_data, user)

    def create_transactions_batch_legacy(self, transactions_data, user):
        """原有的批量创建进出库记录方法（作为备选）"""
        created_count = 0
        skipped_count = 0
        details = []

        for data in transactions_data:
            try:
                with transaction.atomic():
                    # 创建进出库记录
                    transaction_obj = Transaction.objects.create(
                        product=data['product'],
                        transaction_type='IN' if data['transaction_type'] == '入库' else 'OUT',
                        quantity=data['quantity'],
                        reason=data['reason'],
                        notes=data['notes'],
                        operator=user
                    )

                    # 更新库存
                    self.update_inventory_for_transaction(transaction_obj)

                    created_count += 1
                    details.append(f'✓ {data["product"].name}：{data["transaction_type"]} {data["quantity"]}')

            except Exception as e:
                skipped_count += 1
                details.append(f'✗ {data.get("sku", "未知商品")}：{str(e)}')

        return {
            'created_count': created_count,
            'skipped_count': skipped_count,
            'details': details
        }

    def update_inventory_for_transaction(self, transaction_obj):
        """为进出库记录更新库存并记录操作前后库存"""
        try:
            inventory = Inventory.objects.get(product=transaction_obj.product)

            # 记录操作前库存
            stock_before = inventory.quantity
            transaction_obj.stock_before = stock_before

            if transaction_obj.transaction_type == 'IN':
                inventory.quantity += transaction_obj.quantity
            else:  # OUT
                inventory.quantity -= transaction_obj.quantity

            # 记录操作后库存
            stock_after = inventory.quantity
            transaction_obj.stock_after = stock_after

            # 保存库存和交易记录
            inventory.save()
            transaction_obj.save()

        except Inventory.DoesNotExist:
            # 如果库存记录不存在，创建新的库存记录
            if transaction_obj.transaction_type == 'IN':
                # 操作前库存为0（因为没有库存记录）
                transaction_obj.stock_before = 0
                transaction_obj.stock_after = transaction_obj.quantity
                transaction_obj.save()

                Inventory.objects.create(
                    product=transaction_obj.product,
                    quantity=transaction_obj.quantity,
                    min_stock=10,  # 默认最低库存
                    max_stock=1000  # 默认最高库存
                )
            else:
                raise Exception('商品库存记录不存在，无法执行出库操作')


@login_required
@require_http_methods(["GET"])
def get_transaction_chart_data(request):
    """获取进出库趋势图表数据API"""
    try:
        from django.utils import timezone
        from datetime import datetime, timedelta
        from django.db.models import Sum, Count
        from django.db.models.functions import TruncDate, TruncMonth

        # 获取时间范围参数，默认显示最近7天
        period = request.GET.get('period', 'daily')  # daily, weekly, monthly
        days = int(request.GET.get('days', 7))

        # 限制days参数的有效值
        if days not in [3, 7, 30]:
            days = 7  # 默认值

        # 计算开始时间 - 简化时区处理（现在USE_TZ=False，直接使用本地时间）
        from datetime import date
        end_date = date.today()
        start_date = end_date - timedelta(days=days-1)  # 包含今天，所以减去days-1

        # 转换为datetime对象以处理时区问题
        start_datetime = timezone.make_aware(datetime.combine(start_date, datetime.min.time()))
        end_datetime = timezone.make_aware(datetime.combine(end_date, datetime.max.time()))

        # 获取时间范围内的进出库记录
        transactions = Transaction.objects.filter(
            created_at__date__gte=start_date,
            created_at__date__lte=end_date
        )

        total_transactions = transactions.count()

        # 如果没有找到记录，尝试扩大查询范围到最近30天
        if total_transactions == 0:
            extended_start_date = end_date - timedelta(days=30)

            extended_transactions = Transaction.objects.filter(
                created_at__date__gte=extended_start_date,
                created_at__date__lte=end_date
            )
            extended_count = extended_transactions.count()

            # 如果扩大范围后有数据，就使用扩大的范围
            if extended_count > 0:
                transactions = extended_transactions
                total_transactions = extended_count
                start_date = extended_start_date

        if period == 'monthly':
            # 按月统计 - 使用Django ORM标准方法
            in_data = transactions.filter(transaction_type='IN').annotate(
                month=TruncMonth('created_at')
            ).values('month').annotate(
                total_quantity=Sum('quantity'),
                count=Count('id')
            ).order_by('month')

            out_data = transactions.filter(transaction_type='OUT').annotate(
                month=TruncMonth('created_at')
            ).values('month').annotate(
                total_quantity=Sum('quantity'),
                count=Count('id')
            ).order_by('month')

            # 生成最近12个月的标签
            labels = []
            current_date = end_date.replace(day=1)
            for i in range(12):
                labels.insert(0, current_date.strftime('%Y-%m'))
                if current_date.month == 1:
                    current_date = current_date.replace(year=current_date.year-1, month=12)
                else:
                    current_date = current_date.replace(month=current_date.month-1)

        else:
            # 按日统计（默认）- 使用Django ORM标准方法
            in_data = transactions.filter(transaction_type='IN').annotate(
                date=TruncDate('created_at')
            ).values('date').annotate(
                total_quantity=Sum('quantity'),
                count=Count('id')
            ).order_by('date')

            out_data = transactions.filter(transaction_type='OUT').annotate(
                date=TruncDate('created_at')
            ).values('date').annotate(
                total_quantity=Sum('quantity'),
                count=Count('id')
            ).order_by('date')

            # 生成日期标签
            labels = []
            current_date = start_date
            while current_date <= end_date:
                labels.append(current_date.strftime('%m-%d'))
                current_date += timedelta(days=1)

        # 构建数据字典
        in_dict = {}
        out_dict = {}

        for item in in_data:
            if period == 'monthly':
                key = item['month'].strftime('%Y-%m')
            else:
                key = item['date'].strftime('%Y-%m-%d')
            in_dict[key] = item['total_quantity'] or 0

        for item in out_data:
            if period == 'monthly':
                key = item['month'].strftime('%Y-%m')
            else:
                key = item['date'].strftime('%Y-%m-%d')
            out_dict[key] = item['total_quantity'] or 0

        # 构造图表数据
        in_quantities = []
        out_quantities = []

        for label in labels:
            if period == 'monthly':
                key = label  # 格式：2024-01
            else:
                # 将 MM-DD 格式转换为完整日期进行查找
                year = end_date.year
                month, day = label.split('-')
                key = f"{year}-{month.zfill(2)}-{day.zfill(2)}"

            in_qty = in_dict.get(key, 0)
            out_qty = out_dict.get(key, 0)

            in_quantities.append(in_qty)
            out_quantities.append(out_qty)

        chart_data = {
            'labels': labels,
            'datasets': [
                {
                    'label': '入库数量',
                    'data': in_quantities,
                    'borderColor': 'rgba(40, 167, 69, 1)',
                    'backgroundColor': 'rgba(40, 167, 69, 0.1)',
                    'fill': True,
                    'tension': 0.4
                },
                {
                    'label': '出库数量',
                    'data': out_quantities,
                    'borderColor': 'rgba(255, 99, 132, 1)',
                    'backgroundColor': 'rgba(255, 99, 132, 0.1)',
                    'fill': True,
                    'tension': 0.4
                }
            ]
        }

        return JsonResponse({
            'success': True,
            'data': chart_data,
            'period': period,
            'days': days,
            'timestamp': timezone.now().isoformat()
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': f'获取进出库趋势数据失败：{str(e)}'
        }, status=500)


@login_required
@require_http_methods(["GET"])
def ajax_filter_transactions(request):
    """AJAX筛选进出库记录"""
    try:
        # 获取筛选参数
        search = request.GET.get('search', '').strip()
        transaction_type = request.GET.get('transaction_type', '')
        date_range = request.GET.get('date_range', '')
        page = int(request.GET.get('page', 1))

        # 构建查询
        queryset = Transaction.objects.select_related('product', 'operator').all()

        # 搜索功能
        if search:
            queryset = queryset.filter(
                Q(product__name__icontains=search) |
                Q(product__sku__icontains=search) |
                Q(reason__icontains=search) |
                Q(operator__username__icontains=search)
            )

        # 类型筛选
        if transaction_type in ['IN', 'OUT']:
            queryset = queryset.filter(transaction_type=transaction_type)

        # 时间范围筛选
        if date_range:
            from datetime import date, timedelta
            today = date.today()

            if date_range == 'today':
                queryset = queryset.filter(created_at__date=today)
            elif date_range == 'week':
                week_start = today - timedelta(days=today.weekday())
                queryset = queryset.filter(created_at__date__gte=week_start)
            elif date_range == 'month':
                month_start = today.replace(day=1)
                queryset = queryset.filter(created_at__date__gte=month_start)

        # 排序
        queryset = queryset.order_by('-created_at')

        # 分页
        paginator = Paginator(queryset, 20)  # 每页20条
        page_obj = paginator.get_page(page)

        # 渲染表格内容
        table_html = render_to_string('transactions/partials/transaction_table.html', {
            'transactions': page_obj,
            'page_obj': page_obj,
            'request': request,
        })

        # 渲染分页
        pagination_html = render_to_string('transactions/partials/pagination.html', {
            'page_obj': page_obj,
            'request': request,
        })

        return JsonResponse({
            'success': True,
            'table_html': table_html,
            'pagination_html': pagination_html,
            'total_count': paginator.count,
            'current_page': page_obj.number,
            'total_pages': paginator.num_pages,
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': f'筛选进出库记录失败：{str(e)}'
        }, status=500)
