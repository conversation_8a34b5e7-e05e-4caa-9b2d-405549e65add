<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户注册 - 仓库管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container">
        <div class="row justify-content-center min-vh-100 align-items-center">
            <div class="col-md-6 col-lg-5">
                <div class="card shadow">
                    <div class="card-body p-5">
                        <div class="text-center mb-4">
                            <i class="bi bi-person-plus display-4 text-primary"></i>
                            <h3 class="mt-3">注册新账户</h3>
                            <p class="text-muted">创建您的仓库管理账户</p>
                        </div>

                        {% if messages %}
                            {% for message in messages %}
                                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            {% endfor %}
                        {% endif %}

                        <form method="post">
                            {% csrf_token %}
                            <div class="mb-3">
                                <label for="{{ form.username.id_for_label }}" class="form-label">用户名</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="bi bi-person"></i>
                                    </span>
                                    {{ form.username }}
                                </div>
                                {% if form.username.help_text %}
                                    <small class="form-text text-muted">{{ form.username.help_text }}</small>
                                {% endif %}
                            </div>
                            
                            <div class="mb-3">
                                <label for="{{ form.password1.id_for_label }}" class="form-label">密码</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="bi bi-lock"></i>
                                    </span>
                                    {{ form.password1 }}
                                </div>
                                {% if form.password1.help_text %}
                                    <small class="form-text text-muted">{{ form.password1.help_text }}</small>
                                {% endif %}
                            </div>
                            
                            <div class="mb-3">
                                <label for="{{ form.password2.id_for_label }}" class="form-label">确认密码</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="bi bi-lock-fill"></i>
                                    </span>
                                    {{ form.password2 }}
                                </div>
                                {% if form.password2.help_text %}
                                    <small class="form-text text-muted">{{ form.password2.help_text }}</small>
                                {% endif %}
                            </div>

                            <div class="alert alert-info">
                                <i class="bi bi-info-circle"></i>
                                <strong>注意：</strong>新注册用户默认为普通用户权限，如需管理员权限请联系系统管理员。
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-person-plus"></i> 注册账户
                                </button>
                            </div>
                        </form>

                        <hr class="my-4">
                        
                        <div class="text-center">
                            <p class="text-muted">已有账户？</p>
                            <a href="{% url 'users:login' %}" class="btn btn-outline-primary">
                                <i class="bi bi-box-arrow-in-right"></i> 立即登录
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 为表单字段添加Bootstrap样式
        document.addEventListener('DOMContentLoaded', function() {
            const inputs = document.querySelectorAll('input[type="text"], input[type="password"]');
            inputs.forEach(input => {
                input.classList.add('form-control');
                input.setAttribute('placeholder', input.labels[0].textContent);
            });
        });
    </script>
</body>
</html>
