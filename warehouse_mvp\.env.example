# Django 配置
SECRET_KEY=your-secret-key-here
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# 数据库配置 - MySQL
DATABASE_ENGINE=django.db.backends.mysql
MYSQL_NAME=your_database_name
MYSQL_USER=your_username
MYSQL_PASSWORD=your_password
MYSQL_HOST=your_host
MYSQL_PORT=3306

# 缓存配置
CACHE_BACKEND=django.core.cache.backends.locmem.LocMemCache
CACHE_LOCATION=unique-snowflake
CACHE_TIMEOUT=300
CACHE_MAX_ENTRIES=1000

# 页面权限中间件配置
PAGE_PERMISSION_ENABLE_CACHE=True
PAGE_PERMISSION_CACHE_TIMEOUT=300

# 国际化配置
LANGUAGE_CODE=zh-hans
TIME_ZONE=Asia/Shanghai

# 登录相关设置
LOGIN_URL=/users/login/
LOGIN_REDIRECT_URL=/products/
LOGOUT_REDIRECT_URL=/users/login/

# 静态文件配置
STATIC_URL=static/
STATIC_ROOT=staticfiles

# 批量交易优化配置
BATCH_TRANSACTION_OPTIMIZATION=True
BATCH_TRANSACTION_SIZE=1000
BATCH_TRANSACTION_MONITORING=True
