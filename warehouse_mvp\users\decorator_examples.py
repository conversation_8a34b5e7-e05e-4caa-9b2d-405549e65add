"""
权限装饰器使用示例
演示各种装饰器和Mixin的使用方法
"""
from django.http import HttpResponse, JsonResponse
from django.views import View
from django.shortcuts import render

from .decorators import (
    permission_required, page_permission_required, dynamic_permission_required,
    multi_permission_required, PermissionRequiredMixin, PagePermissionRequiredMixin,
    MultiPermissionRequiredMixin, RoleRequiredMixin, cache_permission_result,
    clear_permission_cache_on_change, permission_test_mode
)
from .permissions import UserRoles


# ==================== 装饰器使用示例 ====================

@permission_required('products', 'view')
def product_list_view(request):
    """商品列表视图 - 使用基础权限装饰器"""
    return HttpResponse('Product List')


@page_permission_required('products_create', 'products')
def product_create_view(request):
    """商品创建视图 - 使用页面权限装饰器"""
    return HttpResponse('Product Create')


@page_permission_required()  # 自动解析权限
def auto_permission_view(request):
    """自动权限解析视图"""
    return HttpResponse('Auto Permission View')


def dynamic_permission_func(request, product_id):
    """动态生成权限键的函数"""
    return f"product.{product_id}.edit"


@dynamic_permission_required(dynamic_permission_func)
def product_edit_view(request, product_id):
    """商品编辑视图 - 使用动态权限装饰器"""
    return HttpResponse(f'Edit Product {product_id}')


@multi_permission_required([
    ('products', 'view'),
    ('inventory', 'view')
], operator='AND')
def product_inventory_view(request):
    """商品库存视图 - 需要多个权限"""
    return HttpResponse('Product Inventory View')


@multi_permission_required([
    ('products', 'add'),
    ('products', 'change')
], operator='OR')
def product_manage_view(request):
    """商品管理视图 - 需要任一权限"""
    return HttpResponse('Product Manage View')


# ==================== 性能优化装饰器示例 ====================

@cache_permission_result(timeout=600)  # 缓存10分钟
@permission_required('products', 'view')
def cached_product_view(request):
    """缓存权限结果的视图"""
    return HttpResponse('Cached Product View')


@clear_permission_cache_on_change
@permission_required('products', 'change')
def product_update_view(request):
    """更新商品时清除权限缓存"""
    # 模拟更新操作
    return HttpResponse('Product Updated')


@permission_test_mode
@permission_required('products', 'view')
def test_mode_view(request):
    """测试模式视图 - 在DEBUG模式下记录权限信息"""
    return HttpResponse('Test Mode View')


# ==================== Mixin使用示例 ====================

class ProductListView(PermissionRequiredMixin, View):
    """商品列表视图 - 使用权限Mixin"""
    permission_app = 'products'
    permission_action = 'view'
    
    def get(self, request):
        return HttpResponse('Product List via Mixin')


class ProductDetailView(PagePermissionRequiredMixin, View):
    """商品详情视图 - 使用页面权限Mixin"""
    page_permission_name = 'products_detail'
    page_app_name = 'products'
    
    def get(self, request, product_id):
        return HttpResponse(f'Product {product_id} Detail')


class AutoPermissionView(PagePermissionRequiredMixin, View):
    """自动权限解析视图 - 启用自动解析"""
    auto_resolve_permission = True
    
    def get(self, request):
        return HttpResponse('Auto Permission Mixin View')


class ProductManageView(MultiPermissionRequiredMixin, View):
    """商品管理视图 - 使用多权限Mixin"""
    required_permissions = [
        ('products', 'view'),
        ('products', 'change'),
        ('inventory', 'view')
    ]
    permission_operator = 'AND'
    
    def get(self, request):
        return HttpResponse('Product Management View')


class AdminOnlyView(RoleRequiredMixin, View):
    """管理员专用视图 - 使用角色Mixin"""
    allowed_roles = [UserRoles.SUPER_ADMIN, UserRoles.WAREHOUSE_MANAGER]
    
    def get(self, request):
        return HttpResponse('Admin Only View')


# ==================== 高级用法示例 ====================

class CustomPermissionView(PermissionRequiredMixin, View):
    """自定义权限检查视图"""
    permission_app = 'products'
    permission_action = 'view'
    use_cache = True
    cache_timeout = 600
    raise_exception = False
    
    def get_permission_denied_message(self):
        """自定义权限拒绝消息"""
        return '您需要商品查看权限才能访问此页面'
    
    def handle_permission_denied(self, request):
        """自定义权限拒绝处理"""
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({
                'error': 'permission_denied',
                'message': self.get_permission_denied_message(),
                'redirect_url': '/login/'
            }, status=403)
        else:
            return super().handle_permission_denied(request)
    
    def get(self, request):
        return HttpResponse('Custom Permission View')


class DynamicPermissionView(PagePermissionRequiredMixin, View):
    """动态权限视图"""
    auto_resolve_permission = False
    
    def get_page_permission_name(self):
        """动态获取权限名称"""
        # 可以根据请求参数、用户角色等动态确定权限
        if self.request.GET.get('admin_mode'):
            return 'products_admin'
        else:
            return 'products_view'
    
    def get_page_app_name(self):
        """动态获取应用名称"""
        return 'products'
    
    def get(self, request):
        return HttpResponse('Dynamic Permission View')


# ==================== API视图示例 ====================

@permission_required('products', 'view')
def api_product_list(request):
    """API商品列表"""
    products = [
        {'id': 1, 'name': 'Product 1'},
        {'id': 2, 'name': 'Product 2'}
    ]
    return JsonResponse({'products': products})


@page_permission_required('api_products_create', 'api')
def api_product_create(request):
    """API商品创建"""
    if request.method == 'POST':
        # 模拟创建逻辑
        return JsonResponse({'success': True, 'id': 123})
    return JsonResponse({'error': 'Method not allowed'}, status=405)


class APIProductView(MultiPermissionRequiredMixin, View):
    """API商品视图 - 支持多种操作"""
    
    def get_required_permissions(self):
        """根据HTTP方法动态确定所需权限"""
        if self.request.method == 'GET':
            return [('products', 'view')]
        elif self.request.method == 'POST':
            return [('products', 'add')]
        elif self.request.method in ['PUT', 'PATCH']:
            return [('products', 'change')]
        elif self.request.method == 'DELETE':
            return [('products', 'delete')]
        return []
    
    def get(self, request):
        return JsonResponse({'method': 'GET', 'data': 'product data'})
    
    def post(self, request):
        return JsonResponse({'method': 'POST', 'created': True})
    
    def put(self, request):
        return JsonResponse({'method': 'PUT', 'updated': True})
    
    def delete(self, request):
        return JsonResponse({'method': 'DELETE', 'deleted': True})


# ==================== 错误处理示例 ====================

@permission_required('products', 'view', raise_exception=False)
def safe_product_view(request):
    """安全的商品视图 - 不抛出异常，重定向到安全页面"""
    return HttpResponse('Safe Product View')


class GracefulPermissionView(PermissionRequiredMixin, View):
    """优雅的权限处理视图"""
    permission_app = 'products'
    permission_action = 'view'
    raise_exception = False
    
    def handle_permission_denied(self, request):
        """优雅处理权限拒绝"""
        context = {
            'error_message': '您没有权限访问此页面',
            'suggested_actions': [
                '联系管理员申请权限',
                '检查您的账户状态',
                '返回首页'
            ]
        }
        return render(request, 'errors/permission_denied.html', context, status=403)
    
    def get(self, request):
        return HttpResponse('Graceful Permission View')


# ==================== 使用说明 ====================

"""
装饰器使用指南：

1. 基础权限装饰器：
   @permission_required('app_name', 'action')
   - 检查用户是否有特定应用的特定操作权限

2. 页面权限装饰器：
   @page_permission_required('permission_name', 'app_name')
   - 检查用户是否有特定页面的访问权限
   - 支持自动权限解析

3. 动态权限装饰器：
   @dynamic_permission_required(permission_func)
   - 使用函数动态生成权限键

4. 多权限装饰器：
   @multi_permission_required(permissions, operator='AND'/'OR')
   - 检查多个权限的组合

5. Mixin类：
   - PermissionRequiredMixin: 基础权限检查
   - PagePermissionRequiredMixin: 页面权限检查
   - MultiPermissionRequiredMixin: 多权限检查
   - RoleRequiredMixin: 角色检查

6. 性能优化装饰器：
   - @cache_permission_result: 缓存权限检查结果
   - @clear_permission_cache_on_change: 在变更时清除缓存
   - @permission_test_mode: 测试模式记录

注意事项：
- 所有装饰器都会自动检查用户登录状态
- 支持AJAX请求的特殊处理
- 提供灵活的错误处理机制
- 支持权限缓存以提升性能
- 保持向后兼容性
"""
