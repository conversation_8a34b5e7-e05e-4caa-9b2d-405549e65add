# Generated by Django 4.1.2 on 2025-07-30 06:38

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("users", "0003_auditlog_auditlog_users_audit_user_id_4b4ca5_idx_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="PagePermission",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        max_length=100, unique=True, verbose_name="权限名称"
                    ),
                ),
                ("description", models.TextField(blank=True, verbose_name="权限描述")),
                (
                    "url_pattern",
                    models.CharField(
                        help_text="支持正则表达式，如：^/products/.*$",
                        max_length=200,
                        verbose_name="URL模式",
                    ),
                ),
                (
                    "url_name",
                    models.Char<PERSON>ield(
                        blank=True,
                        help_text="Django URL名称，如：products:list",
                        max_length=100,
                        verbose_name="URL名称",
                    ),
                ),
                (
                    "app_name",
                    models.CharField(
                        help_text="Django应用名称，如：products",
                        max_length=50,
                        verbose_name="应用名称",
                    ),
                ),
                (
                    "view_name",
                    models.CharField(
                        blank=True,
                        help_text="视图类或函数名称",
                        max_length=100,
                        verbose_name="视图名称",
                    ),
                ),
                (
                    "category",
                    models.CharField(
                        default="general",
                        help_text="权限分类，用于组织管理",
                        max_length=50,
                        verbose_name="权限分类",
                    ),
                ),
                (
                    "permission_level",
                    models.CharField(
                        choices=[
                            ("public", "公开访问"),
                            ("authenticated", "登录用户"),
                            ("role_based", "基于角色"),
                            ("custom", "自定义权限"),
                        ],
                        default="role_based",
                        max_length=20,
                        verbose_name="权限级别",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="是否启用"),
                ),
                (
                    "is_system",
                    models.BooleanField(
                        default=False,
                        help_text="系统权限不可删除",
                        verbose_name="系统权限",
                    ),
                ),
                ("sort_order", models.IntegerField(default=0, verbose_name="排序顺序")),
                (
                    "priority",
                    models.IntegerField(
                        default=0, help_text="数值越大优先级越高", verbose_name="优先级"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="created_page_permissions",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="创建人",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="updated_page_permissions",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="最后修改人",
                    ),
                ),
            ],
            options={
                "verbose_name": "页面权限",
                "verbose_name_plural": "页面权限",
                "ordering": ["category", "sort_order", "name"],
            },
        ),
        migrations.AddField(
            model_name="userprofile",
            name="custom_permissions",
            field=models.JSONField(
                blank=True,
                default=dict,
                help_text="用户特定的权限设置，覆盖角色默认权限",
                verbose_name="自定义权限",
            ),
        ),
        migrations.AddField(
            model_name="userprofile",
            name="permission_cache",
            field=models.JSONField(
                blank=True,
                default=dict,
                help_text="缓存用户权限信息，提升性能",
                verbose_name="权限缓存",
            ),
        ),
        migrations.AddField(
            model_name="userprofile",
            name="permission_cache_updated",
            field=models.DateTimeField(
                blank=True, null=True, verbose_name="权限缓存更新时间"
            ),
        ),
        migrations.CreateModel(
            name="RolePagePermission",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "role",
                    models.CharField(
                        choices=[
                            ("super_admin", "超级管理员"),
                            ("warehouse_manager", "仓库管理员"),
                            ("warehouse_operator", "仓库操作员"),
                            ("viewer", "查看员"),
                        ],
                        max_length=20,
                        verbose_name="角色",
                    ),
                ),
                (
                    "is_granted",
                    models.BooleanField(default=True, verbose_name="是否授权"),
                ),
                (
                    "grant_source",
                    models.CharField(
                        choices=[
                            ("default", "默认权限"),
                            ("manual", "手动分配"),
                            ("inherited", "继承权限"),
                            ("template", "模板权限"),
                        ],
                        default="manual",
                        max_length=20,
                        verbose_name="授权来源",
                    ),
                ),
                (
                    "valid_from",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="生效时间"
                    ),
                ),
                (
                    "valid_until",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="失效时间"
                    ),
                ),
                (
                    "granted_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="授权时间"),
                ),
                ("notes", models.TextField(blank=True, verbose_name="备注")),
                (
                    "granted_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="granted_role_permissions",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="授权人",
                    ),
                ),
                (
                    "page_permission",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="users.pagepermission",
                        verbose_name="页面权限",
                    ),
                ),
            ],
            options={
                "verbose_name": "角色页面权限",
                "verbose_name_plural": "角色页面权限",
                "ordering": [
                    "role",
                    "page_permission__category",
                    "page_permission__sort_order",
                ],
            },
        ),
        migrations.CreateModel(
            name="PermissionTemplate",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        max_length=100, unique=True, verbose_name="模板名称"
                    ),
                ),
                ("description", models.TextField(blank=True, verbose_name="模板描述")),
                (
                    "category",
                    models.CharField(
                        default="general", max_length=50, verbose_name="模板分类"
                    ),
                ),
                (
                    "target_roles",
                    models.JSONField(
                        default=list,
                        help_text="此模板适用的角色列表",
                        verbose_name="适用角色",
                    ),
                ),
                (
                    "permissions_config",
                    models.JSONField(
                        default=dict,
                        help_text="模板包含的权限配置",
                        verbose_name="权限配置",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="是否启用"),
                ),
                (
                    "is_system",
                    models.BooleanField(
                        default=False,
                        help_text="系统模板不可删除",
                        verbose_name="系统模板",
                    ),
                ),
                (
                    "usage_count",
                    models.IntegerField(default=0, verbose_name="使用次数"),
                ),
                (
                    "last_used",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="最后使用时间"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="created_permission_templates",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="创建人",
                    ),
                ),
            ],
            options={
                "verbose_name": "权限模板",
                "verbose_name_plural": "权限模板",
                "ordering": ["category", "name"],
            },
        ),
        migrations.CreateModel(
            name="CustomPermission",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "permission_type",
                    models.CharField(
                        choices=[
                            ("page", "页面权限"),
                            ("operation", "操作权限"),
                            ("data", "数据权限"),
                            ("field", "字段权限"),
                            ("custom", "自定义权限"),
                        ],
                        max_length=20,
                        verbose_name="权限类型",
                    ),
                ),
                (
                    "permission_key",
                    models.CharField(
                        help_text="权限的唯一标识符",
                        max_length=200,
                        verbose_name="权限键",
                    ),
                ),
                (
                    "permission_value",
                    models.JSONField(
                        default=dict,
                        help_text="权限的具体配置信息",
                        verbose_name="权限值",
                    ),
                ),
                (
                    "is_granted",
                    models.BooleanField(default=True, verbose_name="是否授权"),
                ),
                (
                    "priority",
                    models.IntegerField(
                        default=0,
                        help_text="数值越大优先级越高，用于解决权限冲突",
                        verbose_name="优先级",
                    ),
                ),
                (
                    "valid_from",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="生效时间"
                    ),
                ),
                (
                    "valid_until",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="失效时间"
                    ),
                ),
                (
                    "grant_reason",
                    models.CharField(
                        choices=[
                            ("manual", "手动分配"),
                            ("temporary", "临时授权"),
                            ("exception", "异常处理"),
                            ("delegation", "权限委托"),
                            ("system", "系统分配"),
                        ],
                        default="manual",
                        max_length=20,
                        verbose_name="授权原因",
                    ),
                ),
                (
                    "granted_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="授权时间"),
                ),
                ("description", models.TextField(blank=True, verbose_name="权限描述")),
                ("notes", models.TextField(blank=True, verbose_name="备注")),
                (
                    "granted_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="granted_custom_permissions",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="授权人",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="用户",
                    ),
                ),
            ],
            options={
                "verbose_name": "自定义权限",
                "verbose_name_plural": "自定义权限",
                "ordering": ["-priority", "-granted_at"],
            },
        ),
        migrations.AddIndex(
            model_name="rolepagepermission",
            index=models.Index(
                fields=["role", "is_granted"], name="users_rolep_role_2cd7c0_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="rolepagepermission",
            index=models.Index(
                fields=["page_permission", "is_granted"],
                name="users_rolep_page_pe_704351_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="rolepagepermission",
            index=models.Index(
                fields=["valid_from", "valid_until"],
                name="users_rolep_valid_f_cff567_idx",
            ),
        ),
        migrations.AlterUniqueTogether(
            name="rolepagepermission",
            unique_together={("role", "page_permission")},
        ),
        migrations.AddIndex(
            model_name="permissiontemplate",
            index=models.Index(
                fields=["category", "is_active"], name="users_permi_categor_5ab795_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="permissiontemplate",
            index=models.Index(
                fields=["is_system", "is_active"], name="users_permi_is_syst_0fb0f8_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="pagepermission",
            index=models.Index(
                fields=["app_name", "is_active"], name="users_pagep_app_nam_3c09e6_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="pagepermission",
            index=models.Index(
                fields=["url_pattern"], name="users_pagep_url_pat_c3def2_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="pagepermission",
            index=models.Index(
                fields=["category", "sort_order"], name="users_pagep_categor_f2c963_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="pagepermission",
            index=models.Index(
                fields=["permission_level", "is_active"],
                name="users_pagep_permiss_dc014c_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="custompermission",
            index=models.Index(
                fields=["user", "permission_type", "is_granted"],
                name="users_custo_user_id_a24a88_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="custompermission",
            index=models.Index(
                fields=["permission_key", "is_granted"],
                name="users_custo_permiss_04684e_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="custompermission",
            index=models.Index(
                fields=["valid_from", "valid_until"],
                name="users_custo_valid_f_424a7d_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="custompermission",
            index=models.Index(
                fields=["priority", "granted_at"], name="users_custo_priorit_67d5f0_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="custompermission",
            unique_together={("user", "permission_key")},
        ),
    ]
