from django.urls import path
from . import views

app_name = 'inventory'

urlpatterns = [
    path('', views.InventoryListView.as_view(), name='list'),
    path('edit/<int:pk>/', views.InventoryUpdateView.as_view(), name='edit'),
    path('<int:pk>/update/', views.InventoryUpdateView.as_view(), name='update'),
    path('delete/<int:pk>/', views.InventoryDeleteView.as_view(), name='delete'),
    path('api/update-quantity/', views.ajax_update_inventory_quantity, name='ajax_update_quantity'),
    path('api/search/', views.ajax_search, name='ajax_search'),
    path('api/chart-data/', views.get_chart_data, name='get_chart_data'),
]
