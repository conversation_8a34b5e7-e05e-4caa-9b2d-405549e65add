/* 
 * 仓库管理系统自定义样式
 * 基于Bootstrap 5框架的扩展样式
 * 作者：AI Assistant
 * 创建时间：2025-01-29
 */

/* CSS变量定义 - 色彩体系 */
:root {
    /* 主色调 - 深蓝色系 */
    --primary-color: #2c5aa0;
    --primary-light: #4a7bc8;
    --primary-dark: #1e3f73;
    --primary-gradient: linear-gradient(135deg, #2c5aa0, #4a7bc8);

    /* 辅助色 */
    --secondary-color: #6c757d;
    --secondary-light: #8d9498;
    --secondary-dark: #495057;
    --accent-color: #17a2b8;
    --accent-light: #20c997;

    /* 状态色 */
    --success-color: #28a745;
    --success-light: #34ce57;
    --success-dark: #1e7e34;
    --warning-color: #ffc107;
    --warning-light: #ffcd39;
    --warning-dark: #d39e00;
    --danger-color: #dc3545;
    --danger-light: #e15759;
    --danger-dark: #bd2130;
    --info-color: #17a2b8;
    --info-light: #20c997;
    --info-dark: #117a8b;

    /* 模块特色色彩 */
    --products-color: #2c5aa0;      /* 商品管理 - 蓝色 */
    --products-light: #4a7bc8;
    --products-gradient: linear-gradient(135deg, #2c5aa0, #4a7bc8);

    --inventory-color: #28a745;     /* 库存管理 - 绿色 */
    --inventory-light: #34ce57;
    --inventory-gradient: linear-gradient(135deg, #28a745, #34ce57);

    --transactions-color: #fd7e14;  /* 交易记录 - 橙色 */
    --transactions-light: #ff922b;
    --transactions-gradient: linear-gradient(135deg, #fd7e14, #ff922b);

    --users-color: #6f42c1;        /* 用户管理 - 紫色 */
    --users-light: #8a63d2;
    --users-gradient: linear-gradient(135deg, #6f42c1, #8a63d2);
    
    /* 中性色 */
    --light-gray: #f8f9fa;
    --medium-gray: #e9ecef;
    --dark-gray: #495057;
    
    /* 阴影 */
    --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
    
    /* 圆角 */
    --border-radius: 0.375rem;
    --border-radius-lg: 0.5rem;
    
    /* 过渡动画 */
    --transition: all 0.3s ease;
}

/* 基础样式重置和优化 */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--dark-gray);
}

/* Bootstrap色彩类覆盖 */
.bg-primary {
    background: var(--primary-gradient) !important;
}

.btn-primary {
    background: var(--primary-gradient);
    border-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: var(--primary-dark);
    border-color: var(--primary-dark);
    /* 移除transform和box-shadow动画效果 */
}

.btn-success {
    background: var(--success-color);
    border-color: var(--success-color);
}

.btn-success:hover {
    background: var(--success-dark);
    border-color: var(--success-dark);
    /* 移除transform和box-shadow动画效果 */
}

.btn-warning {
    background: var(--warning-color);
    border-color: var(--warning-color);
    color: #212529;
}

.btn-warning:hover {
    background: var(--warning-dark);
    border-color: var(--warning-dark);
    /* 移除transform和box-shadow动画效果 */
}

.btn-danger {
    background: var(--danger-color);
    border-color: var(--danger-color);
}

.btn-danger:hover {
    background: var(--danger-dark);
    border-color: var(--danger-dark);
    /* 移除transform和box-shadow动画效果 */
}

.text-primary {
    color: var(--primary-color) !important;
}

.text-success {
    color: var(--success-color) !important;
}

.text-warning {
    color: var(--warning-color) !important;
}

.text-danger {
    color: var(--danger-color) !important;
}

.border-primary {
    border-color: var(--primary-color) !important;
}

/* 应用容器布局 */
.app-container {
    display: flex;
    min-height: 100vh;
    position: relative;
}

/* 侧边栏样式优化 */
.sidebar {
    width: 17.5rem; /* 280px转换为rem单位，更好的缩放适配 */
    min-width: 17.5rem;
    height: 100vh;
    background: var(--primary-gradient) !important;
    box-shadow: 0.125rem 0 0.5rem rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(0.625rem);
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1030;
    display: flex;
    flex-direction: column;
    transition: var(--transition);
    overflow-x: hidden;
    overflow-y: auto;
}

/* 侧边栏头部样式 */
.sidebar-header {
    padding: 1.5rem 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-brand {
    display: flex;
    align-items: center;
    font-weight: 600;
    font-size: 1.25rem;
    color: white !important;
    text-decoration: none;
    transition: var(--transition);
    padding: 0.5rem;
    border-radius: var(--border-radius);
}

.sidebar-brand:hover {
    color: white !important;
    background-color: rgba(255, 255, 255, 0.1);
    transform: scale(1.02);
}

.sidebar-brand .brand-icon {
    font-size: 1.5rem;
    margin-right: 0.75rem;
    animation: pulse 2s infinite;
}

.sidebar-brand .brand-text {
    font-weight: 700;
    letter-spacing: 0.5px;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* 侧边栏导航区域 */
.sidebar-nav {
    flex: 1;
    padding: 1rem 0;
    overflow-y: auto;
}

.sidebar-nav .nav {
    padding: 0 1rem;
}

/* 侧边栏导航链接样式 */
.sidebar .nav-link {
    display: flex;
    align-items: center;
    color: rgba(255, 255, 255, 0.9) !important;
    text-decoration: none;
    transition: var(--transition);
    border-radius: var(--border-radius);
    margin: 0.25rem 0;
    padding: 0.75rem 1rem;
    font-weight: 500;
    position: relative;
    overflow: hidden;
}

.sidebar .nav-link i {
    width: 20px;
    margin-right: 0.75rem;
    font-size: 1.1rem;
    text-align: center;
}

.sidebar .nav-link .nav-text {
    flex: 1;
}

.sidebar .nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.sidebar .nav-link:hover::before {
    left: 100%;
}

.sidebar .nav-link:hover {
    color: white !important;
    background-color: rgba(255, 255, 255, 0.15);
    transform: translateX(5px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.sidebar .nav-link.active {
    color: white !important;
    background-color: rgba(255, 255, 255, 0.25);
    font-weight: 600;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.sidebar .nav-link.active::after {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 60%;
    background-color: white;
    border-radius: 0 2px 2px 0;
}

/* 侧边栏底部用户区域 */
.sidebar-footer {
    padding: 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.sidebar-footer .dropdown {
    width: 100%;
}

.sidebar-footer .user-dropdown {
    display: flex;
    align-items: center;
    width: 100%;
    color: rgba(255, 255, 255, 0.9) !important;
    text-decoration: none;
    transition: var(--transition);
    border-radius: var(--border-radius);
    padding: 0.75rem;
    font-weight: 500;
    position: relative;
    overflow: hidden;
}

.sidebar-footer .user-dropdown:hover {
    color: white !important;
    background-color: rgba(255, 255, 255, 0.15);
}

.sidebar-footer .user-avatar {
    font-size: 1.5rem;
    margin-right: 0.75rem;
    transition: var(--transition);
}

.sidebar-footer .user-dropdown:hover .user-avatar {
    transform: rotate(360deg);
}

.sidebar-footer .user-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.sidebar-footer .user-name {
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.sidebar-footer .admin-badge {
    font-size: 0.7rem;
    padding: 0.2rem 0.5rem;
    border-radius: 10px;
    animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
    from { box-shadow: 0 0 5px rgba(220, 53, 69, 0.5); }
    to { box-shadow: 0 0 10px rgba(220, 53, 69, 0.8); }
}

/* 侧边栏登录链接 */
.sidebar-login .nav-link {
    display: flex;
    align-items: center;
    color: rgba(255, 255, 255, 0.9) !important;
    text-decoration: none;
    transition: var(--transition);
    border-radius: var(--border-radius);
    padding: 0.75rem;
    font-weight: 500;
}

.sidebar-login .nav-link:hover {
    color: white !important;
    background-color: rgba(255, 255, 255, 0.15);
}

.sidebar-login .nav-link i {
    margin-right: 0.75rem;
    font-size: 1.1rem;
}

/* 主内容区域 - 修复布局冲突 */
.main-content {
    flex: 1;
    margin-left: 17.5rem; /* 与侧边栏宽度保持一致 */
    min-height: 100vh;
    transition: var(--transition);
    position: relative;
    /* 移除冲突的宽度设置，让flex处理 */
    overflow-x: auto; /* 防止内容溢出 */
    padding: 0; /* 移除默认内边距 */
    box-sizing: border-box;
}

/* 主内容区域的容器 - 优化宽度利用 */
.main-content .container {
    max-width: none; /* 移除Bootstrap容器的最大宽度限制 */
    width: 100%;
    padding-left: 1.5rem;
    padding-right: 1.5rem;
    margin: 0;
    box-sizing: border-box;
}

/* 侧边栏切换按钮 */
.sidebar-toggle {
    position: fixed;
    top: 1rem;
    right: 1.5rem; /* 改为右上角定位，避免遮挡标题 */
    z-index: 999; /* 进一步降低z-index，确保不遮挡页面内容 */
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 50%;
    width: 2.5rem; /* 40px转换为rem */
    height: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    box-shadow: var(--shadow);
    transition: var(--transition);
    cursor: pointer;
    opacity: 0.9; /* 添加透明度，减少视觉干扰 */
}

.sidebar-toggle:hover {
    background: var(--primary-dark);
    transform: scale(1.1);
    box-shadow: var(--shadow-lg);
    opacity: 1; /* 悬停时完全不透明 */
}

.sidebar-toggle:focus {
    outline: none;
    box-shadow: 0 0 0 0.1875rem rgba(44, 90, 160, 0.3);
}

/* 侧边栏折叠状态 */
.sidebar.collapsed {
    transform: translateX(-17.5rem);
}

.main-content.shifted {
    margin-left: 0 !important;
    width: 100% !important;
    max-width: 100% !important;
}

/* 紧凑模式样式 */
.main-content.compact-mode {
    margin-left: 3.75rem !important;
    width: calc(100% - 3.75rem) !important;
    max-width: calc(100% - 3.75rem) !important;
}

.sidebar-toggle.shifted {
    right: 1.5rem; /* 保持右上角位置 */
}

/* 侧边栏用户下拉菜单样式 */
.sidebar-footer .user-menu {
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: var(--border-radius-lg);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10px);
    background-color: rgba(255, 255, 255, 0.98);
    min-width: 250px;
    margin-top: 0;
    margin-bottom: 0.5rem;
    position: absolute;
    bottom: 100%;
    left: 0;
    right: 0;
    transform: translateY(-10px);
    opacity: 0;
    visibility: hidden;
    transition: all 0.2s cubic-bezier(0.16, 1, 0.3, 1);
    pointer-events: none;
    z-index: 1050;
    overflow: hidden;
    padding: 0.5rem 0;
}

/* 用户菜单显示状态 */
.sidebar-footer .user-menu.show {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
    pointer-events: auto;
}

/* 确保用户下拉菜单始终向上展开 */
.sidebar-footer .dropdown {
    position: relative;
    display: flex;
    flex-direction: column;
}

.sidebar-footer .dropdown-toggle {
    position: relative;
    z-index: 1;
}

.sidebar-footer .user-menu .dropdown-header {
    padding: 1rem;
    background: var(--primary-gradient);
    color: white;
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
    margin: -0.5rem -0.5rem 0.5rem -0.5rem;
}

.sidebar-footer .user-menu .dropdown-item {
    padding: 0.75rem 1rem;
    transition: var(--transition);
    border-radius: var(--border-radius);
    margin: 0.25rem 0.5rem;
}

.sidebar-footer .user-menu .dropdown-item:hover {
    background-color: var(--light-gray);
    transform: translateX(5px);
}

.sidebar-footer .user-menu .dropdown-item i {
    width: 20px;
    margin-right: 0.5rem;
}

.sidebar-footer .user-menu .dropdown-item.text-danger:hover {
    background-color: rgba(220, 53, 69, 0.1);
    color: var(--danger-color) !important;
}

/* 侧边栏响应式优化 */

/* 超大桌面端 (≥1400px) - 优化大屏显示 */
@media (min-width: 1400px) {
    .sidebar {
        width: 18.75rem; /* 稍微增加宽度以适应大屏 */
        min-width: 18.75rem;
    }

    .main-content {
        margin-left: 18.75rem;
        width: calc(100% - 18.75rem);
        max-width: calc(100% - 18.75rem);
    }

    .sidebar-toggle {
        right: 1.5rem; /* 右上角位置 */
    }

    .sidebar.collapsed {
        transform: translateX(-18.75rem);
    }

    .sidebar-toggle.shifted {
        right: 1.5rem; /* 保持右上角位置 */
    }
}

/* 大桌面端 (1200px-1399px) - 标准桌面显示 */
@media (min-width: 1200px) and (max-width: 1399.98px) {
    .sidebar {
        width: 17.5rem;
        min-width: 17.5rem;
    }

    .main-content {
        margin-left: 17.5rem;
        width: calc(100% - 17.5rem);
        max-width: calc(100% - 17.5rem);
    }
}

/* 桌面端 (992px-1199px) - 标准显示，支持折叠 */
@media (min-width: 992px) and (max-width: 1199.98px) {
    .sidebar-toggle {
        display: flex;
    }

    .sidebar.collapsed {
        transform: translateX(-17.5rem);
    }

    .main-content.shifted {
        margin-left: 0;
        width: 100%;
        max-width: 100%;
    }

    .sidebar-toggle.shifted {
        right: 1.5rem; /* 保持右上角位置 */
    }
}

/* 平板端 (768px - 991px) - 可折叠，默认隐藏 */
@media (min-width: 768px) and (max-width: 991.98px) {
    .sidebar {
        transform: translateX(-17.5rem);
        width: 17.5rem;
        min-width: 17.5rem;
    }

    .main-content {
        margin-left: 0;
        width: 100%;
        max-width: 100%;
    }

    .sidebar-toggle {
        right: 1.5rem; /* 右上角位置 */
        display: flex;
    }

    .sidebar.show {
        transform: translateX(0);
    }

    /* 平板端紧凑模式 */
    .sidebar:not(.show) {
        width: 3.75rem; /* 60px转换为rem */
        min-width: 3.75rem;
        transform: translateX(0);
    }

    .sidebar:not(.show) .sidebar-brand .brand-text,
    .sidebar:not(.show) .nav-link .nav-text {
        display: none;
    }

    .sidebar:not(.show) .sidebar-footer .user-info {
        display: none;
    }

    .sidebar:not(.show) .sidebar-header,
    .sidebar:not(.show) .sidebar-footer {
        padding: 1rem 0.5rem;
    }

    .main-content {
        margin-left: 3.75rem;
        width: calc(100% - 3.75rem);
        max-width: calc(100% - 3.75rem);
    }

    .sidebar.show ~ .main-content {
        margin-left: 0;
        width: 100%;
        max-width: 100%;
    }
}

/* 移动端 (<768px) - 完全隐藏，通过按钮切换 */
@media (max-width: 767.98px) {
    .sidebar {
        transform: translateX(-17.5rem);
        width: 17.5rem;
        min-width: 17.5rem;
        z-index: 1035;
    }

    .main-content {
        margin-left: 0;
        width: 100%;
        max-width: 100%;
    }

    .sidebar-toggle {
        right: 1.5rem; /* 右上角位置 */
        display: flex;
        z-index: 999; /* 进一步降低z-index，确保不遮挡页面内容 */
        width: 2.5rem;
        height: 2.5rem;
        opacity: 0.9; /* 添加透明度，减少视觉干扰 */
    }

    .sidebar.show {
        transform: translateX(0);
    }

    .sidebar-brand .brand-text,
    .nav-link .nav-text {
        display: block;
    }

    /* 移动端遮罩层 */
    .sidebar-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 1030;
        opacity: 0;
        visibility: hidden;
        transition: var(--transition);
        backdrop-filter: blur(0.125rem);
    }

    .sidebar-overlay.show {
        opacity: 1;
        visibility: visible;
    }

    /* 移动端优化 */
    .sidebar-header {
        padding: 1rem;
    }

    .sidebar-footer {
        padding: 1rem;
    }

    .sidebar .nav-link {
        padding: 1rem;
        font-size: 1rem;
        min-height: 3rem; /* 确保触摸目标足够大 */
    }

    .sidebar .nav-link i {
        font-size: 1.2rem;
        margin-right: 1rem;
        width: 1.5rem;
        text-align: center;
    }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
    .sidebar .nav-link {
        min-height: 48px;
        display: flex;
        align-items: center;
    }

    .sidebar-toggle {
        min-width: 48px;
        min-height: 48px;
        width: 48px;
        height: 48px;
    }

    .sidebar-footer .user-dropdown {
        min-height: 48px;
    }

    /* 移除hover效果，使用active状态 */
    .sidebar .nav-link:hover {
        transform: none;
    }

    .sidebar .nav-link:active {
        background-color: rgba(255, 255, 255, 0.2);
        transform: scale(0.98);
    }

    .sidebar-toggle:active {
        transform: scale(0.95);
    }
}

/* 高分辨率屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .sidebar {
        box-shadow: 2px 0 16px rgba(0, 0, 0, 0.2);
    }

    .sidebar-toggle {
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
    }
}

/* 减少动画的用户偏好 */
@media (prefers-reduced-motion: reduce) {
    .sidebar,
    .main-content,
    .sidebar-toggle,
    .sidebar-overlay {
        transition: none;
    }

    .sidebar .nav-link::before {
        transition: none;
    }

    .sidebar-brand .brand-icon {
        animation: none;
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    .sidebar-overlay {
        background-color: rgba(0, 0, 0, 0.7);
    }
}

/* 浏览器缩放适配优化 */
@media (min-resolution: 120dpi) and (max-resolution: 143dpi) {
    /* 125% 缩放优化 */
    .sidebar {
        box-shadow: 0.1rem 0 0.4rem rgba(0, 0, 0, 0.15);
    }

    .sidebar-toggle {
        box-shadow: 0 0.2rem 0.6rem rgba(0, 0, 0, 0.2);
    }
}

@media (min-resolution: 144dpi) and (max-resolution: 191dpi) {
    /* 150% 缩放优化 */
    .sidebar {
        box-shadow: 0.08rem 0 0.3rem rgba(0, 0, 0, 0.15);
    }

    .main-content {
        overflow-x: hidden; /* 防止水平滚动条 */
    }
}

@media (min-resolution: 192dpi) {
    /* 200% 及以上缩放优化 */
    .sidebar {
        box-shadow: 0.06rem 0 0.25rem rgba(0, 0, 0, 0.15);
    }

    .sidebar .nav-link {
        padding: 0.6rem 0.8rem;
    }

    .sidebar-header,
    .sidebar-footer {
        padding: 0.8rem;
    }
}

/* 低分辨率/75%缩放优化 */
@media (max-resolution: 95dpi) {
    .sidebar {
        box-shadow: 0.15rem 0 0.6rem rgba(0, 0, 0, 0.15);
    }

    .sidebar .nav-link {
        padding: 0.9rem 1.2rem;
    }
}

/* 容器最大宽度限制，防止在超宽屏幕上过度拉伸 */
.container {
    max-width: 100%;
}

/* 主内容区域内的表格容器不应用最大宽度限制 */
.main-content .table-responsive,
.main-content .card-data {
    max-width: none !important;
    width: 100% !important;
}

/* 强制修复统计卡片的Bootstrap网格布局 */
.row.mb-4 {
    display: flex !important;
    flex-wrap: wrap !important;
    margin-right: -0.75rem !important;
    margin-left: -0.75rem !important;
}

.row.mb-4 .col-md-3 {
    flex: 0 0 25% !important;
    max-width: 25% !important;
    width: 25% !important;
    padding-right: 0.75rem !important;
    padding-left: 0.75rem !important;
    position: relative !important;
}

/* 移除了错误的主内容区域布局覆盖 - 使用正常的侧边栏布局 */

/* 调整侧边栏宽度，让主内容区域更宽 */
.sidebar {
    width: 250px !important;
    min-width: 250px !important;
}

/* 旧的图表容器样式已移除，使用下方统一的图表样式 */

@media (min-width: 1400px) {
    .container:not(.main-content .container) {
        max-width: 1320px;
    }
}

@media (min-width: 1600px) {
    .container:not(.main-content .container) {
        max-width: 1520px;
    }
}

/* 表格响应式优化 */
.table-responsive {
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    background-color: white;
    position: relative;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch; /* iOS 平滑滚动 */
    width: 100%;
    max-width: 100%;
    margin: 0;
}

/* 卡片响应式优化 */
.card {
    word-wrap: break-word;
    overflow-wrap: break-word;
    width: 100%;
    max-width: 100%;
    margin: 0;
}

/* 特别针对包含表格的卡片 - 只对表格卡片应用这些样式 */
.card-data .card-body {
    padding: 0 !important; /* 移除卡片内边距，让表格完全填充 */
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    border: none !important;
}

/* 特别针对包含表格的卡片 */
.card-body .table-responsive {
    border-radius: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* 防止内容溢出的通用规则 */
* {
    box-sizing: border-box;
}

img {
    max-width: 100%;
    height: auto;
}

/* 文字缩放适配 */
@media (min-width: 1200px) {
    html {
        font-size: 16px;
    }
}

@media (max-width: 767.98px) {
    html {
        font-size: 14px;
    }
}

/* 表格对齐调试样式 - 临时使用 */
.debug-table-alignment {
    border: 2px solid red !important;
}

.debug-table-alignment .table {
    border: 2px solid blue !important;
}

.debug-table-alignment .table thead th {
    border: 1px solid green !important;
    background: var(--primary-gradient) !important;
}

.debug-table-alignment .table tbody td {
    border: 1px solid orange !important;
}

/* ========== 统一表格样式系统 ========== */

/* 统一表格容器样式 */
.unified-table-container {
    width: 100% !important;
    overflow-x: auto !important;
    background-color: white !important;
    border-radius: 8px !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
    margin-bottom: 1rem !important;
}

/* 统一表格基础样式 */
.unified-table {
    width: 100% !important;
    margin: 0 !important;
    border-collapse: separate !important;
    border-spacing: 0 !important;
    background-color: white !important;
    font-size: 14px !important;
    border: 1px solid #dee2e6 !important;
}

/* 统一表格头部样式 */
.unified-table thead th {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%) !important;
    color: white !important;
    text-align: center !important;
    font-weight: 600 !important;
    padding: 12px 8px !important;
    border: 1px solid #dee2e6 !important;
    vertical-align: middle !important;
    font-size: 13px !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
}

/* 复选框列头部样式 */
.unified-table thead th.checkbox-column {
    padding: 8px !important;
    text-transform: none !important;
    letter-spacing: normal !important;
}

/* 统一表格数据行样式 */
.unified-table tbody td {
    text-align: center !important;
    background-color: white !important;
    padding: 12px 8px !important;
    border: 1px solid #dee2e6 !important;
    vertical-align: middle !important;
    font-size: 14px !important;
}

/* 复选框列样式（使用类选择器更兼容） */
.unified-table tbody td.checkbox-column {
    text-align: center !important;
    padding: 8px !important;
}

/* 商品信息列左对齐（考虑有无复选框列的情况） */
.unified-table tbody td:nth-child(2),
.unified-table tbody td:nth-child(3) {
    text-align: left !important;
}

/* 如果没有复选框列，第一列左对齐 */
.unified-table tbody td:first-child:not(.checkbox-column) {
    text-align: left !important;
}

/* 商品表格特定列宽优化 - 使用最高优先级 */
body .main-content .card .unified-table-container .unified-table#product-table {
    table-layout: fixed !important;
    width: 100% !important;
    min-width: 1000px !important; /* 增加最小宽度以容纳操作按钮 */
}

/* 清除可能的宽度冲突 - 使用最高优先级 */
body .main-content .card .unified-table-container .unified-table#product-table th,
body .main-content .card .unified-table-container .unified-table#product-table td {
    max-width: none !important;
    min-width: 0 !important;
    box-sizing: border-box !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
}

/* 商品表格列宽分配 - 有删除权限（9列，总和100%） - 最高优先级 */
body .main-content .unified-table#product-table th:nth-child(1) { width: 5% !important; }    /* 复选框 */
body .main-content .unified-table#product-table th:nth-child(2) { width: 22% !important; }   /* 商品名称 */
body .main-content .unified-table#product-table th:nth-child(3) { width: 11% !important; }   /* 商品编码 */
body .main-content .unified-table#product-table th:nth-child(4) { width: 9% !important; }    /* 价格 */
body .main-content .unified-table#product-table th:nth-child(5) { width: 7% !important; }    /* 状态 */
body .main-content .unified-table#product-table th:nth-child(6) { width: 13% !important; }   /* 创建时间 */
body .main-content .unified-table#product-table th:nth-child(7) { width: 9% !important; }    /* 创建人 */
body .main-content .unified-table#product-table th:nth-child(8) { width: 12% !important; }   /* 最后修改 */
body .main-content .unified-table#product-table th:nth-child(9) { width: 12% !important; }   /* 操作 */

/* 对应的td列宽 - 最高优先级 */
body .main-content .unified-table#product-table td:nth-child(1) { width: 5% !important; }
body .main-content .unified-table#product-table td:nth-child(2) { width: 22% !important; }
body .main-content .unified-table#product-table td:nth-child(3) { width: 11% !important; }
body .main-content .unified-table#product-table td:nth-child(4) { width: 9% !important; }
body .main-content .unified-table#product-table td:nth-child(5) { width: 7% !important; }
body .main-content .unified-table#product-table td:nth-child(6) { width: 13% !important; }
body .main-content .unified-table#product-table td:nth-child(7) { width: 9% !important; }
body .main-content .unified-table#product-table td:nth-child(8) { width: 12% !important; }
body .main-content .unified-table#product-table td:nth-child(9) { width: 12% !important; }

/* 商品表格强制布局修复 - 根据权限动态调整 */
table#product-table {
    table-layout: fixed !important;
    width: 100% !important;
}

/* 有删除权限时的列宽（9列） - 修复操作列宽度 */
body.has-delete-permission table#product-table th:nth-child(1),
body.has-delete-permission table#product-table td:nth-child(1) { width: 60px !important; }    /* 复选框 */
body.has-delete-permission table#product-table th:nth-child(2),
body.has-delete-permission table#product-table td:nth-child(2) { width: 22% !important; }     /* 商品名称 */
body.has-delete-permission table#product-table th:nth-child(3),
body.has-delete-permission table#product-table td:nth-child(3) { width: 11% !important; }     /* 商品编码 */
body.has-delete-permission table#product-table th:nth-child(4),
body.has-delete-permission table#product-table td:nth-child(4) { width: 9% !important; }      /* 价格 */
body.has-delete-permission table#product-table th:nth-child(5),
body.has-delete-permission table#product-table td:nth-child(5) { width: 7% !important; }      /* 状态 */
body.has-delete-permission table#product-table th:nth-child(6),
body.has-delete-permission table#product-table td:nth-child(6) { width: 13% !important; }     /* 创建时间 */
body.has-delete-permission table#product-table th:nth-child(7),
body.has-delete-permission table#product-table td:nth-child(7) { width: 9% !important; }      /* 创建人 */
body.has-delete-permission table#product-table th:nth-child(8),
body.has-delete-permission table#product-table td:nth-child(8) { width: 18% !important; }     /* 最后修改 */

/* 无删除权限时的列宽（7列，移除操作列） */
body.no-delete-permission table#product-table th:nth-child(1),
body.no-delete-permission table#product-table td:nth-child(1) { width: 35% !important; }      /* 商品名称 */
body.no-delete-permission table#product-table th:nth-child(2),
body.no-delete-permission table#product-table td:nth-child(2) { width: 18% !important; }      /* 商品编码 */
body.no-delete-permission table#product-table th:nth-child(3),
body.no-delete-permission table#product-table td:nth-child(3) { width: 15% !important; }      /* 价格 */
body.no-delete-permission table#product-table th:nth-child(4),
body.no-delete-permission table#product-table td:nth-child(4) { width: 12% !important; }      /* 状态 */
body.no-delete-permission table#product-table th:nth-child(5),
body.no-delete-permission table#product-table td:nth-child(5) { width: 20% !important; }      /* 创建时间 */
body.no-delete-permission table#product-table th:nth-child(6),
body.no-delete-permission table#product-table td:nth-child(6) { width: 15% !important; }      /* 创建人 */
body.no-delete-permission table#product-table th:nth-child(7),
body.no-delete-permission table#product-table td:nth-child(7) { width: 20% !important; }      /* 最后修改 */

/* 没有删除权限时的列宽分配（8列，总和100%）- 使用更兼容的选择器 */
.no-delete-permission .unified-table#product-table th:nth-child(1) { width: 23% !important; }   /* 商品名称 */
.no-delete-permission .unified-table#product-table th:nth-child(2) { width: 12% !important; }   /* 商品编码 */
.no-delete-permission .unified-table#product-table th:nth-child(3) { width: 10% !important; }   /* 价格 */
.no-delete-permission .unified-table#product-table th:nth-child(4) { width: 8% !important; }    /* 状态 */
.no-delete-permission .unified-table#product-table th:nth-child(5) { width: 15% !important; }   /* 创建时间 */
.no-delete-permission .unified-table#product-table th:nth-child(6) { width: 10% !important; }   /* 创建人 */
.no-delete-permission .unified-table#product-table th:nth-child(7) { width: 14% !important; }   /* 最后修改 */
.no-delete-permission .unified-table#product-table th:nth-child(8) { width: 8% !important; }    /* 操作 */

.no-delete-permission .unified-table#product-table td:nth-child(1) { width: 23% !important; }
.no-delete-permission .unified-table#product-table td:nth-child(2) { width: 12% !important; }
.no-delete-permission .unified-table#product-table td:nth-child(3) { width: 10% !important; }
.no-delete-permission .unified-table#product-table td:nth-child(4) { width: 8% !important; }
.no-delete-permission .unified-table#product-table td:nth-child(5) { width: 15% !important; }
.no-delete-permission .unified-table#product-table td:nth-child(6) { width: 10% !important; }
.no-delete-permission .unified-table#product-table td:nth-child(7) { width: 14% !important; }
.no-delete-permission .unified-table#product-table td:nth-child(8) { width: 8% !important; }

/* 调试：为表格添加边框以便查看列宽 */
.debug-table-layout .unified-table#product-table th,
.debug-table-layout .unified-table#product-table td {
    border: 1px solid red !important;
    position: relative !important;
}

.debug-table-layout .unified-table#product-table th::after,
.debug-table-layout .unified-table#product-table td::after {
    content: attr(data-width) !important;
    position: absolute !important;
    top: 0 !important;
    right: 0 !important;
    background: rgba(255, 0, 0, 0.8) !important;
    color: white !important;
    font-size: 10px !important;
    padding: 2px !important;
    z-index: 1000 !important;
}

/* 表格中的复选框样式优化 */
.unified-table .form-check {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    margin: 0 !important;
    padding: 0 !important;
}

.unified-table .form-check-input {
    margin: 0 !important;
    transform: scale(1.1) !important;
    cursor: pointer !important;
}

.unified-table .form-check-label {
    margin: 0 !important;
    cursor: pointer !important;
}

/* 全选复选框特殊样式 */
.unified-table thead .form-check-input {
    transform: scale(1.2) !important;
    border-width: 2px !important;
}

/* 复选框列的响应式处理 */
@media (max-width: 768px) {
    .unified-table#product-table th:nth-child(1),
    .unified-table#product-table td:nth-child(1) {
        width: 40px !important;
    }

    .unified-table .form-check-input {
        transform: scale(1.0) !important;
    }

    /* 小屏幕下操作按钮只显示图标 */
    #product-table .btn-group .btn {
        padding: 0.15rem 0.3rem !important;
        font-size: 0.7rem !important;
    }

    #product-table .btn-group .btn .bi {
        margin-right: 0 !important;
    }

    /* 隐藏按钮文字，只保留图标 */
    #product-table .btn-group .btn .btn-text {
        display: none !important;
    }

    #product-table .btn-group .btn i {
        font-size: 0.8rem !important;
        margin-right: 0 !important;
    }
}

/* 统一悬停效果 */
.unified-table tbody tr:hover {
    background-color: #f8f9fa !important;
}

/* ========== 表格主题颜色 - 统一蓝紫色 ========== */

/* 所有表格统一使用蓝紫色渐变主题 */
.unified-table.table-theme-primary thead th,
.unified-table.table-theme-success thead th,
.unified-table.table-theme-warning thead th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

/* 进出库页面info主题表头样式 */
.unified-table.table-theme-info thead th {
    background: linear-gradient(135deg, #17a2b8 0%, #20c997 100%) !important;
    color: white !important;
    text-align: center !important;
    font-weight: 600 !important;
    padding: 12px 8px !important;
    border: none !important;
    font-size: 14px !important;
}

/* 特殊行样式 - 库存警告 */
.unified-table tbody tr.table-warning {
    background-color: #fff3cd !important;
}

.unified-table tbody tr.table-warning:hover {
    background-color: #ffeaa7 !important;
}

/* 旧的重复样式已删除，现在使用统一表格样式系统 */

/* ========== 进出库记录表格样式 ========== */
.transactions-table-container {
    width: 100% !important;
    overflow-x: auto !important;
    background-color: white !important;
    border-radius: 8px !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
}

.transactions-table {
    width: 100% !important;
    margin: 0 !important;
    border-collapse: separate !important;
    border-spacing: 0 !important;
    background-color: white !important;
    font-size: 14px !important;
    border: 1px solid #dee2e6 !important;
}

.transactions-table thead th {
    background: linear-gradient(135deg, #fd7e14 0%, #e83e8c 100%) !important;
    color: white !important;
    text-align: center !important;
    font-weight: 600 !important;
    padding: 12px 8px !important;
    border: 1px solid #dee2e6 !important;
    vertical-align: middle !important;
    font-size: 13px !important;
    text-transform: uppercase !important;
}

.transactions-table tbody td {
    text-align: center !important;
    background-color: white !important;
    padding: 12px 8px !important;
    border: 1px solid #dee2e6 !important;
    vertical-align: middle !important;
    font-size: 14px !important;
}

.transactions-table tbody td:first-child,
.transactions-table tbody td:nth-child(2) {
    text-align: left !important;
}

.transactions-table tbody tr:hover {
    background-color: #f8f9fa !important;
}

.products-table-container .table tbody tr:hover td {
    background-color: rgba(44, 90, 160, 0.05) !important;
}

/* 删除所有列宽度限制，让表格自然对齐 */

/* 移除最后一列的右边框 */
.products-table-container .table thead th:last-child,
.products-table-container .table tbody td:last-child {
    border-right: none !important;
}

/* 临时调试边框 - 帮助查看对齐效果 */
.debug-table-borders .products-table-container .table thead th {
    border: 2px solid red !important;
}

.debug-table-borders .products-table-container .table tbody td {
    border: 1px solid blue !important;
}

/* 移除有问题的重置规则 */

/* 重复的样式定义已删除 */

/* 缩放级别专用优化 */
.zoom-75 .sidebar {
    width: 18.75rem;
    min-width: 18.75rem;
}

.zoom-75 .main-content {
    margin-left: 18.75rem;
    width: calc(100% - 18.75rem);
    max-width: calc(100% - 18.75rem);
}

.zoom-75 .sidebar-toggle {
    right: 1.5rem; /* 保持右上角位置 */
}

.zoom-125 .sidebar {
    width: 16.25rem;
    min-width: 16.25rem;
}

.zoom-125 .main-content {
    margin-left: 16.25rem;
    width: calc(100% - 16.25rem);
    max-width: calc(100% - 16.25rem);
}

.zoom-125 .sidebar-toggle {
    right: 1.5rem; /* 保持右上角位置 */
}

.zoom-150 .sidebar {
    width: 15rem;
    min-width: 15rem;
}

.zoom-150 .main-content {
    margin-left: 15rem;
    width: calc(100% - 15rem);
    max-width: calc(100% - 15rem);
}

.zoom-150 .sidebar-toggle {
    right: 1.5rem; /* 保持右上角位置 */
}

.zoom-200 .sidebar {
    width: 13.75rem;
    min-width: 13.75rem;
}

.zoom-200 .main-content {
    margin-left: 13.75rem;
    width: calc(100% - 13.75rem);
    max-width: calc(100% - 13.75rem);
}

.zoom-200 .sidebar-toggle {
    right: 1.5rem; /* 保持右上角位置 */
}

.zoom-200 .sidebar .nav-link {
    padding: 0.5rem 0.75rem;
    font-size: 0.9rem;
}

.zoom-200 .sidebar-header,
.zoom-200 .sidebar-footer {
    padding: 0.75rem;
}

/* 高缩放比例下的模态框优化 */
.zoom-150 #editModal .modal-dialog,
.zoom-200 #editModal .modal-dialog {
    max-width: min(95vw, 35rem);
}

.zoom-200 #editModal .modal-body {
    padding: 1rem;
    font-size: 0.9rem;
}

.zoom-200 #editModal .modal-header,
.zoom-200 #editModal .modal-footer {
    padding: 0.75rem 1rem;
}

/* 低缩放比例下的优化 */
.zoom-75 #editModal .modal-dialog {
    max-width: min(85vw, 65rem);
}

.zoom-75 .sidebar .nav-link {
    padding: 1rem 1.25rem;
}

.zoom-75 .sidebar-header,
.zoom-75 .sidebar-footer {
    padding: 1.25rem;
}

/* 编辑模态框样式 */
#editModal .modal-dialog {
    transition: var(--transition);
    max-width: min(90vw, 50rem); /* 响应式最大宽度 */
    margin: 1.75rem auto;
}

#editModal.fade .modal-dialog {
    transform: scale(0.8) translateY(-3.125rem);
}

#editModal.show .modal-dialog {
    transform: scale(1) translateY(0);
}

#editModal .modal-content {
    border: none;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    max-height: calc(100vh - 3.5rem); /* 确保不超出视口 */
    display: flex;
    flex-direction: column;
}

#editModal .modal-header {
    background: var(--primary-gradient);
    color: white;
    border: none;
    padding: 1.5rem;
    flex-shrink: 0;
}

#editModal .modal-header .modal-title {
    font-weight: 600;
    font-size: 1.25rem;
    line-height: 1.2;
}

#editModal .modal-header .btn-close {
    filter: invert(1);
    opacity: 0.8;
    transition: var(--transition);
    width: 1.5rem;
    height: 1.5rem;
}

#editModal .modal-header .btn-close:hover {
    opacity: 1;
    transform: scale(1.1);
}

#editModal .modal-body {
    padding: 2rem;
    max-height: calc(70vh - 8rem); /* 减去头部和底部的高度 */
    overflow-y: auto;
    flex: 1;
}

#editModal .modal-footer {
    border: none;
    padding: 1rem 2rem 2rem;
    background-color: var(--light-gray);
    flex-shrink: 0;
}

#editModal .modal-footer .btn {
    min-width: 7.5rem; /* 120px转换为rem */
    font-weight: 500;
    padding: 0.75rem 1.5rem;
}

/* 模态框表单样式增强 */
#editModal .form-label.required {
    font-weight: 600;
    color: var(--dark-gray);
}

#editModal .form-control,
#editModal .form-select {
    border: 2px solid var(--medium-gray);
    border-radius: var(--border-radius);
    transition: var(--transition);
    font-size: 1rem;
}

#editModal .form-control:focus,
#editModal .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(44, 90, 160, 0.25);
    transform: translateY(-1px);
}

#editModal .form-control.is-invalid,
#editModal .form-select.is-invalid {
    border-color: var(--danger-color);
    background-image: none;
}

#editModal .form-control.is-valid,
#editModal .form-select.is-valid {
    border-color: var(--success-color);
    background-image: none;
}

#editModal .invalid-feedback {
    display: block;
    font-size: 0.875rem;
    color: var(--danger-color);
    font-weight: 500;
    margin-top: 0.5rem;
}

#editModal .form-text {
    font-size: 0.875rem;
    color: var(--secondary-color);
    margin-top: 0.5rem;
}

#editModal .form-check {
    margin-top: 1rem;
}

#editModal .form-check-input {
    width: 1.25rem;
    height: 1.25rem;
    border: 2px solid var(--medium-gray);
    border-radius: var(--border-radius);
}

#editModal .form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

#editModal .form-check-label {
    font-weight: 500;
    margin-left: 0.5rem;
    cursor: pointer;
}

/* 模态框加载状态 */
#editModal .spinner-border {
    width: 3rem;
    height: 3rem;
}

#editModal .spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* 模态框动画增强 */
#editModal .alert {
    border: none;
    border-radius: var(--border-radius);
    padding: 1rem 1.5rem;
    margin-bottom: 1.5rem;
}

#editModal .alert-danger {
    background-color: rgba(220, 53, 69, 0.1);
    color: var(--danger-color);
    border-left: 4px solid var(--danger-color);
}

/* 编辑按钮样式增强 */
.edit-btn {
    position: relative;
    overflow: hidden;
}

.edit-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s;
}

.edit-btn:hover::before {
    left: 100%;
}

.edit-btn:hover {
    /* 移除transform和box-shadow动画效果 */
}

/* 响应式模态框 */
@media (max-width: 991.98px) {
    #editModal .modal-dialog {
        max-width: min(95vw, 40rem);
        margin: 1rem auto;
    }

    #editModal .modal-body {
        padding: 1.5rem;
        max-height: calc(65vh - 6rem);
    }

    #editModal .modal-header,
    #editModal .modal-footer {
        padding: 1rem 1.5rem;
    }
}

@media (max-width: 767.98px) {
    #editModal .modal-dialog {
        margin: 0.5rem;
        max-width: calc(100vw - 1rem);
        width: calc(100vw - 1rem);
    }

    #editModal .modal-content {
        max-height: calc(100vh - 1rem);
    }

    #editModal .modal-body {
        padding: 1rem;
        max-height: calc(60vh - 4rem);
        font-size: 0.9rem;
    }

    #editModal .modal-header {
        padding: 1rem;
    }

    #editModal .modal-footer {
        padding: 1rem;
        flex-direction: column;
        gap: 0.5rem;
    }

    #editModal .modal-footer .btn {
        width: 100%;
        min-width: auto;
    }

    #editModal .row > .col-md-6 {
        margin-bottom: 1rem;
    }

    #editModal .form-control,
    #editModal .form-select {
        font-size: 1rem; /* 防止iOS缩放 */
    }
}

/* 高分辨率屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    #editModal .modal-content {
        box-shadow: 0 0.5rem 2rem rgba(0, 0, 0, 0.3);
    }
}

/* 浏览器缩放适配 */
@media (min-width: 1200px) {
    #editModal .modal-dialog {
        max-width: min(80vw, 60rem);
    }
}

@media (min-width: 1400px) {
    #editModal .modal-dialog {
        max-width: min(70vw, 65rem);
    }
}

/* 卡片组件优化 */
.card {
    border: none;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
    background: linear-gradient(145deg, #ffffff, #f8f9fa);
    overflow: hidden;
    position: relative;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.6s;
    z-index: 1;
}

.card:hover::before {
    left: 100%;
}

.card:hover {
    box-shadow: var(--shadow-lg);
    /* 移除transform动画效果 */
}

.card-header {
    background: var(--primary-gradient);
    color: white;
    border: none;
    font-weight: 600;
    padding: 1rem 1.5rem;
    position: relative;
    z-index: 2;
}

.card-header h4,
.card-header h5,
.card-header h6 {
    margin: 0;
    color: white;
}

.card-body {
    padding: 1.5rem;
    position: relative;
    z-index: 2;
}

.card-footer {
    background-color: var(--light-gray);
    border: none;
    padding: 1rem 1.5rem;
    position: relative;
    z-index: 2;
}

/* 特殊卡片样式 */
.card.card-stats {
    background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
    border-left: 4px solid var(--primary-color);
    transition: var(--transition);
}

.card.card-stats:hover {
    border-left-width: 6px;
    /* 移除transform动画效果 */
}

.card.card-search {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.card.card-data {
    background: linear-gradient(145deg, #ffffff, #f1f3f4);
}

/* 按钮样式优化 */
.btn {
    border-radius: var(--border-radius);
    font-weight: 600;
    transition: var(--transition);
    padding: 0.75rem 1.5rem;
    font-size: 0.9rem;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
    border: 2px solid transparent;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    /* 移除transform和box-shadow动画效果 */
}

.btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* 按钮尺寸变体 */
.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

.btn-lg {
    padding: 1rem 2rem;
    font-size: 1.1rem;
}

/* 按钮组优化 */
.btn-group .btn {
    margin: 0;
    border-radius: 0;
}

.btn-group .btn:first-child {
    border-radius: var(--border-radius) 0 0 var(--border-radius);
}

.btn-group .btn:last-child {
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
}

.btn-group .btn:only-child {
    border-radius: var(--border-radius);
}

.btn-group:hover .btn:not(:hover) {
    opacity: 0.7;
}

/* 特殊按钮样式 */
.btn-outline-primary {
    border-color: var(--primary-color);
    color: var(--primary-color);
    background-color: transparent;
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
    box-shadow: 0 4px 12px rgba(44, 90, 160, 0.3);
}

.btn-outline-secondary {
    border-color: var(--secondary-color);
    color: var(--secondary-color);
    background-color: transparent;
}

.btn-outline-secondary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    color: white;
}

/* 加载状态按钮 */
.btn.loading {
    pointer-events: none;
    opacity: 0.7;
    position: relative;
}

.btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.btn.loading .btn-text {
    opacity: 0;
}

/* 图标按钮 */
.btn-icon {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-icon i {
    font-size: 1.1em;
}

.btn-icon-only {
    padding: 0.75rem;
    width: auto;
    height: auto;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.btn-icon-only i {
    margin: 0;
}

/* 旧的重复表格样式已删除，现在使用统一表格样式系统 */

/* 覆盖Bootstrap的table-light类 - 统一表格样式 */
.unified-table thead.table-light th {
    background: inherit !important;
    color: white !important;
    border-color: transparent !important;
}

/* 确保表头样式不被覆盖 */
.unified-table thead th {
    color: white !important;
    border-color: transparent !important;
    /* 不覆盖background，让主题样式生效 */
}

/* 旧的重复列宽样式已删除 */

/* 所有旧的重复表格样式已删除，现在使用统一表格样式系统 */

.table tbody tr {
    position: relative;
}

.table tbody tr::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 0;
    background: var(--primary-gradient);
    /* 移除transition动画效果 */
    z-index: 1;
}

.table tbody tr:hover::before {
    /* 移除悬停时的左边框动画效果 */
    width: 0;
}

.table tbody tr:hover {
    background-color: rgba(44, 90, 160, 0.05);
    /* 移除transform和box-shadow动画效果 */
}

.table tbody tr:hover td {
    color: var(--primary-dark);
}

/* 斑马纹优化 */
.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(248, 249, 250, 0.5);
}

.table-striped tbody tr:nth-of-type(odd):hover {
    background-color: rgba(44, 90, 160, 0.08);
}

/* 表格响应式容器 */
.table-responsive {
    border-radius: var(--border-radius-lg) !important;
    box-shadow: var(--shadow-sm) !important;
    background-color: white !important;
    position: relative !important;
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
    overflow-x: auto !important;
    -webkit-overflow-scrolling: touch;
    border: none !important;
}

/* 强制表格容器内的表格占满宽度 */
.table-responsive .table {
    margin: 0 !important;
    width: 100% !important;
    min-width: 100% !important;
}

.table-responsive::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: var(--border-radius-lg);
    background: linear-gradient(145deg, transparent, rgba(255, 255, 255, 0.1));
    pointer-events: none;
    z-index: 1;
}

/* 表格加载状态 */
.table-loading {
    position: relative;
    opacity: 0.6;
    pointer-events: none;
}

.table-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 40px;
    height: 40px;
    border: 4px solid var(--light-gray);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 10;
}

/* 表格操作按钮组 */
.table .btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    border-radius: var(--border-radius);
    margin: 0 1px;
}

/* 商品表格操作列按钮优化 */
#product-table .btn-group .btn {
    padding: 0.2rem 0.4rem !important;
    font-size: 0.75rem !important;
    white-space: nowrap !important;
    margin: 0 1px !important;
}

#product-table .btn-group .btn i {
    font-size: 0.7rem !important;
    margin-right: 2px !important;
}

/* 确保大屏幕上按钮文字显示 */
@media (min-width: 769px) {
    #product-table .btn-group .btn .btn-text {
        display: inline !important;
    }
}

/* 审计日志表格特定样式 */
#audit-log-table {
    table-layout: fixed !important;
    width: 100% !important;
}

#audit-log-table th:nth-child(1),
#audit-log-table td:nth-child(1) { width: 12% !important; }  /* 时间 */
#audit-log-table th:nth-child(2),
#audit-log-table td:nth-child(2) { width: 10% !important; }  /* 用户 */
#audit-log-table th:nth-child(3),
#audit-log-table td:nth-child(3) { width: 8% !important; }   /* 操作 */
#audit-log-table th:nth-child(4),
#audit-log-table td:nth-child(4) { width: 10% !important; }  /* 对象类型 */
#audit-log-table th:nth-child(5),
#audit-log-table td:nth-child(5) { width: 15% !important; }  /* 对象 */
#audit-log-table th:nth-child(6),
#audit-log-table td:nth-child(6) { width: 35% !important; }  /* 变更详情 */
#audit-log-table th:nth-child(7),
#audit-log-table td:nth-child(7) { width: 10% !important; }  /* IP地址 */

/* 商品行双击编辑样式 */
.product-row[style*="pointer"] {
    transition: background-color 0.2s ease;
}

.product-row[style*="pointer"]:hover {
    background-color: rgba(44, 90, 160, 0.05) !important;
}

.product-row[style*="pointer"]:hover::after {
    content: "双击编辑";
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(44, 90, 160, 0.8);
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.7rem;
    pointer-events: none;
    z-index: 10;
}

.product-row {
    position: relative;
}

.table .btn-group-sm .btn:hover {
    /* 移除transform和box-shadow动画效果 */
}

/* 表单样式优化 */
.form-control, .form-select {
    border-radius: var(--border-radius);
    border: 2px solid var(--medium-gray);
    /* 移除transition动画效果 */
    padding: 0.75rem 1rem;
    font-size: 1rem;
    background-color: #fff;
    position: relative;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(44, 90, 160, 0.25);
    background-color: #fff;
    outline: none;
    /* 移除transform动画效果 */
}

.form-control:hover:not(:focus), .form-select:hover:not(:focus) {
    border-color: var(--primary-light);
    /* 移除悬停时的阴影动画效果 */
}

/* 浮动标签效果 */
.form-floating {
    position: relative;
}

.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label {
    opacity: 0.65;
    transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
    color: var(--primary-color);
}

.form-floating > label {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    padding: 1rem;
    pointer-events: none;
    border: 1px solid transparent;
    transform-origin: 0 0;
    transition: var(--transition);
    color: var(--secondary-color);
}

/* 表单验证状态 */
.form-control.is-valid, .form-select.is-valid {
    border-color: var(--success-color);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='m2.3 6.73.94-.94 2.94 2.94L8.5 6.4l.94.94L6.5 10.27z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.form-control.is-valid:focus, .form-select.is-valid:focus {
    border-color: var(--success-color);
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.form-control.is-invalid, .form-select.is-invalid {
    border-color: var(--danger-color);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 4.6 2.4 2.4m0-2.4L5.8 7'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.form-control.is-invalid:focus, .form-select.is-invalid:focus {
    border-color: var(--danger-color);
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

/* 表单标签优化 */
.form-label {
    font-weight: 600;
    color: var(--dark-gray);
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.form-label.required::after {
    content: ' *';
    color: var(--danger-color);
}

/* 表单文本和帮助信息 */
.form-text {
    font-size: 0.875rem;
    color: var(--secondary-color);
    margin-top: 0.25rem;
}

.valid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: var(--success-color);
    font-weight: 500;
}

.invalid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: var(--danger-color);
    font-weight: 500;
}

/* 模块特色样式类 */
/* 商品管理模块 */
.module-products .card-header,
.module-products .btn-primary {
    background: var(--products-gradient);
    border-color: var(--products-color);
}

.module-products .nav-link.active {
    background-color: var(--products-color) !important;
}

.module-products .badge-primary {
    background-color: var(--products-color);
}

/* 库存管理模块 */
.module-inventory .card-header,
.module-inventory .btn-primary {
    background: var(--inventory-gradient);
    border-color: var(--inventory-color);
}

.module-inventory .nav-link.active {
    background-color: var(--inventory-color) !important;
}

.module-inventory .badge-primary {
    background-color: var(--inventory-color);
}

/* 交易记录模块 */
.module-transactions .card-header,
.module-transactions .btn-primary {
    background: var(--transactions-gradient);
    border-color: var(--transactions-color);
}

.module-transactions .nav-link.active {
    background-color: var(--transactions-color) !important;
}

.module-transactions .badge-primary {
    background-color: var(--transactions-color);
}

/* 用户管理模块 */
.module-users .card-header,
.module-users .btn-primary {
    background: var(--users-gradient);
    border-color: var(--users-color);
}

.module-users .nav-link.active {
    background-color: var(--users-color) !important;
}

.module-users .badge-primary {
    background-color: var(--users-color);
}

/* 工具类 */
.text-primary-custom {
    color: var(--primary-color) !important;
}

.bg-primary-custom {
    background-color: var(--primary-color) !important;
}

.border-primary-custom {
    border-color: var(--primary-color) !important;
}

/* 模块色彩工具类 */
.text-products { color: var(--products-color) !important; }
.bg-products { background-color: var(--products-color) !important; }
.border-products { border-color: var(--products-color) !important; }

.text-inventory { color: var(--inventory-color) !important; }
.bg-inventory { background-color: var(--inventory-color) !important; }
.border-inventory { border-color: var(--inventory-color) !important; }

.text-transactions { color: var(--transactions-color) !important; }
.bg-transactions { background-color: var(--transactions-color) !important; }
.border-transactions { border-color: var(--transactions-color) !important; }

.text-users { color: var(--users-color) !important; }
.bg-users { background-color: var(--users-color) !important; }
.border-users { border-color: var(--users-color) !important; }

/* 响应式优化 */
@media (max-width: 768px) {
    .card {
        margin-bottom: 1rem;
        border-radius: var(--border-radius);
    }

    .card:hover {
        /* 移除transform动画效果 */
    }

    .table-responsive {
        border-radius: var(--border-radius);
        box-shadow: var(--shadow);
    }

    .table thead th {
        padding: 0.75rem 0.5rem;
        font-size: 0.8rem;
    }

    .table tbody td {
        padding: 0.75rem 0.5rem;
        font-size: 0.9rem;
    }

    /* 移动端表格卡片化 */
    .table-mobile-cards .table,
    .table-mobile-cards .table thead,
    .table-mobile-cards .table tbody,
    .table-mobile-cards .table th,
    .table-mobile-cards .table td,
    .table-mobile-cards .table tr {
        display: block;
    }

    .table-mobile-cards .table thead tr {
        position: absolute;
        top: -9999px;
        left: -9999px;
    }

    .table-mobile-cards .table tr {
        background: white;
        border-radius: var(--border-radius);
        box-shadow: var(--shadow-sm);
        margin-bottom: 1rem;
        padding: 1rem;
        position: relative;
    }

    .table-mobile-cards .table tr:hover {
        /* 移除移动端表格悬停动画效果 */
        background-color: #f8f9fa;
    }

    .table-mobile-cards .table td {
        border: none;
        padding: 0.5rem 0;
        text-align: right;
        position: relative;
        padding-left: 50%;
    }

    .table-mobile-cards .table td::before {
        content: attr(data-label);
        position: absolute;
        left: 0;
        width: 45%;
        padding-right: 10px;
        white-space: nowrap;
        text-align: left;
        font-weight: 600;
        color: var(--primary-color);
    }
}

@media (max-width: 576px) {
    .card-body {
        padding: 1rem;
    }

    .card-header {
        padding: 0.75rem 1rem;
    }

    .table thead th,
    .table tbody td {
        padding: 0.5rem 0.25rem;
        font-size: 0.85rem;
    }
}

/* 加载动画 */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 复选框和单选框优化 */
.form-check {
    margin-bottom: 0.75rem;
    padding-left: 2rem;
}

.form-check-input {
    width: 1.25rem;
    height: 1.25rem;
    margin-top: 0.125rem;
    margin-left: -2rem;
    border: 2px solid var(--medium-gray);
    border-radius: var(--border-radius);
    /* 移除transition动画效果 */
    cursor: pointer;
}

.form-check-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(44, 90, 160, 0.25);
}

.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='m6 10 3 3 6-6'/%3e%3c/svg%3e");
}

.form-check-input:hover:not(:checked) {
    border-color: var(--primary-light);
    transform: scale(1.05);
}

.form-check-label {
    cursor: pointer;
    font-weight: 500;
    color: var(--dark-gray);
    margin-left: 0.5rem;
}

.form-check-label:hover {
    color: var(--primary-color);
}

/* 下拉选择框优化 */
.form-select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 16px 12px;
    cursor: pointer;
}

.form-select:focus {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%232c5aa0' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3e%3c/svg%3e");
}

/* 输入组优化 */
.input-group {
    position: relative;
    display: flex;
    flex-wrap: wrap;
    align-items: stretch;
    width: 100%;
}

.input-group > .form-control,
.input-group > .form-select {
    position: relative;
    flex: 1 1 auto;
    width: 1%;
    min-width: 0;
}

.input-group-text {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    font-weight: 500;
    line-height: 1.5;
    color: var(--dark-gray);
    text-align: center;
    white-space: nowrap;
    background-color: var(--light-gray);
    border: 2px solid var(--medium-gray);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.input-group:focus-within .input-group-text {
    border-color: var(--primary-color);
    background-color: rgba(44, 90, 160, 0.1);
    color: var(--primary-color);
}

/* 文件上传优化 */
.form-control[type="file"] {
    padding: 0.5rem;
    cursor: pointer;
}

.form-control[type="file"]::-webkit-file-upload-button {
    padding: 0.5rem 1rem;
    margin: -0.5rem -1rem -0.5rem 0;
    margin-inline-end: 1rem;
    color: var(--dark-gray);
    background-color: var(--light-gray);
    border: none;
    border-radius: var(--border-radius);
    transition: var(--transition);
    cursor: pointer;
}

.form-control[type="file"]::-webkit-file-upload-button:hover {
    background-color: var(--primary-color);
    color: white;
}

/* 数据可视化样式 - 优化缩放支持 */
.chart-container {
    position: relative;
    height: 350px;
    min-height: 300px;
    max-height: 400px;
    margin: 1rem 0;
    background: white;
    border-radius: var(--border-radius-lg);
    padding: 1.25rem;
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
    overflow: hidden;
    width: 100%;
    box-sizing: border-box;
}

.chart-container:hover {
    box-shadow: var(--shadow);
    /* 移除transform动画效果 */
}

.chart-container canvas {
    max-height: 280px !important;
    width: 100% !important;
    height: auto !important;
    display: block !important;
    box-sizing: border-box !important;
    border-radius: var(--border-radius);
    /* 高DPI显示优化 */
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
}

.chart-title {
    font-size: clamp(1rem, 2.5vw, 1.25rem);
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 1rem;
    text-align: center;
    line-height: 1.2;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.chart-legend {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 1rem;
    flex-wrap: wrap;
}

.chart-legend-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: var(--dark-gray);
}

.chart-legend-color {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    flex-shrink: 0;
}

/* 统计卡片增强 */
.stats-card-enhanced {
    background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
    border: none;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
    overflow: hidden;
    position: relative;
}

.stats-card-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--primary-gradient);
    transition: width 0.3s ease;
}

.stats-card-enhanced:hover::before {
    width: 8px;
}

.stats-card-enhanced:hover {
    /* 移除transform和box-shadow动画效果 */
}

.stats-card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.stats-card-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    background: var(--primary-gradient);
    box-shadow: var(--shadow-sm);
}

.stats-card-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin: 0.5rem 0;
}

.stats-card-label {
    font-size: 0.9rem;
    color: var(--secondary-color);
    font-weight: 500;
}

.stats-card-trend {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 0.5rem;
    font-size: 0.85rem;
}

.stats-card-trend.positive {
    color: var(--success-color);
}

.stats-card-trend.negative {
    color: var(--danger-color);
}

.stats-card-trend.neutral {
    color: var(--secondary-color);
}

/* 进度条样式 */
.progress-enhanced {
    height: 8px;
    background-color: var(--light-gray);
    border-radius: 4px;
    overflow: hidden;
    margin-top: 1rem;
    position: relative;
}

.progress-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
    width: 100%;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* 页面加载动画 */
.page-loader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity 0.5s ease, visibility 0.5s ease;
}

.page-loader.hidden {
    opacity: 0;
    visibility: hidden;
}

.loader-content {
    text-align: center;
    color: white;
}

.loader-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

.loader-text {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.loader-subtext {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* 页面内容淡入动画 */
.page-content {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.6s ease;
}

.page-content.loaded {
    opacity: 1;
    transform: translateY(0);
}

/* 元素依次出现动画 */
.animate-in {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.animate-in.visible {
    opacity: 1;
    transform: translateY(0);
}

.animate-in:nth-child(1) { transition-delay: 0.1s; }
.animate-in:nth-child(2) { transition-delay: 0.2s; }
.animate-in:nth-child(3) { transition-delay: 0.3s; }
.animate-in:nth-child(4) { transition-delay: 0.4s; }
.animate-in:nth-child(5) { transition-delay: 0.5s; }
.animate-in:nth-child(6) { transition-delay: 0.6s; }

/* 悬浮动画增强 - 已移除 */

/* hover-scale 动画已移除 */

/* hover-rotate 动画已移除 */

/* 脉冲动画已移除 */

/* 摇摆动画已移除 */

/* 弹跳动画已移除 */

/* 模态框动画 */
.modal.fade .modal-dialog {
    transform: scale(0.8) translateY(-50px);
    transition: all 0.3s ease;
}

.modal.show .modal-dialog {
    transform: scale(1) translateY(0);
}

.modal-content {
    border: none;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
}

.modal-header {
    background: var(--primary-gradient);
    color: white;
    border: none;
    padding: 1.5rem;
}

.modal-header .btn-close {
    filter: invert(1);
    opacity: 0.8;
    transition: var(--transition);
}

.modal-header .btn-close:hover {
    opacity: 1;
    transform: scale(1.1);
}

.modal-body {
    padding: 2rem;
}

.modal-footer {
    border: none;
    padding: 1rem 2rem 2rem;
    background-color: var(--light-gray);
}

/* 下拉菜单动画增强 */
.dropdown-menu {
    border: none;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    padding: 0.5rem;
    margin-top: 0.5rem;
    transform: translateY(-10px);
    opacity: 0;
    visibility: hidden;
    transition: all 0.2s cubic-bezier(0.16, 1, 0.3, 1);
    pointer-events: none;
    z-index: 1050;
}

.dropdown-menu.show {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
    pointer-events: auto;
}

/* 特殊处理：侧边栏用户菜单向上展开 */
.sidebar-footer .dropdown-menu {
    transform: translateY(10px);
    margin-top: 0;
    margin-bottom: 0.5rem;
}

.sidebar-footer .dropdown-menu.show {
    transform: translateY(0);
}

/* 用户菜单项优化 */
.sidebar-footer .dropdown-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    border-radius: var(--border-radius);
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
    color: var(--text-color);
    background-color: transparent;
    border: none;
    text-decoration: none;
    margin: 0 0.25rem;
}

.sidebar-footer .dropdown-item:hover {
    background-color: rgba(44, 90, 160, 0.08);
    color: var(--primary-color);
    transform: translateX(2px);
    text-decoration: none;
}

.sidebar-footer .dropdown-item:focus {
    background-color: rgba(44, 90, 160, 0.12);
    color: var(--primary-color);
    outline: none;
    box-shadow: none;
    text-decoration: none;
}

.sidebar-footer .dropdown-item:active {
    background-color: rgba(44, 90, 160, 0.15);
    color: var(--primary-color);
    text-decoration: none;
}

.sidebar-footer .dropdown-item i {
    width: 1.25rem;
    text-align: center;
    flex-shrink: 0;
}

.sidebar-footer .dropdown-header {
    padding: 1rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
    margin-bottom: 0.5rem;
    background-color: rgba(248, 249, 250, 0.95);
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    color: #212529;
    font-weight: 600;
    line-height: 1.4;
}

/* 修复用户菜单头部文字可读性 */
.sidebar-footer .dropdown-header .text-muted {
    color: #343a40 !important;
    font-size: 0.875rem;
    font-weight: 500;
    margin-top: 0.25rem;
    opacity: 1;
}

/* 强制覆盖所有可能的灰色文字样式 */
.sidebar-footer .user-menu .text-muted,
.sidebar-footer .user-menu .text-secondary,
.sidebar-footer .dropdown-header small,
.sidebar-footer .dropdown-header .small,
.sidebar-footer .dropdown-header small.text-muted,
.sidebar-footer .dropdown-header .text-muted.d-block {
    color: #343a40 !important;
    opacity: 1 !important;
    font-weight: 500 !important;
}

/* 特别针对邮箱地址的样式 */
.sidebar-footer .user-menu .dropdown-header small.text-muted.d-block {
    color: #495057 !important;
    font-size: 0.8rem !important;
    margin-top: 0.25rem !important;
    font-weight: 400 !important;
}

.sidebar-footer .dropdown-header i {
    color: var(--primary-color);
    margin-right: 0.5rem;
    font-size: 1.1rem;
}

/* 确保用户名清晰可见 */
.sidebar-footer .dropdown-header {
    text-shadow: none;
}

.sidebar-footer .dropdown-header > i {
    vertical-align: middle;
}

.sidebar-footer .dropdown-divider {
    margin: 0.5rem 0;
    border-color: rgba(0, 0, 0, 0.08);
}

/* 危险操作样式（如退出登录） */
.sidebar-footer .dropdown-item.text-danger {
    color: #dc3545;
}

.sidebar-footer .dropdown-item.text-danger:hover {
    background-color: rgba(220, 53, 69, 0.1);
    color: #dc3545;
}

.sidebar-footer .dropdown-item.text-danger:focus {
    background-color: rgba(220, 53, 69, 0.15);
    color: #dc3545;
    box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.3);
}

/* 防止菜单闪烁 */
.sidebar-footer .user-menu {
    will-change: transform, opacity;
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
}

/* 重置Bootstrap默认样式 */
.sidebar-footer .dropdown-item.active,
.sidebar-footer .dropdown-item:active {
    background-color: rgba(44, 90, 160, 0.15) !important;
    color: var(--primary-color) !important;
}

.sidebar-footer .dropdown-menu .dropdown-item {
    white-space: nowrap;
    border: none;
    background: none;
}

/* 确保图标和文字对齐 */
.sidebar-footer .dropdown-item i {
    width: 1.25rem;
    text-align: center;
    flex-shrink: 0;
    opacity: 0.7;
}

.sidebar-footer .dropdown-item:hover i {
    opacity: 1;
}

/* 响应式优化 */
@media (max-width: 768px) {
    .sidebar-footer .user-menu {
        min-width: 200px;
        left: -50px;
        right: auto;
    }
}

.dropdown-item {
    border-radius: var(--border-radius);
    margin: 0.25rem 0;
    padding: 0.75rem 1rem;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.dropdown-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(44, 90, 160, 0.1), transparent);
    transition: left 0.5s;
}

.dropdown-item:hover::before {
    left: 100%;
}

.dropdown-item:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateX(5px);
}

/* 工具提示动画 */
.tooltip {
    opacity: 0;
    transform: scale(0.8);
    transition: all 0.2s ease;
}

.tooltip.show {
    opacity: 1;
    transform: scale(1);
}

.tooltip-inner {
    background-color: var(--dark-gray);
    border-radius: var(--border-radius);
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    box-shadow: var(--shadow);
}

/* 警告框动画 */
.alert {
    border: none;
    border-radius: var(--border-radius-lg);
    padding: 1rem 1.5rem;
    margin-bottom: 1rem;
    position: relative;
    overflow: hidden;
    transform: translateX(-100%);
    animation: slideInRight 0.5s ease forwards;
}

@keyframes slideInRight {
    to { transform: translateX(0); }
}

.alert::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background-color: currentColor;
}

.alert-dismissible .btn-close {
    transition: var(--transition);
}

.alert-dismissible .btn-close:hover {
    transform: scale(1.1);
}

/* 徽章动画 */
.badge {
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s;
}

.badge:hover::before {
    left: 100%;
}

.badge:hover {
    transform: scale(1.1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* AJAX功能样式 */
/* 网络状态指示器 */
.network-indicator {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--primary-gradient);
    color: white;
    padding: 0.75rem 1rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    z-index: 1050;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    font-weight: 500;
    animation: slideInRight 0.3s ease;
}

.network-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* 搜索相关样式 */
.search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid var(--medium-gray);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    z-index: 1000;
    max-height: 300px;
    overflow-y: auto;
    margin-top: 2px;
}

/* 搜索按钮样式优化 */
.btn[type="submit"] {
    white-space: nowrap;
    min-width: 44px; /* 确保按钮有最小宽度 */
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 搜索表单样式 */
.d-flex form,
form.d-flex {
    flex-wrap: nowrap; /* 防止换行 */
    align-items: stretch;
}

.d-flex .form-control {
    flex: 1;
    min-width: 0; /* 允许输入框收缩 */
}

.d-flex .btn {
    flex-shrink: 0; /* 防止按钮收缩 */
}

.search-suggestion-item {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid var(--light-gray);
    cursor: pointer;
    transition: var(--transition);
}

.search-suggestion-item:last-child {
    border-bottom: none;
}

.search-suggestion-item:hover {
    background-color: var(--light-gray);
    transform: translateX(5px);
}

.suggestion-title {
    font-weight: 500;
    color: var(--dark-gray);
    margin-bottom: 0.25rem;
}

.suggestion-meta {
    font-size: 0.85rem;
    color: var(--secondary-color);
}

/* 异步表格样式 */
.async-table {
    position: relative;
}

.async-table.loading {
    opacity: 0.7;
    pointer-events: none;
}

.async-table .loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid var(--light-gray);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* 异步表单样式 */
.async-form {
    position: relative;
}

.async-form.submitting {
    opacity: 0.8;
}

.async-form .form-control.is-invalid {
    animation: shake 0.5s ease-in-out;
}

/* 实时状态更新 */
.status-indicator.updating {
    animation: pulse 1s infinite;
}

.status-badge {
    position: relative;
    overflow: hidden;
}

.status-badge.updating::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: shimmer 1.5s infinite;
}

/* 分页控件增强 */
.pagination-async .page-link {
    border-radius: var(--border-radius);
    margin: 0 2px;
}

.pagination-async .page-link:hover {
    /* 移除transform和box-shadow动画效果 */
}

.pagination-async .page-item.active .page-link {
    background: var(--primary-gradient);
    border-color: var(--primary-color);
}

/* 加载骨架屏 */
.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

.skeleton-text {
    height: 1rem;
    border-radius: 4px;
    margin-bottom: 0.5rem;
}

.skeleton-text.short {
    width: 60%;
}

.skeleton-text.medium {
    width: 80%;
}

.skeleton-text.long {
    width: 100%;
}

/* 响应式AJAX组件 */
@media (max-width: 768px) {
    .network-indicator {
        top: 10px;
        right: 10px;
        padding: 0.5rem 0.75rem;
        font-size: 0.8rem;
    }

    .search-suggestions {
        max-height: 200px;
    }

    .search-suggestion-item {
        padding: 0.5rem 0.75rem;
    }
}

.progress-bar-enhanced {
    height: 100%;
    background: var(--primary-gradient);
    border-radius: 4px;
    transition: width 1s ease-in-out;
    position: relative;
    overflow: hidden;
}

.progress-bar-enhanced.success {
    background: linear-gradient(90deg, var(--success-color), var(--success-light));
}

.progress-bar-enhanced.warning {
    background: linear-gradient(90deg, var(--warning-color), var(--warning-light));
}

.progress-bar-enhanced.danger {
    background: linear-gradient(90deg, var(--danger-color), var(--danger-light));
}

/* 库存状态指示器 */
.stock-indicator {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
}

.stock-indicator.high {
    background-color: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
    border: 1px solid rgba(40, 167, 69, 0.3);
}

.stock-indicator.normal {
    background-color: rgba(23, 162, 184, 0.1);
    color: var(--info-color);
    border: 1px solid rgba(23, 162, 184, 0.3);
}

.stock-indicator.low {
    background-color: rgba(255, 193, 7, 0.1);
    color: var(--warning-color);
    border: 1px solid rgba(255, 193, 7, 0.3);
}

.stock-indicator.critical {
    background-color: rgba(220, 53, 69, 0.1);
    color: var(--danger-color);
    border: 1px solid rgba(220, 53, 69, 0.3);
}

.stock-indicator::before {
    content: '';
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: currentColor;
}

/* 响应式图表 */
@media (max-width: 768px) {
    .chart-container {
        height: 250px;
        padding: 0.75rem;
    }

    .chart-legend {
        gap: 0.5rem;
    }

    .chart-legend-item {
        font-size: 0.8rem;
    }

    .stats-card-value {
        font-size: 1.5rem;
    }

    .stats-card-icon {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }
}

/* 自定义徽章样式 */
.badge-custom {
    background-color: var(--primary-color);
    color: white;
    padding: 0.375rem 0.75rem;
    border-radius: var(--border-radius);
    font-size: 0.875rem;
}

.badge {
    font-weight: 500;
    border-radius: var(--border-radius);
}

.badge.bg-success {
    background-color: var(--success-color) !important;
}

.badge.bg-warning {
    background-color: var(--warning-color) !important;
    color: #212529 !important;
}

.badge.bg-danger {
    background-color: var(--danger-color) !important;
}

.badge.bg-info {
    background-color: var(--info-color) !important;
}

.badge.bg-secondary {
    background-color: var(--secondary-color) !important;
}

/* 状态指示器 */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.status-indicator.status-active {
    background-color: var(--success-color);
    box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.3);
}

.status-indicator.status-inactive {
    background-color: var(--secondary-color);
}

.status-indicator.status-warning {
    background-color: var(--warning-color);
    box-shadow: 0 0 0 2px rgba(255, 193, 7, 0.3);
}

.status-indicator.status-danger {
    background-color: var(--danger-color);
    box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.3);
}

/* 统计卡片样式 */
.stats-card {
    background: linear-gradient(135deg, #fff, var(--light-gray));
    border-left: 4px solid var(--primary-color);
    transition: var(--transition);
}

.stats-card:hover {
    /* 移除transform和box-shadow动画效果 */
}

.stats-card .display-6 {
    color: var(--primary-color);
}

.stats-card.stats-products {
    border-left-color: var(--products-color);
}

.stats-card.stats-products .display-6 {
    color: var(--products-color);
}

.stats-card.stats-inventory {
    border-left-color: var(--inventory-color);
}

.stats-card.stats-inventory .display-6 {
    color: var(--inventory-color);
}

.stats-card.stats-transactions {
    border-left-color: var(--transactions-color);
}

.stats-card.stats-transactions .display-6 {
    color: var(--transactions-color);
}

.stats-card.stats-users {
    border-left-color: var(--users-color);
}

.stats-card.stats-users .display-6 {
    color: var(--users-color);
}

/* ========================================
   商品选择组件样式
   ======================================== */

/* Select2 组件样式优化 */
.select2-container--bootstrap-5 .select2-selection {
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    min-height: calc(1.5em + 0.75rem + 2px);
}

.select2-container--bootstrap-5 .select2-selection--single {
    height: calc(1.5em + 0.75rem + 2px);
    padding: 0.375rem 0.75rem;
}

.select2-container--bootstrap-5 .select2-selection--single .select2-selection__rendered {
    color: #495057;
    line-height: 1.5;
    padding-left: 0;
    padding-right: 20px;
}

.select2-container--bootstrap-5 .select2-selection--single .select2-selection__arrow {
    height: calc(1.5em + 0.75rem);
    right: 0.75rem;
}

/* 商品选择框的特殊样式 */
.product-search.select2-hidden-accessible + .select2-container {
    width: 100% !important;
}

.select2-container--bootstrap-5 .select2-dropdown {
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.select2-container--bootstrap-5 .select2-results__option {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
}

.select2-container--bootstrap-5 .select2-results__option--highlighted {
    background-color: var(--primary-color);
    color: white;
}

/* 商品选择结果的样式 */
.select2-container--bootstrap-5 .select2-results__option .product-name {
    font-weight: 500;
    color: #212529;
}

.select2-container--bootstrap-5 .select2-results__option .product-sku {
    font-size: 0.75rem;
    color: #6c757d;
    margin-left: 0.5rem;
}

/* 搜索框样式 */
.select2-container--bootstrap-5 .select2-search--dropdown .select2-search__field {
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}

/* 加载状态样式 */
.select2-container--bootstrap-5 .select2-results__message {
    color: #6c757d;
    font-size: 0.875rem;
    padding: 0.75rem;
    text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .select2-container--bootstrap-5 .select2-selection--single {
        font-size: 0.875rem;
    }

    .select2-container--bootstrap-5 .select2-results__option {
        padding: 0.75rem;
        font-size: 0.875rem;
    }
}
