from django.contrib import admin
from django.contrib.admin.models import LogEntry, DELETION
from django.contrib.contenttypes.models import ContentType
from .models import Product

# Register your models here.

@admin.register(Product)
class ProductAdmin(admin.ModelAdmin):
    list_display = ['name', 'sku', 'price', 'created_at']
    list_filter = ['created_at']
    search_fields = ['name', 'sku', 'description']
    readonly_fields = ['created_at', 'updated_at']

    def delete_model(self, request, obj):
        """重写删除方法，记录审计日志"""
        from users.services import AuditService
        from users.signals import _mark_admin_action_logged
        import logging

        logger = logging.getLogger(__name__)
        logger.info(f"Admin删除商品: {obj.name}, 用户: {request.user.username}")

        # 标记admin操作已记录，避免信号重复记录
        _mark_admin_action_logged(obj, 'delete')

        # 手动记录审计日志，传入当前用户
        audit_log = AuditService.log_delete(
            instance=obj,
            user=request.user,
            request=request,
            extra_data={
                'model': 'Product',
                'operation': 'admin_delete_product',
                'admin_action': True
            }
        )

        logger.info(f"审计日志创建结果: {audit_log}, 用户: {audit_log.user if audit_log else 'None'}")

        # 执行实际删除
        super().delete_model(request, obj)

    def delete_queryset(self, request, queryset):
        """重写批量删除方法，记录审计日志"""
        from users.services import AuditService
        from users.signals import _mark_admin_action_logged

        # 为每个对象记录审计日志
        for obj in queryset:
            # 标记admin操作已记录，避免信号重复记录
            _mark_admin_action_logged(obj, 'delete')

            AuditService.log_delete(
                instance=obj,
                user=request.user,
                request=request,
                extra_data={
                    'model': 'Product',
                    'operation': 'admin_batch_delete_product',
                    'admin_action': True
                }
            )

        # 执行实际批量删除
        super().delete_queryset(request, queryset)
