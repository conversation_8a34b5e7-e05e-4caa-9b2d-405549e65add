# Generated by Django 4.1.2 on 2025-07-30 04:33

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("contenttypes", "0002_remove_content_type_name"),
        ("users", "0002_alter_userprofile_role"),
    ]

    operations = [
        migrations.CreateModel(
            name="AuditLog",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "timestamp",
                    models.DateTimeField(auto_now_add=True, verbose_name="操作时间"),
                ),
                (
                    "action",
                    models.CharField(
                        choices=[
                            ("CREATE", "创建"),
                            ("UPDATE", "更新"),
                            ("DELETE", "删除"),
                            ("VIEW", "查看"),
                        ],
                        max_length=10,
                        verbose_name="操作类型",
                    ),
                ),
                ("object_id", models.PositiveIntegerField(verbose_name="对象ID")),
                (
                    "object_repr",
                    models.CharField(max_length=200, verbose_name="对象描述"),
                ),
                (
                    "changes",
                    models.JSONField(blank=True, default=dict, verbose_name="变更详情"),
                ),
                (
                    "ip_address",
                    models.GenericIPAddressField(
                        blank=True, null=True, verbose_name="IP地址"
                    ),
                ),
                ("user_agent", models.TextField(blank=True, verbose_name="用户代理")),
                (
                    "extra_data",
                    models.JSONField(blank=True, default=dict, verbose_name="额外信息"),
                ),
                (
                    "content_type",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="contenttypes.contenttype",
                        verbose_name="对象类型",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="操作用户",
                    ),
                ),
            ],
            options={
                "verbose_name": "操作审计日志",
                "verbose_name_plural": "操作审计日志",
                "ordering": ["-timestamp"],
            },
        ),
        migrations.AddIndex(
            model_name="auditlog",
            index=models.Index(
                fields=["user", "timestamp"], name="users_audit_user_id_4b4ca5_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="auditlog",
            index=models.Index(
                fields=["content_type", "object_id"],
                name="users_audit_content_f4c6fb_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="auditlog",
            index=models.Index(
                fields=["action", "timestamp"], name="users_audit_action_962101_idx"
            ),
        ),
    ]
