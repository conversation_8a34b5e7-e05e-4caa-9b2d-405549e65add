<!-- 库存表格内容 -->
{% for inventory in inventories %}
    <tr class="inventory-row{% if inventory.is_low_stock %} table-warning{% endif %}"
        data-inventory-id="{{ inventory.pk }}"
        data-product-name="{{ inventory.product.name }}"
        {% if perms.inventory.change_inventory %}data-editable="true"{% endif %}
        style="cursor: {% if perms.inventory.change_inventory %}pointer{% else %}default{% endif %};">
        {% if perms.inventory.delete_inventory %}
        <td class="checkbox-column">
            <div class="form-check">
                <input class="form-check-input inventory-checkbox"
                       type="checkbox"
                       value="{{ inventory.pk }}"
                       data-name="{{ inventory.product.name }}"
                       data-delete-url="{% url 'inventory:delete' inventory.pk %}">
            </div>
        </td>
        {% endif %}
        <td>
            <div class="d-flex align-items-center">
                <div class="me-3">
                    <div class="bg-light rounded p-2">
                        <i class="bi bi-box-seam text-primary fs-4"></i>
                    </div>
                </div>
                <div>
                    <div class="fw-bold">{{ inventory.product.name|default:"未知商品" }}</div>
                    <small class="text-muted">编号：{{ inventory.product.sku|default:"无编号" }}</small>
                    {% if inventory.product.description %}
                        <br><small class="text-muted">{{ inventory.product.description|truncatechars:50 }}</small>
                    {% endif %}
                </div>
            </div>
        </td>
            <td>
                <div class="inventory-quantity-container" data-inventory-id="{{ inventory.pk }}">
                    <div class="quantity-display">
                        <span class="fs-5 fw-bold quantity-value {% if inventory.quantity == 0 %}text-danger{% elif inventory.is_low_stock %}text-warning{% else %}text-success{% endif %}"
                              data-original-quantity="{{ inventory.quantity }}">
                            {{ inventory.quantity }}
                        </span>
                        {% if perms.inventory.change_inventory %}
                        <button type="button" class="btn btn-sm btn-link p-0 ms-1 edit-quantity-btn"
                                title="点击编辑库存">
                            <i class="bi bi-pencil-square text-primary"></i>
                        </button>
                        {% endif %}
                    </div>
                    <div class="quantity-edit" style="display: none;">
                        <div class="input-group input-group-sm">
                            <input type="number" class="form-control quantity-input"
                                   value="{{ inventory.quantity }}" min="0" max="999999">
                            <button type="button" class="btn btn-success btn-sm save-quantity-btn" title="保存">
                                <i class="bi bi-check"></i>
                            </button>
                            <button type="button" class="btn btn-secondary btn-sm cancel-quantity-btn" title="取消">
                                <i class="bi bi-x"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </td>
            <td>
                {% if inventory.quantity == 0 %}
                    <span class="badge bg-danger">缺货</span>
                {% elif inventory.quantity <= 10 %}
                    <span class="badge bg-warning">库存不足</span>
                {% else %}
                    <span class="badge bg-success">库存充足</span>
                {% endif %}
            </td>
            <td>
                <div>
                    <span class="fw-bold">¥{{ inventory.total_value|floatformat:2 }}</span>
                    <br><small class="text-muted">单价：¥{{ inventory.product.price|floatformat:2 }}</small>
                </div>
            </td>
            <td>
                <div>{{ inventory.updated_at|date:"m-d H:i" }}</div>
            </td>
            <td>
                <div>{{ inventory.updated_by.username|default:"系统" }}</div>
            </td>
        </tr>
{% empty %}
    <tr>
        <td colspan="6" class="text-center py-4">
            <div class="text-muted">
                <i class="bi bi-inbox fs-1"></i>
                <p class="mt-2">暂无库存数据</p>
            </div>
        </td>
    </tr>
{% endfor %}
