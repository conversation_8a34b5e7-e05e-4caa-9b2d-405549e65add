from django.test import TestCase
from django.contrib.auth.models import User
from .models import Product

# Create your tests here.

class ProductSKUGenerationTestCase(TestCase):
    """测试商品SKU自动生成功能"""

    def setUp(self):
        """测试前准备"""
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )

    def test_sku_auto_generation(self):
        """测试SKU自动生成"""
        product = Product.objects.create(
            name='苹果手机',
            description='测试商品',
            price=5999.00,
            created_by=self.user
        )

        # 验证SKU已自动生成
        self.assertIsNotNone(product.sku)
        self.assertTrue(len(product.sku) >= 4)  # 至少3个字母+1个数字

    def test_sku_uniqueness(self):
        """测试SKU唯一性"""
        # 创建两个相同名称的商品
        product1 = Product.objects.create(
            name='苹果手机',
            description='测试商品1',
            price=5999.00,
            created_by=self.user
        )

        product2 = Product.objects.create(
            name='苹果手机',
            description='测试商品2',
            price=6999.00,
            created_by=self.user
        )

        # 验证SKU不同
        self.assertNotEqual(product1.sku, product2.sku)

    def test_sku_format(self):
        """测试SKU格式"""
        product = Product.objects.create(
            name='蓝牙耳机',
            description='测试商品',
            price=299.00,
            created_by=self.user
        )

        # 验证SKU格式：大写字母+数字
        import re
        self.assertTrue(re.match(r'^[A-Z]+\d+$', product.sku))

    def test_sku_with_special_characters(self):
        """测试包含特殊字符的商品名称"""
        product = Product.objects.create(
            name='iPhone 15 Pro Max (256GB)',
            description='测试商品',
            price=9999.00,
            created_by=self.user
        )

        # 验证SKU已生成且格式正确
        self.assertIsNotNone(product.sku)
        import re
        self.assertTrue(re.match(r'^[A-Z]+\d+$', product.sku))

    def test_existing_sku_preserved(self):
        """测试现有SKU不被覆盖"""
        product = Product.objects.create(
            name='测试商品',
            sku='CUSTOM001',
            description='测试商品',
            price=100.00,
            created_by=self.user
        )

        # 验证自定义SKU被保留
        self.assertEqual(product.sku, 'CUSTOM001')
