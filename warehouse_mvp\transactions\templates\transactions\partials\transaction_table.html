<!-- 进出库记录表格 - 使用统一表格样式 -->
<div class="unified-table-container">
    <table class="unified-table table-theme-primary">
        <thead>
            <tr>
                <th>商品信息</th>
                <th>操作类型</th>
                <th>数量变化</th>
                <th>操作前库存</th>
                <th>操作后库存</th>
                <th>操作时间</th>
                <th>操作人</th>
                <th>备注</th>
            </tr>
        </thead>
        <tbody>
            {% for transaction in transactions %}
            <tr>
                <td>
                    <div class="d-flex align-items-center">
                        <div class="me-3">
                            <div class="bg-light rounded p-2">
                                <i class="bi bi-box text-info fs-4"></i>
                            </div>
                        </div>
                        <div>
                            <div class="fw-bold">{{ transaction.product.name }}</div>
                            <small class="text-muted">编号：{{ transaction.product.sku }}</small>
                        </div>
                    </div>
                </td>
                <td>
                    {% if transaction.transaction_type == 'IN' %}
                        <span class="badge bg-success">
                            <i class="bi bi-arrow-down"></i> 入库
                        </span>
                    {% else %}
                        <span class="badge bg-danger">
                            <i class="bi bi-arrow-up"></i> 出库
                        </span>
                    {% endif %}
                </td>
                <td>
                    {% if transaction.transaction_type == 'IN' %}
                        <span class="text-success fw-bold">+{{ transaction.quantity }}</span>
                    {% else %}
                        <span class="text-danger fw-bold">-{{ transaction.quantity }}</span>
                    {% endif %}
                </td>
                <td>{{ transaction.stock_before }}</td>
                <td>{{ transaction.stock_after }}</td>
                <td>
                    <div>{{ transaction.created_at|date:"Y-m-d H:i" }}</div>
                </td>
                <td>
                    <div>{{ transaction.operator.username|default:"系统" }}</div>
                </td>
                <td>
                    <div>{{ transaction.notes|default:"-"|truncatechars:30 }}</div>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="8" class="text-center py-5">
                    <i class="bi bi-arrow-left-right display-1 text-muted"></i>
                    <h4 class="text-muted mt-3">暂无进出库记录</h4>
                    <p class="text-muted">点击上方"新增记录"按钮开始记录进出库操作</p>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
