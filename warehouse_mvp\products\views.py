from django.shortcuts import render, get_object_or_404
from django.urls import reverse_lazy
from django.views.generic import ListView, CreateView, UpdateView, DeleteView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib import messages
from django.http import JsonResponse
from django.template.loader import render_to_string
from django.db.models import Q
from django.core.paginator import Paginator
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.contrib.auth.decorators import login_required
from django.db import transaction
from django.utils.decorators import method_decorator
from decimal import Decimal, InvalidOperation
import openpyxl
import io
from .models import Product
from .forms import ProductForm
from users.decorators import PermissionRequiredMixin

# Create your views here.

class ProductListView(PermissionRequiredMixin, ListView):
    """商品列表视图"""
    model = Product
    template_name = 'products/list.html'
    context_object_name = 'products'
    paginate_by = 10
    permission_app = 'products'
    permission_action = 'view'

    def get_queryset(self):
        queryset = Product.objects.all()
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(name__icontains=search)
        return queryset

class ProductCreateView(PermissionRequiredMixin, CreateView):
    """商品创建视图"""
    model = Product
    form_class = ProductForm
    permission_app = 'products'
    permission_action = 'add'
    template_name = 'products/form.html'
    success_url = reverse_lazy('products:list')

    def get(self, request, *args, **kwargs):
        """处理GET请求"""
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            # AJAX请求，返回表单HTML
            form = self.get_form()
            form_html = render_to_string('products/form_modal.html', {
                'form': form,
                'object': None,
            }, request=request)

            return JsonResponse({
                'success': True,
                'form_html': form_html
            })

        # 普通请求，返回完整页面
        return super().get(request, *args, **kwargs)

    def form_valid(self, form):
        # 设置创建人
        form.instance.created_by = self.request.user

        if self.request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            # AJAX请求处理 - 只返回JSON，不设置Django messages
            self.object = form.save()
            return JsonResponse({
                'success': True,
                'message': '商品创建成功！',
                'redirect_url': str(self.success_url)
            })

        # 普通请求处理 - 只设置Django messages
        messages.success(self.request, '商品创建成功！')
        return super().form_valid(form)

    def form_invalid(self, form):
        if self.request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            # AJAX请求，返回带错误的表单HTML
            form_html = render_to_string('products/form_modal.html', {
                'form': form,
                'object': None,
            }, request=self.request)

            return JsonResponse({
                'success': False,
                'form_html': form_html,
                'errors': form.errors,
                'error': '请检查表单中的错误信息'
            })

        # 普通请求处理
        return super().form_invalid(form)

class ProductUpdateView(PermissionRequiredMixin, UpdateView):
    """商品更新视图"""
    model = Product
    form_class = ProductForm
    template_name = 'products/form.html'
    success_url = reverse_lazy('products:list')
    permission_app = 'products'
    permission_action = 'change'

    def get(self, request, *args, **kwargs):
        """处理GET请求"""
        self.object = self.get_object()

        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            # AJAX请求，返回表单HTML
            form = self.get_form()
            form_html = render_to_string('products/form_modal.html', {
                'form': form,
                'object': self.object,
            }, request=request)

            return JsonResponse({
                'success': True,
                'form_html': form_html
            })

        # 普通请求，返回完整页面
        return super().get(request, *args, **kwargs)

    def post(self, request, *args, **kwargs):
        """处理POST请求"""
        self.object = self.get_object()
        form = self.get_form()

        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            # AJAX请求处理 - 只返回JSON消息，不设置Django messages避免重复
            if form.is_valid():
                # 设置更新人
                form.instance.updated_by = self.request.user
                self.object = form.save()
                return JsonResponse({
                    'success': True,
                    'message': '商品更新成功！',
                    'redirect_url': str(self.success_url)
                })
            else:
                # 表单验证失败，返回带错误的表单HTML
                form_html = render_to_string('products/form_modal.html', {
                    'form': form,
                    'object': self.object,
                }, request=request)

                return JsonResponse({
                    'success': False,
                    'form_html': form_html,
                    'errors': form.errors,
                    'error': '请检查表单中的错误信息'
                })

        # 普通请求处理 - 只设置Django messages，用于页面刷新后显示
        if form.is_valid():
            # 设置更新人
            form.instance.updated_by = self.request.user
            messages.success(self.request, '商品更新成功！')
            return super().form_valid(form)
        else:
            return super().form_invalid(form)

class ProductDeleteView(PermissionRequiredMixin, DeleteView):
    """商品删除视图"""
    model = Product
    template_name = 'products/confirm_delete.html'
    success_url = reverse_lazy('products:list')
    permission_app = 'products'
    permission_action = 'delete'

    def delete(self, request, *args, **kwargs):
        """处理删除请求"""
        from users.services import AuditService
        from users.signals import _mark_admin_action_logged

        product = self.get_object()
        product_name = product.name

        # 标记操作已记录，避免信号重复记录
        _mark_admin_action_logged(product, 'delete')

        # 手动记录审计日志，传入当前用户
        audit_log = AuditService.log_delete(
            instance=product,
            user=request.user,
            request=request,
            extra_data={
                'model': 'Product',
                'operation': 'frontend_delete_product',
                'frontend_action': True
            }
        )

        # 执行删除
        response = super().delete(request, *args, **kwargs)

        # 如果是AJAX请求，返回JSON响应而不是重定向
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest' or 'application/json' in request.headers.get('Accept', ''):
            from django.http import JsonResponse
            return JsonResponse({
                'success': True,
                'message': f'商品「{product_name}」删除成功！',
                'refresh_charts': True  # 标记需要刷新图表
            })

        # 非AJAX请求，添加成功消息并重定向
        messages.success(self.request, f'商品「{product_name}」删除成功！')
        return response


@login_required
@require_http_methods(["GET"])
def ajax_search(request):
    """AJAX搜索商品"""
    try:
        # 获取搜索参数
        search_query = request.GET.get('search', '').strip()
        page = int(request.GET.get('page', 1))

        # 构建查询
        queryset = Product.objects.all()

        # 搜索过滤
        if search_query:
            queryset = queryset.filter(
                Q(name__icontains=search_query) |
                Q(sku__icontains=search_query) |
                Q(description__icontains=search_query)
            )

        # 排序
        queryset = queryset.order_by('-created_at')

        # 分页
        paginator = Paginator(queryset, 10)  # 每页10条
        page_obj = paginator.get_page(page)

        # 渲染表格内容
        # 修复权限传递问题：使用与主页面一致的权限对象格式
        from django.contrib.auth.context_processors import PermWrapper
        perms = PermWrapper(request.user) if request.user.is_authenticated else {}

        table_html = render_to_string('products/partials/product_table.html', {
            'products': page_obj,
            'page_obj': page_obj,
            'request': request,
            'perms': perms,
        })

        # 渲染分页
        pagination_html = render_to_string('products/partials/pagination.html', {
            'page_obj': page_obj,
            'request': request,
        })

        return JsonResponse({
            'success': True,
            'table_html': table_html,
            'pagination_html': pagination_html,
            'total_count': paginator.count,
            'current_page': page_obj.number,
            'total_pages': paginator.num_pages,
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': f'搜索失败：{str(e)}'
        })



@method_decorator(login_required, name='dispatch')
@method_decorator(csrf_exempt, name='dispatch')
class BatchUploadView(PermissionRequiredMixin, CreateView):
    """商品批量上传视图"""
    model = Product
    permission_app = 'products'
    permission_action = 'add'

    def post(self, request, *args, **kwargs):
        """处理批量上传请求"""
        # 检查是否是预览请求
        action = kwargs.get('action', 'upload')
        if action == 'preview':
            return self.preview_duplicates(request)

        try:
            # 检查是否有上传的文件
            if 'file' not in request.FILES:
                return JsonResponse({
                    'success': False,
                    'error': '请选择要上传的Excel文件'
                })

            uploaded_file = request.FILES['file']

            # 验证文件格式
            if not self.validate_file_format(uploaded_file):
                return JsonResponse({
                    'success': False,
                    'error': '文件格式不正确，请上传.xlsx或.xls格式的Excel文件'
                })

            # 验证文件大小（10MB限制）
            if uploaded_file.size > 10 * 1024 * 1024:
                return JsonResponse({
                    'success': False,
                    'error': '文件大小不能超过10MB'
                })

            # 解析Excel文件
            products_data = self.parse_excel_file(uploaded_file)

            if not products_data:
                return JsonResponse({
                    'success': False,
                    'error': 'Excel文件中没有找到有效的商品数据'
                })

            # 验证数据
            validation_result = self.validate_products_data(products_data)
            if not validation_result['valid']:
                return JsonResponse({
                    'success': False,
                    'error': validation_result['error']
                })

            # 获取重复处理策略
            duplicate_strategy = request.POST.get('duplicate_strategy', 'skip')

            # 批量创建商品
            result = self.create_products_batch(products_data, request.user, duplicate_strategy)

            # 构建响应消息
            message_parts = []
            if result['created_count'] > 0:
                message_parts.append(f"成功创建 {result['created_count']} 个商品")
            if result.get('updated_count', 0) > 0:
                message_parts.append(f"更新 {result['updated_count']} 个商品")
            if result['skipped_count'] > 0:
                message_parts.append(f"跳过 {result['skipped_count']} 个商品")

            message = "批量上传处理完成：" + "，".join(message_parts) if message_parts else "批量上传处理完成"

            return JsonResponse({
                'success': True,
                'message': message,
                'created_count': result['created_count'],
                'updated_count': result.get('updated_count', 0),
                'skipped_count': result['skipped_count'],
                'details': result['details']
            })

        except Exception as e:
            return JsonResponse({
                'success': False,
                'error': f'处理失败：{str(e)}'
            })

    def preview_duplicates(self, request):
        """预览重复商品检测结果"""
        try:
            # 检查是否有上传的文件
            if 'file' not in request.FILES:
                return JsonResponse({
                    'success': False,
                    'error': '请选择要上传的Excel文件'
                })

            uploaded_file = request.FILES['file']

            # 验证文件格式
            if not self.validate_file_format(uploaded_file):
                return JsonResponse({
                    'success': False,
                    'error': '文件格式不正确，请上传.xlsx或.xls格式的Excel文件'
                })

            # 解析Excel文件
            products_data = self.parse_excel_file(uploaded_file)

            if not products_data:
                return JsonResponse({
                    'success': False,
                    'error': 'Excel文件中没有找到有效的商品数据'
                })

            # 验证数据
            validation_result = self.validate_products_data(products_data)
            if not validation_result['valid']:
                return JsonResponse({
                    'success': False,
                    'error': validation_result['error']
                })

            # 检测重复商品
            duplicate_info = self.detect_duplicates(products_data)

            return JsonResponse({
                'success': True,
                'total_count': len(products_data),
                'new_count': duplicate_info['new_count'],
                'duplicate_count': duplicate_info['duplicate_count'],
                'duplicates': duplicate_info['duplicates'],
                'preview_data': products_data[:5]  # 返回前5行预览
            })

        except Exception as e:
            return JsonResponse({
                'success': False,
                'error': f'预览失败：{str(e)}'
            })

    def detect_duplicates(self, products_data):
        """检测重复商品"""
        duplicates = []
        new_count = 0
        duplicate_count = 0

        for product_data in products_data:
            name = product_data['name']
            existing_product = Product.objects.filter(name=name).first()

            if existing_product:
                duplicate_count += 1
                duplicates.append({
                    'row_number': product_data['row_number'],
                    'name': name,
                    'existing_sku': existing_product.sku,
                    'existing_price': str(existing_product.price),
                    'new_price': str(product_data['price']),
                    'price_changed': existing_product.price != product_data['price']
                })
            else:
                new_count += 1

        return {
            'new_count': new_count,
            'duplicate_count': duplicate_count,
            'duplicates': duplicates
        }

    def validate_file_format(self, uploaded_file):
        """验证文件格式"""
        allowed_extensions = ['.xlsx', '.xls']
        allowed_content_types = [
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/vnd.ms-excel'
        ]

        # 检查文件扩展名
        file_extension = uploaded_file.name.lower().split('.')[-1]
        if f'.{file_extension}' not in allowed_extensions:
            return False

        # 检查MIME类型
        if uploaded_file.content_type not in allowed_content_types:
            return False

        return True

    def parse_excel_file(self, uploaded_file):
        """解析Excel文件"""
        try:
            # 读取Excel文件
            workbook = openpyxl.load_workbook(io.BytesIO(uploaded_file.read()))

            # 优先使用"商品数据"工作表，如果不存在则使用第一个工作表
            worksheet = None
            if "商品数据" in workbook.sheetnames:
                worksheet = workbook["商品数据"]
            else:
                worksheet = workbook.active

            # 获取表头行
            headers = []
            for cell in worksheet[1]:
                headers.append(cell.value)

            # 验证必要的列是否存在 - 更新为3个核心字段（移除商品编码）
            required_columns = ['商品名称', '价格']
            optional_columns = ['商品描述', '描述']
            column_mapping = {}

            for required_col in required_columns:
                found = False
                for i, header in enumerate(headers):
                    if header and required_col in str(header):
                        column_mapping[required_col] = i
                        found = True
                        break
                if not found:
                    raise ValueError(f'Excel文件中缺少必要的列：{required_col}')

            # 查找可选的描述列
            for optional_col in optional_columns:
                for i, header in enumerate(headers):
                    if header and optional_col in str(header):
                        column_mapping['描述'] = i
                        break

            # 查找可选列（移除状态相关列）
            # 描述列已在上面处理，这里不需要额外处理

            # 解析数据行
            products_data = []
            for row_num in range(2, worksheet.max_row + 1):
                row = worksheet[row_num]

                # 跳过空行
                if all(cell.value is None or str(cell.value).strip() == '' for cell in row):
                    continue

                # 简单检查：确保至少有商品名称
                name_value = self.get_cell_value(row, column_mapping.get('商品名称'))

                if not name_value:
                    continue

                product_data = {
                    'row_number': row_num,
                    'name': self.get_cell_value(row, column_mapping.get('商品名称')),
                    'price': self.get_cell_value(row, column_mapping.get('价格')),
                    'description': self.get_cell_value(row, column_mapping.get('描述'))
                }

                products_data.append(product_data)

            return products_data

        except Exception as e:
            raise ValueError(f'Excel文件解析失败：{str(e)}')



    def get_cell_value(self, row, column_index):
        """获取单元格值"""
        if column_index is None or column_index >= len(row):
            return None
        cell_value = row[column_index].value
        return str(cell_value).strip() if cell_value is not None else None

    def parse_boolean_value(self, value):
        """解析布尔值"""
        if value is None:
            return True  # 默认启用

        value_str = str(value).lower().strip()
        true_values = ['是', 'true', '1', '启用', 'yes', 'y']
        false_values = ['否', 'false', '0', '禁用', 'no', 'n']

        if value_str in true_values:
            return True
        elif value_str in false_values:
            return False
        else:
            return True  # 默认启用

    def validate_products_data(self, products_data):
        """验证商品数据"""
        errors = []

        for product in products_data:
            row_num = product['row_number']

            # 验证必填字段
            if not product['name'] or product['name'].strip() == '':
                errors.append(f'第{row_num}行：商品名称不能为空')

            if not product['price']:
                errors.append(f'第{row_num}行：价格不能为空')
            else:
                # 验证价格格式
                try:
                    price_value = Decimal(str(product['price']))
                    if price_value < 0:
                        errors.append(f'第{row_num}行：价格不能为负数')
                    product['price'] = price_value
                except (InvalidOperation, ValueError):
                    errors.append(f'第{row_num}行：价格格式不正确')

            # 验证商品名称长度
            if product['name'] and len(product['name']) > 200:
                errors.append(f'第{row_num}行：商品名称不能超过200个字符')

        if errors:
            return {
                'valid': False,
                'error': '数据验证失败：\n' + '\n'.join(errors[:10])  # 最多显示10个错误
            }

        return {'valid': True}

    def create_products_batch(self, products_data, user, duplicate_strategy='skip'):
        """批量创建商品，支持重复检测和处理

        Args:
            products_data: 商品数据列表
            user: 当前用户
            duplicate_strategy: 重复处理策略 ('skip': 跳过, 'update': 更新)
        """
        created_count = 0
        updated_count = 0
        skipped_count = 0
        details = []

        with transaction.atomic():
            for product_data in products_data:
                try:
                    name = product_data['name']
                    description = product_data['description'] or ''
                    price = product_data['price']
                    row_number = product_data['row_number']

                    # 检测重复商品（按商品名称）
                    existing_product = Product.objects.filter(name=name).first()

                    if existing_product:
                        if duplicate_strategy == 'skip':
                            # 跳过重复商品
                            skipped_count += 1
                            details.append(f"第{row_number}行：跳过重复商品 '{name}' (已存在SKU: {existing_product.sku})")
                            continue
                        elif duplicate_strategy == 'update':
                            # 更新现有商品
                            existing_product.description = description
                            existing_product.price = price
                            existing_product.updated_by = user
                            existing_product.save()

                            updated_count += 1
                            details.append(f"第{row_number}行：更新商品 '{name}' (SKU: {existing_product.sku})")
                            continue

                    # 创建新商品（SKU将自动生成）
                    product = Product.objects.create(
                        name=name,
                        description=description,
                        price=price,
                        created_by=user,
                        updated_by=user
                    )

                    created_count += 1
                    details.append(f"第{row_number}行：成功创建商品 '{product.name}' (SKU: {product.sku})")

                except Exception as e:
                    skipped_count += 1
                    details.append(f"第{product_data['row_number']}行：处理失败 - {str(e)}")
                    # 继续处理其他商品，不中断整个批量操作

        return {
            'created_count': created_count,
            'updated_count': updated_count,
            'skipped_count': skipped_count,
            'details': details
        }


def get_model_field_info():
    """获取Product模型的字段信息 - 只返回批量上传需要的核心字段"""
    from django.db import models

    # 只保留批量上传需要的3个核心字段（移除SKU，由系统自动生成）
    allowed_fields = ['name', 'description', 'price']

    # 获取所有字段
    all_fields = Product._meta.get_fields()

    # 过滤出指定的字段
    editable_fields = []

    for field in all_fields:
        # 只处理指定的字段
        if (hasattr(field, 'name') and field.name in allowed_fields):
            field_info = {
                'name': field.name,
                'verbose_name': getattr(field, 'verbose_name', field.name),
                'field_type': type(field).__name__,
                'required': not getattr(field, 'blank', True) and not getattr(field, 'null', True),
                'max_length': getattr(field, 'max_length', None),
                'help_text': getattr(field, 'help_text', ''),
                'default': getattr(field, 'default', None),
                'choices': getattr(field, 'choices', None)
            }
            editable_fields.append(field_info)

    # 按指定顺序排序
    field_order = {name: i for i, name in enumerate(allowed_fields)}
    editable_fields.sort(key=lambda x: field_order.get(x['name'], 999))

    return editable_fields

def generate_sample_data_for_field(field_info):
    """根据字段类型生成示例数据"""
    field_type = field_info['field_type']
    field_name = field_info['name']

    # 根据字段类型生成示例数据
    if field_type == 'CharField':
        if field_name == 'name':
            return ['苹果手机', '蓝牙耳机', '充电宝']
        else:
            return ['示例文本1', '示例文本2', '示例文本3']

    elif field_type == 'TextField':
        if field_name == 'description':
            return ['最新款智能手机，性能卓越', '无线蓝牙耳机，音质清晰', '大容量移动电源，快速充电']
        else:
            return ['详细描述信息1', '详细描述信息2', '详细描述信息3']

    elif field_type == 'DecimalField':
        if field_name == 'price':
            return ['5999.00', '299.99', '89.50']
        else:
            return ['100.00', '200.50', '300.99']

    elif field_type == 'IntegerField':
        return ['100', '200', '300']

    elif field_type == 'BooleanField':
        return ['是', '是', '否']

    elif field_type == 'DateField':
        return ['2024-01-01', '2024-02-01', '2024-03-01']

    elif field_type == 'DateTimeField':
        return ['2024-01-01 10:00:00', '2024-02-01 14:30:00', '2024-03-01 16:45:00']

    else:
        return ['示例值1', '示例值2', '示例值3']

@login_required
@require_http_methods(["GET"])
def download_template(request):
    """下载商品批量上传模板 - 动态生成版本"""
    # 检查权限
    if not request.user.has_perm('products.add_product'):
        return JsonResponse({'success': False, 'error': '权限不足'}, status=403)

    try:
        # 获取模型字段信息
        field_infos = get_model_field_info()

        # 创建Excel工作簿
        workbook = openpyxl.Workbook()

        # 第一个工作表：商品数据模板
        data_sheet = workbook.active
        data_sheet.title = "商品数据"

        # 动态生成列标题
        headers = []
        for field_info in field_infos:
            verbose_name = field_info['verbose_name']
            headers.append(verbose_name)

        # 写入标题行到数据工作表
        for col, header in enumerate(headers, 1):
            cell = data_sheet.cell(row=1, column=col, value=header)
            # 设置标题样式
            cell.font = openpyxl.styles.Font(bold=True, color="FFFFFF")
            cell.fill = openpyxl.styles.PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            cell.alignment = openpyxl.styles.Alignment(horizontal="center", vertical="center")

        # 动态生成示例数据
        sample_data = []
        for row_index in range(3):  # 生成3行示例数据
            row_data = []
            for field_info in field_infos:
                sample_values = generate_sample_data_for_field(field_info)
                row_data.append(sample_values[row_index])
            sample_data.append(row_data)

        for row_num, row_data in enumerate(sample_data, 2):
            for col, value in enumerate(row_data, 1):
                cell = data_sheet.cell(row=row_num, column=col, value=value)
                # 设置数据行样式
                if row_num % 2 == 0:
                    cell.fill = openpyxl.styles.PatternFill(start_color="F8F9FA", end_color="F8F9FA", fill_type="solid")

        # 动态设置数据工作表的列宽
        for col, field_info in enumerate(field_infos, 1):
            # 根据字段类型和名称设置合适的列宽
            field_type = field_info['field_type']
            field_name = field_info['name']

            if field_name == 'name':
                width = 25
            elif field_name == 'sku':
                width = 15
            elif field_name == 'description':
                width = 35
            elif field_type in ['DecimalField', 'IntegerField']:
                width = 12
            elif field_type == 'BooleanField':
                width = 10
            elif field_type in ['DateField', 'DateTimeField']:
                width = 18
            else:
                width = 20

            data_sheet.column_dimensions[openpyxl.utils.get_column_letter(col)].width = width

        # 创建第二个工作表：使用说明
        help_sheet = workbook.create_sheet(title="使用说明")

        # 动态生成使用说明内容
        help_content = [
            ["商品批量上传使用说明", ""],
            ["", ""],
            ["1. 基本步骤", ""],
            ["   ① 切换到「商品数据」工作表", ""],
            ["   ② 删除示例数据行（第2-4行）", ""],
            ["   ③ 填写您的商品信息", ""],
            ["   ④ 保存文件并上传", ""],
            ["", ""],
            ["2. 字段说明", ""]
        ]

        # 动态添加字段说明
        for field_info in field_infos:
            verbose_name = field_info['verbose_name']
            if field_info['name'] == 'sku':
                verbose_name = f"{verbose_name}(SKU)"

            # 生成字段说明
            description_parts = []

            # 必填/可选
            if field_info['required']:
                description_parts.append("必填")
            else:
                description_parts.append("可选")

            # 字段类型特定说明
            field_type = field_info['field_type']
            if field_type == 'CharField' and field_info['max_length']:
                description_parts.append(f"不超过{field_info['max_length']}个字符")
            elif field_type == 'TextField':
                description_parts.append("详细描述信息")
            elif field_type == 'DecimalField':
                description_parts.append("数字格式，不能为负数")
            elif field_type == 'BooleanField':
                description_parts.append("填写'是'或'否'，默认为'是'")
            elif field_type == 'DateField':
                description_parts.append("日期格式：YYYY-MM-DD")
            elif field_type == 'DateTimeField':
                description_parts.append("日期时间格式：YYYY-MM-DD HH:MM:SS")

            # 特殊字段的额外说明
            if field_info['name'] == 'sku':
                description_parts.append("只能包含字母、数字、下划线、连字符")

            description = "，".join(description_parts)
            help_content.append([f"   {verbose_name}", description])

        # 添加其余说明内容
        help_content.extend([
            ["", ""],
            ["3. 注意事项", ""],
            ["   • 请确保商品编码(SKU)唯一，重复的SKU会被跳过", ""],
            ["   • 数字字段请使用正确的数字格式", ""],
            ["   • 布尔字段支持：是/否、true/false、1/0", ""],
            ["   • 建议一次上传不超过1000个商品", ""],
            ["", ""],
            ["4. 示例数据", ""],
            ["   商品数据工作表中包含3行示例数据", ""],
            ["   您可以参考示例格式填写自己的商品信息", ""],
            ["   上传前请删除示例数据或直接修改为您的数据", ""]
        ])

        # 写入说明内容
        for row_num, (col1, col2) in enumerate(help_content, 1):
            help_sheet.cell(row=row_num, column=1, value=col1)
            help_sheet.cell(row=row_num, column=2, value=col2)

        # 设置说明工作表样式
        # 标题样式
        title_cell = help_sheet.cell(row=1, column=1)
        title_cell.font = openpyxl.styles.Font(bold=True, size=14, color="366092")

        # 动态计算章节标题行号
        section_rows = []
        for row_num, (col1, col2) in enumerate(help_content, 1):
            if col1 in ["1. 基本步骤", "2. 字段说明", "3. 注意事项", "4. 示例数据"]:
                section_rows.append(row_num)

        # 章节标题样式
        for row in section_rows:
            cell = help_sheet.cell(row=row, column=1)
            cell.font = openpyxl.styles.Font(bold=True, color="366092")

        # 设置列宽
        help_sheet.column_dimensions['A'].width = 35
        help_sheet.column_dimensions['B'].width = 50

        # 保存到内存
        output = io.BytesIO()
        workbook.save(output)
        output.seek(0)

        # 设置HTTP响应
        from django.http import HttpResponse
        response = HttpResponse(
            output.getvalue(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = 'attachment; filename="商品批量上传模板.xlsx"'
        response['Content-Length'] = len(output.getvalue())

        return response

    except Exception as e:
        return JsonResponse({'success': False, 'error': f'模板生成失败：{str(e)}'}, status=500)
