from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from products.models import Product
from users.services import AuditService
from users.models import AuditLog


class Command(BaseCommand):
    help = '测试审计日志功能'

    def handle(self, *args, **options):
        # 获取一个用户
        user = User.objects.first()
        if not user:
            self.stdout.write(self.style.ERROR('没有找到用户'))
            return

        # 创建一个测试商品
        product = Product.objects.create(
            name='测试商品',
            sku='TEST001',
            description='测试商品描述',
            price=100.00,
            created_by=user
        )
        
        self.stdout.write(f'创建测试商品: {product.name}')

        # 手动记录审计日志
        audit_log = AuditService.log_delete(
            instance=product,
            user=user,
            extra_data={
                'model': 'Product',
                'operation': 'test_delete',
                'test': True
            }
        )

        self.stdout.write(f'创建审计日志: {audit_log}')
        self.stdout.write(f'审计日志用户: {audit_log.user if audit_log else "None"}')

        # 查看最新的审计日志
        latest_logs = AuditLog.objects.order_by('-timestamp')[:5]
        self.stdout.write('\n最新的5条审计日志:')
        for log in latest_logs:
            user_name = log.user.username if log.user else '系统'
            self.stdout.write(f'- {log.timestamp}: {user_name} {log.get_action_display()} {log.object_repr}')

        # 删除测试商品
        product.delete()
        self.stdout.write('删除测试商品')
