"""
权限中间件模块
提供全局权限检查、页面级权限验证和审计日志记录
"""
import json
import re
import time
import logging
from typing import Optional, List, Dict, Any
from django.utils.deprecation import MiddlewareMixin
from django.contrib.auth.models import AnonymousUser
from django.http import JsonResponse, HttpResponseForbidden
from django.shortcuts import redirect, render
from django.contrib import messages
from django.urls import reverse, resolve, Resolver404
from django.core.cache import cache
from django.conf import settings
from django.template.loader import render_to_string
from .permissions import PermissionManager

logger = logging.getLogger(__name__)


class PagePermissionMiddleware(MiddlewareMixin):
    """页面级权限检查中间件"""

    def __init__(self, get_response):
        self.get_response = get_response
        super().__init__(get_response)

        # 初始化配置
        self.cache_timeout = getattr(settings, 'PAGE_PERMISSION_CACHE_TIMEOUT', 300)
        self.enable_cache = getattr(settings, 'PAGE_PERMISSION_ENABLE_CACHE', True)
        self.debug_mode = getattr(settings, 'DEBUG', False)

        # 编译URL模式缓存
        self._compiled_patterns = {}

        # 性能统计
        self._stats = {
            'total_checks': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'permission_denials': 0,
            'avg_check_time': 0.0
        }

    # 不需要权限检查的URL路径
    EXEMPT_URLS = [
        '/users/login/',
        '/users/register/',
        '/users/logout/',
        '/admin/',
        '/static/',
        '/media/',
        '/favicon.ico',
        '/robots.txt',
        '/sitemap.xml',
    ]

    # 不需要权限检查的URL模式（正则表达式）
    EXEMPT_PATTERNS = [
        r'^/api/public/',
        r'^/health/',
        r'^/status/',
        r'^/debug/',
    ]

    # 公开访问的URL路径
    PUBLIC_URLS = [
        '/',
        '/users/login/',
        '/users/register/',
        '/about/',
        '/contact/',
    ]

    def __call__(self, request):
        """中间件主入口"""
        start_time = time.time()

        # 处理请求
        response = self.process_request(request)
        if response:
            return response

        # 获取视图响应
        response = self.get_response(request)

        # 处理响应
        response = self.process_response(request, response)

        # 记录性能统计
        if self.debug_mode:
            check_time = time.time() - start_time
            self._update_stats(check_time)

        return response

    def process_request(self, request):
        """处理请求前的权限检查"""
        # 跳过不需要检查的URL
        if self._should_skip_permission_check(request):
            return None

        # 检查用户登录状态
        if isinstance(request.user, AnonymousUser):
            return self._handle_anonymous_user(request)

        # 执行页面权限检查
        return self._check_page_permission(request)

    def process_response(self, request, response):
        """处理响应后的清理工作"""
        # 在DEBUG模式下添加权限检查信息到响应头
        if self.debug_mode and hasattr(request, '_permission_check_info'):
            response['X-Permission-Check-Info'] = str(request._permission_check_info)

        return response

    def _should_skip_permission_check(self, request) -> bool:
        """判断是否跳过权限检查"""
        path = request.path

        # 检查精确匹配的豁免URL
        if any(path.startswith(url) for url in self.EXEMPT_URLS):
            return True

        # 检查正则表达式模式
        for pattern in self.EXEMPT_PATTERNS:
            if self._match_url_pattern(path, pattern):
                return True

        return False

    def _match_url_pattern(self, path: str, pattern: str) -> bool:
        """匹配URL模式（支持正则表达式）"""
        # 使用缓存的编译模式
        if pattern not in self._compiled_patterns:
            try:
                self._compiled_patterns[pattern] = re.compile(pattern)
            except re.error as e:
                logger.warning(f"Invalid URL pattern '{pattern}': {e}")
                return False

        compiled_pattern = self._compiled_patterns[pattern]
        return bool(compiled_pattern.match(path))

    def _handle_anonymous_user(self, request):
        """处理匿名用户访问"""
        path = request.path

        # 允许访问公开页面
        if path in self.PUBLIC_URLS:
            return None

        # 处理AJAX请求
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({
                'success': False,
                'error': 'authentication_required',
                'message': '请先登录',
                'redirect_url': reverse('users:login')
            }, status=401)

        # 重定向到登录页面
        messages.info(request, '请先登录以访问此页面')
        return redirect(f"{reverse('users:login')}?next={request.path}")

    def _check_page_permission(self, request):
        """检查页面权限"""
        start_time = time.time()

        try:
            # 解析URL信息
            url_info = self._resolve_url_info(request)
            if not url_info:
                return None

            # 生成权限缓存键
            cache_key = self._generate_cache_key(request.user.id, url_info)

            # 检查缓存
            has_permission = None
            if self.enable_cache:
                has_permission = cache.get(cache_key)
                if has_permission is not None:
                    self._stats['cache_hits'] += 1
                else:
                    self._stats['cache_misses'] += 1

            # 如果缓存中没有，进行权限检查
            if has_permission is None:
                has_permission = self._perform_permission_check(request.user, url_info)

                # 缓存结果
                if self.enable_cache:
                    cache.set(cache_key, has_permission, self.cache_timeout)

            # 记录检查信息
            check_time = time.time() - start_time
            if self.debug_mode:
                request._permission_check_info = {
                    'url_info': url_info,
                    'has_permission': has_permission,
                    'check_time': round(check_time * 1000, 2),  # 毫秒
                    'cache_hit': has_permission is not None and self.enable_cache
                }

            # 处理权限拒绝
            if not has_permission:
                self._stats['permission_denials'] += 1
                return self._handle_permission_denied(request, url_info)

            return None

        except Exception as e:
            logger.error(f"Page permission check failed for {request.path}: {e}")
            # 在出错时允许访问，避免影响正常业务
            return None

    def _resolve_url_info(self, request) -> Optional[Dict[str, Any]]:
        """解析URL信息"""
        try:
            resolved = resolve(request.path)
            return {
                'app_name': resolved.app_name,
                'url_name': resolved.url_name,
                'view_name': resolved.view_name,
                'namespace': resolved.namespace,
                'path': request.path,
                'kwargs': resolved.kwargs
            }
        except Resolver404:
            logger.warning(f"Cannot resolve URL: {request.path}")
            return None

    def _generate_cache_key(self, user_id: int, url_info: Dict[str, Any]) -> str:
        """生成权限缓存键"""
        app_name = url_info.get('app_name', 'unknown')
        url_name = url_info.get('url_name', 'unknown')
        return f"page_perm:{user_id}:{app_name}:{url_name}"

    def _perform_permission_check(self, user, url_info: Dict[str, Any]) -> bool:
        """执行权限检查"""
        app_name = url_info.get('app_name')
        url_name = url_info.get('url_name')

        if not app_name or not url_name:
            # 如果无法解析应用名称或URL名称，默认允许访问
            return True

        # 使用PermissionManager检查页面权限
        try:
            return PermissionManager.check_page_permission(user, app_name, url_name)
        except Exception as e:
            logger.error(f"Permission check failed for {app_name}.{url_name}: {e}")
            # 出错时默认允许访问
            return True

    def _handle_permission_denied(self, request, url_info: Dict[str, Any]):
        """处理权限拒绝"""
        app_name = url_info.get('app_name', 'unknown')
        url_name = url_info.get('url_name', 'unknown')

        # 记录权限拒绝日志
        logger.warning(
            f"Permission denied for user {request.user.username} "
            f"accessing {app_name}.{url_name} ({request.path})"
        )

        # 记录审计日志
        self._log_permission_denial(request, url_info)

        # 处理AJAX请求
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({
                'success': False,
                'error': 'permission_denied',
                'message': f'您没有权限访问此页面：{app_name}.{url_name}',
                'redirect_url': reverse('products:list')  # 重定向到安全页面
            }, status=403)

        # 处理普通请求
        context = {
            'error_title': '访问被拒绝',
            'error_message': f'您没有权限访问此页面：{app_name}.{url_name}',
            'app_name': app_name,
            'url_name': url_name,
            'user_role': PermissionManager.get_user_role(request.user),
            'suggested_actions': [
                '联系管理员申请相应权限',
                '检查您的账户角色设置',
                '返回到您有权限访问的页面'
            ]
        }

        # 尝试渲染自定义错误页面
        try:
            return render(request, 'errors/permission_denied.html', context, status=403)
        except Exception:
            # 如果自定义模板不存在，返回简单的HTTP 403响应
            return HttpResponseForbidden(
                f'<h1>403 Forbidden</h1>'
                f'<p>您没有权限访问此页面：{app_name}.{url_name}</p>'
                f'<p><a href="{reverse("products:list")}">返回首页</a></p>'
            )

    def _log_permission_denial(self, request, url_info: Dict[str, Any]):
        """记录权限拒绝的审计日志"""
        try:
            from .models import AuditLog
            from django.contrib.contenttypes.models import ContentType

            AuditLog.objects.create(
                user=request.user,
                action='PERMISSION_DENIED',
                content_type=ContentType.objects.get_for_model(request.user),
                object_id=request.user.id,
                object_repr=f"页面权限拒绝: {url_info.get('app_name')}.{url_info.get('url_name')}",
                changes={
                    'denied_permission': f"{url_info.get('app_name')}.{url_info.get('url_name')}",
                    'requested_path': request.path,
                    'user_role': PermissionManager.get_user_role(request.user)
                },
                ip_address=self._get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                extra_data={
                    'url_info': url_info,
                    'middleware': 'PagePermissionMiddleware'
                }
            )
        except Exception as e:
            logger.error(f"Failed to log permission denial: {e}")

    def _get_client_ip(self, request) -> str:
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR', 'unknown')
        return ip

    def _update_stats(self, check_time: float):
        """更新性能统计"""
        self._stats['total_checks'] += 1

        # 计算平均检查时间
        total_time = self._stats['avg_check_time'] * (self._stats['total_checks'] - 1)
        self._stats['avg_check_time'] = (total_time + check_time) / self._stats['total_checks']

    def get_stats(self) -> Dict[str, Any]:
        """获取中间件性能统计"""
        cache_hit_rate = 0
        if self._stats['total_checks'] > 0:
            cache_hit_rate = (self._stats['cache_hits'] /
                            (self._stats['cache_hits'] + self._stats['cache_misses'])) * 100

        return {
            'total_checks': self._stats['total_checks'],
            'cache_hits': self._stats['cache_hits'],
            'cache_misses': self._stats['cache_misses'],
            'cache_hit_rate': round(cache_hit_rate, 2),
            'permission_denials': self._stats['permission_denials'],
            'avg_check_time_ms': round(self._stats['avg_check_time'] * 1000, 2),
            'denial_rate': round((self._stats['permission_denials'] /
                               max(self._stats['total_checks'], 1)) * 100, 2)
        }

    def clear_cache(self, user_id: Optional[int] = None):
        """清除权限缓存"""
        if user_id:
            # 清除特定用户的缓存
            cache_pattern = f"page_perm:{user_id}:*"
            # 注意：这需要Redis支持，Django默认缓存不支持模式删除
            try:
                if hasattr(cache, 'delete_pattern'):
                    cache.delete_pattern(cache_pattern)
                else:
                    logger.warning("Cache backend does not support pattern deletion")
            except Exception as e:
                logger.error(f"Failed to clear user cache: {e}")
        else:
            # 清除所有页面权限缓存
            try:
                if hasattr(cache, 'delete_pattern'):
                    cache.delete_pattern("page_perm:*")
                else:
                    cache.clear()  # 清除所有缓存
            except Exception as e:
                logger.error(f"Failed to clear cache: {e}")


class PermissionMiddleware(MiddlewareMixin):
    """权限检查中间件"""
    
    # 不需要权限检查的URL路径
    EXEMPT_URLS = [
        '/users/login/',
        '/users/register/',
        '/users/logout/',
        '/admin/',
        '/static/',
        '/media/',
    ]
    
    # 公开访问的URL路径
    PUBLIC_URLS = [
        '/',
        '/users/login/',
        '/users/register/',
    ]
    
    def process_request(self, request):
        """处理请求前的权限检查"""
        # 跳过不需要检查的URL
        if any(request.path.startswith(url) for url in self.EXEMPT_URLS):
            return None
        
        # 跳过未登录用户的公开页面
        if isinstance(request.user, AnonymousUser):
            if request.path in self.PUBLIC_URLS:
                return None
            # 重定向到登录页面
            return redirect('users:login')
        
        return None
    
    def process_response(self, request, response):
        """处理响应后的清理工作"""
        return response


class AuditMiddleware(MiddlewareMixin):
    """审计中间件 - 记录用户操作"""
    
    # 需要记录的HTTP方法
    AUDIT_METHODS = ['POST', 'PUT', 'PATCH', 'DELETE']
    
    # 不需要审计的URL路径
    EXEMPT_URLS = [
        '/admin/',  # 排除所有admin操作，由admin类自己处理
        '/static/',
        '/media/',
        '/users/login/',
        '/users/logout/',
    ]
    
    def __init__(self, get_response):
        self.get_response = get_response
        super().__init__(get_response)
    
    def __call__(self, request):
        # 请求前处理
        self.process_request(request)
        
        # 获取响应
        response = self.get_response(request)
        
        # 响应后处理
        self.process_response(request, response)
        
        return response
    
    def process_request(self, request):
        """记录请求信息"""
        # 跳过不需要审计的请求
        if self._should_skip_audit(request):
            return None
        
        # 存储请求开始时间
        import time
        request._audit_start_time = time.time()
        
        # 存储请求数据
        if request.method in self.AUDIT_METHODS:
            request._audit_data = self._get_request_data(request)
        
        return None
    
    def process_response(self, request, response):
        """记录响应信息和操作日志"""
        # 跳过不需要审计的请求
        if self._should_skip_audit(request):
            return response
        
        # 只记录需要审计的方法
        if request.method not in self.AUDIT_METHODS:
            return response
        
        # 只记录已登录用户的操作
        if isinstance(request.user, AnonymousUser):
            return response
        
        # 记录操作日志
        try:
            self._log_operation(request, response)
        except Exception as e:
            # 审计日志记录失败不应该影响正常业务
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"审计日志记录失败: {str(e)}")
        
        return response
    
    def _should_skip_audit(self, request):
        """判断是否跳过审计"""
        return any(request.path.startswith(url) for url in self.EXEMPT_URLS)
    
    def _get_request_data(self, request):
        """获取请求数据"""
        data = {}
        
        # 获取POST数据
        if hasattr(request, 'POST') and request.POST:
            data['POST'] = dict(request.POST)
            # 移除敏感信息
            if 'password' in data['POST']:
                data['POST']['password'] = '***'
            if 'csrfmiddlewaretoken' in data['POST']:
                del data['POST']['csrfmiddlewaretoken']
        
        # 获取JSON数据
        if request.content_type == 'application/json':
            try:
                json_data = json.loads(request.body.decode('utf-8'))
                data['JSON'] = json_data
                # 移除敏感信息
                if isinstance(json_data, dict) and 'password' in json_data:
                    data['JSON']['password'] = '***'
            except (json.JSONDecodeError, UnicodeDecodeError):
                pass
        
        return data
    
    def _log_operation(self, request, response):
        """记录操作日志"""
        from .models import AuditLog
        from django.contrib.contenttypes.models import ContentType
        
        # 确定操作类型
        action = self._determine_action(request)
        
        # 获取客户端IP
        ip_address = self._get_client_ip(request)
        
        # 构建额外数据
        extra_data = {
            'method': request.method,
            'path': request.path,
            'status_code': response.status_code,
            'user_agent': request.META.get('HTTP_USER_AGENT', ''),
        }
        
        # 添加请求数据
        if hasattr(request, '_audit_data'):
            extra_data['request_data'] = request._audit_data
        
        # 添加响应时间
        if hasattr(request, '_audit_start_time'):
            import time
            extra_data['response_time'] = round(time.time() - request._audit_start_time, 3)
        
        # 创建审计日志
        AuditLog.objects.create(
            user=request.user,
            action=action,
            content_type=ContentType.objects.get_for_model(request.user),
            object_id=request.user.id,
            object_repr=f"用户操作: {request.path}",
            changes={},
            ip_address=ip_address,
            user_agent=request.META.get('HTTP_USER_AGENT', ''),
            extra_data=extra_data
        )
    
    def _determine_action(self, request):
        """根据请求确定操作类型"""
        method_action_map = {
            'POST': 'CREATE',
            'PUT': 'UPDATE',
            'PATCH': 'UPDATE',
            'DELETE': 'DELETE',
        }
        return method_action_map.get(request.method, 'VIEW')
    
    def _get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class UserActivityMiddleware(MiddlewareMixin):
    """用户活动中间件 - 更新用户最后活动时间"""
    
    def process_request(self, request):
        """更新用户最后活动时间"""
        if request.user.is_authenticated:
            try:
                from django.utils import timezone
                request.user.last_login = timezone.now()
                request.user.save(update_fields=['last_login'])
            except Exception:
                # 忽略更新失败
                pass
        
        return None
