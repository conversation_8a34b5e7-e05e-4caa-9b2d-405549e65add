<form method="post" action="{% if object %}{% url 'products:update' object.pk %}{% else %}{% url 'products:create' %}{% endif %}" data-submit-url="{% if object %}{% url 'products:update' object.pk %}{% else %}{% url 'products:create' %}{% endif %}">
    {% csrf_token %}
    
    {% if form.non_field_errors %}
        <div class="alert alert-danger">
            <i class="bi bi-exclamation-triangle"></i>
            {{ form.non_field_errors }}
        </div>
    {% endif %}

    <div class="mb-3">
        <label for="{{ form.name.id_for_label }}" class="form-label required">
            {{ form.name.label }} <span class="text-danger">*</span>
        </label>
        {{ form.name }}
        {% if form.name.errors %}
            <div class="invalid-feedback d-block">
                <i class="bi bi-exclamation-circle"></i>
                {{ form.name.errors.0 }}
            </div>
        {% endif %}
        <div class="form-text">
            <i class="bi bi-info-circle"></i>
            商品编码(SKU)将根据商品名称自动生成
        </div>
    </div>

    <div class="mb-3">
        <label for="{{ form.price.id_for_label }}" class="form-label required">
            {{ form.price.label }} <span class="text-danger">*</span>
        </label>
        <div class="input-group">
            <span class="input-group-text">¥</span>
            {{ form.price }}
        </div>
        {% if form.price.errors %}
            <div class="invalid-feedback d-block">
                <i class="bi bi-exclamation-circle"></i>
                {{ form.price.errors.0 }}
            </div>
        {% endif %}
    </div>

    <div class="mb-3">
        <label for="{{ form.description.id_for_label }}" class="form-label">
            {{ form.description.label }}
        </label>
        {{ form.description }}
        {% if form.description.errors %}
            <div class="invalid-feedback d-block">
                <i class="bi bi-exclamation-circle"></i>
                {{ form.description.errors.0 }}
            </div>
        {% endif %}
        <div class="form-text">
            <i class="bi bi-info-circle"></i>
            可选项，用于描述商品的详细信息
        </div>
    </div>

    <!-- 隐藏字段用于标识这是模态框表单 -->
    <input type="hidden" name="modal_form" value="1">
</form>
