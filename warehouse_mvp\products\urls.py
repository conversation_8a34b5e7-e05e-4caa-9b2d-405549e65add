from django.urls import path
from . import views

app_name = 'products'

urlpatterns = [
    path('', views.ProductListView.as_view(), name='list'),
    path('create/', views.ProductCreateView.as_view(), name='create'),
    path('<int:pk>/update/', views.ProductUpdateView.as_view(), name='update'),
    path('<int:pk>/delete/', views.ProductDeleteView.as_view(), name='delete'),
    path('api/search/', views.ajax_search, name='ajax_search'),
    path('api/batch-upload/', views.BatchUploadView.as_view(), name='batch_upload'),
    path('api/preview-duplicates/', views.BatchUploadView.as_view(), {'action': 'preview'}, name='preview_duplicates'),
    path('api/download-template/', views.download_template, name='download_template'),
]
