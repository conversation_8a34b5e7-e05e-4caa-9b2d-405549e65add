"""
SPA (Single Page Application) 视图
用于实现页面局部刷新功能
"""

from django.shortcuts import render
from django.http import JsonResponse
from django.template.loader import render_to_string
from django.views.decorators.http import require_http_methods
from django.contrib.auth.decorators import login_required
from django.urls import resolve, reverse
from django.core.exceptions import PermissionDenied
from django.contrib import messages
import re


# 页面路由映射
PAGE_ROUTES = {
    '/products/': {
        'view_name': 'products:list',
        'template': 'products/spa_content.html',
        'title': '商品管理'
    },
    '/inventory/': {
        'view_name': 'inventory:list',
        'template': 'inventory/spa_content.html',
        'title': '库存查看'
    },
    '/transactions/': {
        'view_name': 'transactions:list',
        'template': 'transactions/spa_content.html',
        'title': '进出库记录'
    }
}


@login_required
@require_http_methods(["GET"])
def load_page_content(request):
    """
    AJAX加载页面内容
    用于SPA功能，只返回页面的主要内容部分
    """
    try:
        # 获取目标URL
        target_url = request.GET.get('url', '/')

        # 检查是否是支持的路由
        if target_url not in PAGE_ROUTES:
            return JsonResponse({
                'success': False,
                'error': '不支持的页面路由'
            })

        route_config = PAGE_ROUTES[target_url]

        # 解析URL获取视图信息
        try:
            resolved = resolve(target_url)
        except:
            return JsonResponse({
                'success': False,
                'error': '无效的URL'
            })

        # 获取视图类
        view_func = resolved.func
        view_args = resolved.args
        view_kwargs = resolved.kwargs

        # 创建视图实例并获取上下文数据
        if hasattr(view_func, 'view_class'):
            view_class = view_func.view_class
            view_instance = view_class()
            view_instance.setup(request, *view_args, **view_kwargs)

            # 检查权限
            try:
                # 检查权限但不执行完整的dispatch
                if hasattr(view_instance, 'permission_app') and hasattr(view_instance, 'permission_action'):
                    # 简单的权限检查
                    try:
                        permission_name = f"{view_instance.permission_app}.{view_instance.permission_action}_{view_instance.model._meta.model_name}"
                        if not request.user.has_perm(permission_name):
                            return JsonResponse({
                                'success': False,
                                'error': '权限不足'
                            })
                    except Exception as e:
                        # 如果权限检查失败，记录错误但继续执行
                        print(f"权限检查错误: {e}")
                        pass

                # 设置请求对象
                view_instance.request = request
                view_instance.args = view_args
                view_instance.kwargs = view_kwargs

                # 获取查询集
                queryset = view_instance.get_queryset()

                # 处理分页
                paginate_by = getattr(view_instance, 'paginate_by', None)
                if paginate_by:
                    from django.core.paginator import Paginator
                    paginator = Paginator(queryset, paginate_by)
                    page_number = request.GET.get('page', 1)
                    page_obj = paginator.get_page(page_number)

                    # 设置分页相关属性
                    view_instance.object_list = page_obj
                    view_instance.paginate_by = paginate_by

                    # 调用get_context_data获取完整上下文
                    context = view_instance.get_context_data(
                        object_list=page_obj,
                        page_obj=page_obj,
                        paginator=paginator,
                        is_paginated=page_obj.has_other_pages()
                    )
                else:
                    view_instance.object_list = queryset
                    context = view_instance.get_context_data(object_list=queryset)

                # 添加请求对象到上下文
                context['request'] = request

                # 添加权限信息到上下文 - 使用Django的权限包装器
                from django.contrib.auth.context_processors import PermWrapper
                context['perms'] = PermWrapper(request.user)

            except PermissionDenied:
                return JsonResponse({
                    'success': False,
                    'error': '权限不足'
                })
        else:
            return JsonResponse({
                'success': False,
                'error': '不支持的视图类型'
            })

        # 渲染内容模板
        try:
            content_html = render_to_string(route_config['template'], context, request)
        except Exception as template_error:
            return JsonResponse({
                'success': False,
                'error': f'模板渲染失败: {str(template_error)}'
            })

        return JsonResponse({
            'success': True,
            'content': content_html,
            'title': route_config['title'],
            'url': target_url
        })

    except Exception as e:
        import traceback
        print(f"SPA视图错误: {e}")
        print(traceback.format_exc())
        return JsonResponse({
            'success': False,
            'error': f'加载页面失败: {str(e)}'
        })
