# Generated by Django 4.1.2 on 2025-07-29 09:44

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Product",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.Char<PERSON>ield(max_length=200, verbose_name="商品名称")),
                (
                    "sku",
                    models.CharField(
                        max_length=100, unique=True, verbose_name="商品编码"
                    ),
                ),
                ("description", models.TextField(blank=True, verbose_name="商品描述")),
                (
                    "price",
                    models.DecimalField(
                        decimal_places=2, max_digits=10, verbose_name="价格"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "is_active",
                    models.<PERSON><PERSON>an<PERSON>ield(default=True, verbose_name="是否启用"),
                ),
            ],
            options={
                "verbose_name": "商品",
                "verbose_name_plural": "商品",
                "ordering": ["-created_at"],
            },
        ),
    ]
