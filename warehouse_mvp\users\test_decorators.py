"""
权限装饰器测试模块
"""
from django.test import TestCase, RequestFactory
from django.contrib.auth.models import User
from django.http import HttpResponse
from django.core.exceptions import PermissionDenied
from django.contrib.messages.storage.fallback import FallbackStorage
from django.contrib.sessions.middleware import SessionMiddleware
from unittest.mock import patch, MagicMock

from .decorators import (
    permission_required, page_permission_required, dynamic_permission_required,
    multi_permission_required, PermissionRequiredMixin, PagePermissionRequiredMixin,
    MultiPermissionRequiredMixin, RoleRequiredMixin, check_permission_sync,
    get_user_permissions_summary
)
from .permissions import UserRoles
from .models import UserProfile


class DecoratorTestCase(TestCase):
    def setUp(self):
        self.factory = RequestFactory()
        
        # 创建测试用户
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # 创建用户配置
        self.user_profile = UserProfile.objects.create(
            user=self.user,
            role=UserRoles.WAREHOUSE_OPERATOR
        )

        # 创建超级管理员用户
        self.admin_user = User.objects.create_user(
            username='admin',
            email='<EMAIL>',
            password='adminpass123'
        )

        self.admin_profile = UserProfile.objects.create(
            user=self.admin_user,
            role=UserRoles.SUPER_ADMIN
        )

    def _add_session_and_messages(self, request):
        """为请求添加session和messages支持"""
        middleware = SessionMiddleware(lambda req: None)
        middleware.process_request(request)
        request.session.save()

        messages = FallbackStorage(request)
        setattr(request, '_messages', messages)

    def test_permission_required_decorator_success(self):
        """测试权限装饰器成功情况"""
        @permission_required('products', 'view')
        def test_view(request):
            return HttpResponse('Success')
        
        request = self.factory.get('/test/')
        request.user = self.admin_user
        self._add_session_and_messages(request)
        
        with patch('users.permissions.PermissionManager.user_has_permission', return_value=True):
            response = test_view(request)
            self.assertEqual(response.status_code, 200)
            self.assertEqual(response.content.decode(), 'Success')

    def test_permission_required_decorator_denied(self):
        """测试权限装饰器拒绝情况"""
        @permission_required('products', 'delete', raise_exception=True)
        def test_view(request):
            return HttpResponse('Success')
        
        request = self.factory.get('/test/')
        request.user = self.user
        self._add_session_and_messages(request)
        
        with patch('users.permissions.PermissionManager.user_has_permission', return_value=False):
            with self.assertRaises(PermissionDenied):
                test_view(request)

    def test_page_permission_required_decorator(self):
        """测试页面权限装饰器"""
        @page_permission_required('products_list', 'products')
        def test_view(request):
            return HttpResponse('Success')
        
        request = self.factory.get('/products/')
        request.user = self.admin_user
        self._add_session_and_messages(request)
        
        with patch('users.permissions.PermissionManager.check_page_permission', return_value=True):
            response = test_view(request)
            self.assertEqual(response.status_code, 200)

    def test_dynamic_permission_required_decorator(self):
        """测试动态权限装饰器"""
        def permission_func(request, *args, **kwargs):
            return f"dynamic.{request.user.username}.access"
        
        @dynamic_permission_required(permission_func)
        def test_view(request):
            return HttpResponse('Success')
        
        request = self.factory.get('/test/')
        request.user = self.admin_user
        self._add_session_and_messages(request)
        
        with patch('users.permissions.PermissionManager.check_custom_permission', return_value=True):
            response = test_view(request)
            self.assertEqual(response.status_code, 200)

    def test_multi_permission_required_decorator_and(self):
        """测试多权限装饰器AND操作"""
        @multi_permission_required([('products', 'view'), ('inventory', 'view')], operator='AND')
        def test_view(request):
            return HttpResponse('Success')
        
        request = self.factory.get('/test/')
        request.user = self.admin_user
        self._add_session_and_messages(request)
        
        with patch('users.permissions.PermissionManager.user_has_permission', return_value=True):
            response = test_view(request)
            self.assertEqual(response.status_code, 200)

    def test_multi_permission_required_decorator_or(self):
        """测试多权限装饰器OR操作"""
        @multi_permission_required([('products', 'view'), ('inventory', 'view')], operator='OR')
        def test_view(request):
            return HttpResponse('Success')
        
        request = self.factory.get('/test/')
        request.user = self.user
        self._add_session_and_messages(request)
        
        # 模拟第一个权限失败，第二个权限成功
        def mock_permission_check(user, app, action):
            if app == 'products':
                return False
            elif app == 'inventory':
                return True
            return False
        
        with patch('users.permissions.PermissionManager.user_has_permission', side_effect=mock_permission_check):
            response = test_view(request)
            self.assertEqual(response.status_code, 200)

    def test_permission_required_mixin(self):
        """测试权限Mixin类"""
        from django.views import View

        class TestView(PermissionRequiredMixin, View):
            permission_app = 'products'
            permission_action = 'view'

            def get(self, request):
                return HttpResponse('Success')

        view = TestView()
        request = self.factory.get('/test/')
        request.user = self.admin_user
        self._add_session_and_messages(request)

        with patch('users.permissions.PermissionManager.user_has_permission', return_value=True):
            response = view.dispatch(request)
            self.assertEqual(response.status_code, 200)

    def test_page_permission_required_mixin(self):
        """测试页面权限Mixin类"""
        from django.views import View

        class TestView(PagePermissionRequiredMixin, View):
            page_permission_name = 'products_list'
            page_app_name = 'products'

            def get(self, request):
                return HttpResponse('Success')

        view = TestView()
        request = self.factory.get('/products/')
        request.user = self.admin_user
        self._add_session_and_messages(request)

        with patch('users.permissions.PermissionManager.check_page_permission', return_value=True):
            response = view.dispatch(request)
            self.assertEqual(response.status_code, 200)

    def test_multi_permission_required_mixin(self):
        """测试多权限Mixin类"""
        from django.views import View

        class TestView(MultiPermissionRequiredMixin, View):
            required_permissions = [('products', 'view'), ('inventory', 'view')]
            permission_operator = 'AND'

            def get(self, request):
                return HttpResponse('Success')

        view = TestView()
        request = self.factory.get('/test/')
        request.user = self.admin_user
        self._add_session_and_messages(request)

        with patch('users.permissions.PermissionManager.user_has_permission', return_value=True):
            response = view.dispatch(request)
            self.assertEqual(response.status_code, 200)

    def test_role_required_mixin(self):
        """测试角色Mixin类"""
        from django.views import View

        class TestView(RoleRequiredMixin, View):
            allowed_roles = [UserRoles.SUPER_ADMIN, UserRoles.WAREHOUSE_MANAGER]

            def get(self, request):
                return HttpResponse('Success')

        view = TestView()
        request = self.factory.get('/test/')
        request.user = self.admin_user
        self._add_session_and_messages(request)

        with patch('users.permissions.PermissionManager.get_user_role', return_value=UserRoles.SUPER_ADMIN):
            response = view.dispatch(request)
            self.assertEqual(response.status_code, 200)

    def test_check_permission_sync_function(self):
        """测试同步权限检查函数"""
        with patch('users.permissions.PermissionManager.check_page_permission', return_value=True):
            result = check_permission_sync(self.admin_user, 'page.products.products_list')
            self.assertTrue(result)
        
        with patch('users.permissions.PermissionManager.user_has_permission', return_value=True):
            result = check_permission_sync(self.admin_user, 'app.products.view')
            self.assertTrue(result)
        
        with patch('users.permissions.PermissionManager.check_custom_permission', return_value=True):
            result = check_permission_sync(self.admin_user, 'custom.permission.key')
            self.assertTrue(result)

    def test_get_user_permissions_summary(self):
        """测试用户权限摘要函数"""
        with patch('users.permissions.PermissionManager.get_user_role', return_value=UserRoles.SUPER_ADMIN):
            with patch('users.permissions.PermissionManager.get_user_permissions', return_value=['perm1', 'perm2']):
                summary = get_user_permissions_summary(self.admin_user)
                
                self.assertEqual(summary['user_id'], self.admin_user.id)
                self.assertEqual(summary['username'], self.admin_user.username)
                self.assertEqual(summary['role'], UserRoles.SUPER_ADMIN)
                self.assertEqual(summary['permissions_count'], 2)
                self.assertIn('cache_info', summary)

    def test_ajax_permission_denied(self):
        """测试AJAX请求的权限拒绝处理"""
        @permission_required('products', 'delete')
        def test_view(request):
            return HttpResponse('Success')
        
        request = self.factory.get('/test/')
        request.user = self.user
        request.META['HTTP_X_REQUESTED_WITH'] = 'XMLHttpRequest'
        self._add_session_and_messages(request)
        
        with patch('users.permissions.PermissionManager.user_has_permission', return_value=False):
            response = test_view(request)
            self.assertEqual(response.status_code, 403)
            self.assertEqual(response['Content-Type'], 'application/json')
