"""
Django settings for warehouse_mvp project.

Generated by 'django-admin startproject' using Django 4.1.2.

For more information on this file, see
https://docs.djangoproject.com/en/4.1/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/4.1/ref/settings/
"""

from pathlib import Path
from decouple import config, Csv

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.1/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = config('SECRET_KEY')

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = config('DEBUG', default=False, cast=bool)

ALLOWED_HOSTS = config('ALLOWED_HOSTS', default='', cast=Csv())


# Application definition

INSTALLED_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    # 自定义应用
    "users",
    "products",
    "inventory",
    "transactions",
]

MIDDLEWARE = [
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    "users.middleware.PagePermissionMiddleware",  # 页面权限检查中间件
    "users.services.AuditMiddleware",  # 审计中间件（线程本地存储）
    "users.middleware.UserActivityMiddleware",  # 用户活动中间件
]

ROOT_URLCONF = "warehouse_mvp.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "warehouse_mvp.wsgi.application"


# Database
# https://docs.djangoproject.com/en/4.1/ref/settings/#databases

# MySQL 数据库配置
DATABASES = {
    "default": {
        "ENGINE": config('DATABASE_ENGINE', default='django.db.backends.mysql'),
        "NAME": config('MYSQL_NAME'),
        "USER": config('MYSQL_USER'),
        "PASSWORD": config('MYSQL_PASSWORD'),
        "HOST": config('MYSQL_HOST'),
        "PORT": config('MYSQL_PORT', default='3306'),
        "OPTIONS": {
            "charset": "utf8mb4",
        },
    }
}


# Password validation
# https://docs.djangoproject.com/en/4.1/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]


# Internationalization
# https://docs.djangoproject.com/en/4.1/topics/i18n/

LANGUAGE_CODE = config('LANGUAGE_CODE', default='zh-hans')

TIME_ZONE = config('TIME_ZONE', default='Asia/Shanghai')

USE_I18N = True

# 修改为False，使Django直接存储本地时间（北京时间）到数据库
# 这样可以避免UTC时间转换问题，简化查询逻辑
USE_TZ = False


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.1/howto/static-files/

STATIC_URL = config('STATIC_URL', default='static/')

# 静态文件目录配置
STATICFILES_DIRS = [
    BASE_DIR / "static",
]

# 生产环境静态文件收集目录
STATIC_ROOT = BASE_DIR / config('STATIC_ROOT', default='staticfiles')

# Default primary key field type
# https://docs.djangoproject.com/en/4.1/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

# 日志配置
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
        },
    },
    'loggers': {
        'products.admin': {
            'handlers': ['console'],
            'level': 'INFO',
            'propagate': True,
        },
        'users.services': {
            'handlers': ['console'],
            'level': 'INFO',
            'propagate': True,
        },
        'users.signals': {
            'handlers': ['console'],
            'level': 'INFO',
            'propagate': True,
        },
    },
}

# 登录相关设置
LOGIN_URL = config('LOGIN_URL', default='/users/login/')
LOGIN_REDIRECT_URL = config('LOGIN_REDIRECT_URL', default='/products/')
LOGOUT_REDIRECT_URL = config('LOGOUT_REDIRECT_URL', default='/users/login/')

# 页面权限中间件配置
PAGE_PERMISSION_ENABLE_CACHE = config('PAGE_PERMISSION_ENABLE_CACHE', default=True, cast=bool)  # 启用权限缓存
PAGE_PERMISSION_CACHE_TIMEOUT = config('PAGE_PERMISSION_CACHE_TIMEOUT', default=300, cast=int)  # 权限缓存超时时间（秒）

# 缓存配置
CACHES = {
    'default': {
        'BACKEND': config('CACHE_BACKEND', default='django.core.cache.backends.locmem.LocMemCache'),
        'LOCATION': config('CACHE_LOCATION', default='unique-snowflake'),
        'TIMEOUT': config('CACHE_TIMEOUT', default=300, cast=int),
        'OPTIONS': {
            'MAX_ENTRIES': config('CACHE_MAX_ENTRIES', default=1000, cast=int),
        }
    }
}

# 批量交易优化配置
BATCH_TRANSACTION_OPTIMIZATION = config('BATCH_TRANSACTION_OPTIMIZATION', default=True, cast=bool)
BATCH_TRANSACTION_SIZE = config('BATCH_TRANSACTION_SIZE', default=1000, cast=int)
BATCH_TRANSACTION_MONITORING = config('BATCH_TRANSACTION_MONITORING', default=True, cast=bool)
