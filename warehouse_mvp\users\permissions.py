"""
用户权限管理模块
定义角色权限矩阵和权限管理工具
"""
from django.contrib.auth.models import Group, Permission
from django.contrib.contenttypes.models import ContentType
from django.db import transaction
from django.core.cache import cache
from django.utils import timezone
from django.db.models import Q
import re
import logging

logger = logging.getLogger(__name__)


# 定义角色常量
class UserRoles:
    SUPER_ADMIN = 'super_admin'
    WAREHOUSE_MANAGER = 'warehouse_manager'
    WAREHOUSE_OPERATOR = 'warehouse_operator'
    VIEWER = 'viewer'
    
    CHOICES = [
        (SUPER_ADMIN, '超级管理员'),
        (WAREHOUSE_MANAGER, '仓库管理员'),
        (WAREHOUSE_OPERATOR, '仓库操作员'),
        (VIEWER, '查看员'),
    ]
    
    @classmethod
    def get_display_name(cls, role):
        """获取角色显示名称"""
        for code, name in cls.CHOICES:
            if code == role:
                return name
        return role


# 权限矩阵定义
PERMISSION_MATRIX = {
    # 商品管理权限
    'products': {
        UserRoles.SUPER_ADMIN: ['add', 'change', 'delete', 'view'],
        UserRoles.WAREHOUSE_MANAGER: ['add', 'change', 'delete', 'view'],
        UserRoles.WAREHOUSE_OPERATOR: ['view'],
        UserRoles.VIEWER: ['view'],
    },
    
    # 库存管理权限
    'inventory': {
        UserRoles.SUPER_ADMIN: ['add', 'change', 'delete', 'view'],
        UserRoles.WAREHOUSE_MANAGER: ['add', 'change', 'delete', 'view'],
        UserRoles.WAREHOUSE_OPERATOR: ['change', 'view'],  # 只能修改库存数量
        UserRoles.VIEWER: ['view'],
    },
    
    # 进出库记录权限
    'transactions': {
        UserRoles.SUPER_ADMIN: ['add', 'change', 'delete', 'view'],
        UserRoles.WAREHOUSE_MANAGER: ['add', 'change', 'delete', 'view'],
        UserRoles.WAREHOUSE_OPERATOR: ['add', 'change', 'view'],
        UserRoles.VIEWER: ['view'],
    },
    
    # 用户管理权限
    'users': {
        UserRoles.SUPER_ADMIN: ['add', 'change', 'delete', 'view'],
        UserRoles.WAREHOUSE_MANAGER: [],
        UserRoles.WAREHOUSE_OPERATOR: [],
        UserRoles.VIEWER: [],
    },
    
    # 操作日志查看权限
    'audit_log': {
        UserRoles.SUPER_ADMIN: ['view'],
        UserRoles.WAREHOUSE_MANAGER: ['view'],
        UserRoles.WAREHOUSE_OPERATOR: [],
        UserRoles.VIEWER: [],
    }
}


class PermissionManager:
    """权限管理器"""
    
    @staticmethod
    def create_roles_and_permissions():
        """创建角色和权限"""
        with transaction.atomic():
            # 创建角色组
            for role_code, role_name in UserRoles.CHOICES:
                group, created = Group.objects.get_or_create(
                    name=role_code,
                    defaults={'name': role_code}
                )
                if created:
                    print(f"创建角色组: {role_name} ({role_code})")
            
            # 分配权限
            PermissionManager._assign_permissions()
    
    @staticmethod
    def _assign_permissions():
        """为角色分配权限"""
        # 获取内容类型
        content_types = {
            'products': ContentType.objects.get(app_label='products', model='product'),
            'inventory': ContentType.objects.get(app_label='inventory', model='inventory'),
            'transactions': ContentType.objects.get(app_label='transactions', model='transaction'),
            'users': ContentType.objects.get(app_label='auth', model='user'),
        }
        
        # 为每个角色分配权限
        for role_code, role_name in UserRoles.CHOICES:
            group = Group.objects.get(name=role_code)
            group.permissions.clear()  # 清除现有权限
            
            # 根据权限矩阵分配权限
            for app_name, role_permissions in PERMISSION_MATRIX.items():
                if app_name in content_types:
                    content_type = content_types[app_name]
                    permissions = role_permissions.get(role_code, [])
                    
                    for perm_action in permissions:
                        perm_codename = f"{perm_action}_{content_type.model}"
                        try:
                            permission = Permission.objects.get(
                                codename=perm_codename,
                                content_type=content_type
                            )
                            group.permissions.add(permission)
                            print(f"为 {role_name} 添加权限: {perm_codename}")
                        except Permission.DoesNotExist:
                            print(f"权限不存在: {perm_codename}")
    
    @staticmethod
    def assign_user_to_role(user, role_code):
        """将用户分配到角色"""
        try:
            # 移除用户的所有角色组
            user.groups.clear()
            
            # 添加新角色组
            if role_code:
                group = Group.objects.get(name=role_code)
                user.groups.add(group)
                
                # 更新用户资料中的角色字段
                from .models import UserProfile
                profile, created = UserProfile.objects.get_or_create(user=user)
                profile.role = role_code
                profile.save()
                
                print(f"用户 {user.username} 已分配到角色: {UserRoles.get_display_name(role_code)}")
                return True
        except Group.DoesNotExist:
            print(f"角色组不存在: {role_code}")
            return False
        except Exception as e:
            print(f"分配角色失败: {str(e)}")
            return False
    
    @staticmethod
    def get_user_role(user):
        """获取用户角色"""
        try:
            from .models import UserProfile
            profile = UserProfile.objects.get(user=user)
            return profile.role
        except UserProfile.DoesNotExist:
            return None
    
    @staticmethod
    def user_has_permission(user, app_name, action):
        """检查用户是否有特定权限"""
        if user.is_superuser:
            return True
            
        user_role = PermissionManager.get_user_role(user)
        if not user_role:
            return False
            
        permissions = PERMISSION_MATRIX.get(app_name, {}).get(user_role, [])
        return action in permissions
    
    @staticmethod
    def get_role_permissions(role_code):
        """获取角色的所有权限"""
        permissions = {}
        for app_name, role_permissions in PERMISSION_MATRIX.items():
            permissions[app_name] = role_permissions.get(role_code, [])
        return permissions

    # ==================== 新增的页面权限检查方法 ====================

    @staticmethod
    def user_has_page_permission(user, url_path, url_name=None):
        """检查用户是否有页面访问权限"""
        if user.is_superuser:
            return True

        # 首先检查缓存
        cache_key = f"page_permission_{user.id}_{url_path}"
        cached_result = cache.get(cache_key)
        if cached_result is not None:
            return cached_result

        # 获取用户角色
        user_role = PermissionManager.get_user_role(user)
        if not user_role:
            cache.set(cache_key, False, 300)  # 缓存5分钟
            return False

        # 检查页面权限
        result = PermissionManager._check_page_permission(user, user_role, url_path, url_name)

        # 缓存结果
        cache.set(cache_key, result, 300)  # 缓存5分钟
        return result

    @staticmethod
    def check_page_permission(user, app_name, permission_name):
        """检查用户是否有特定页面权限"""
        if user.is_superuser:
            return True

        # 首先检查缓存
        cache_key = f"page_perm_{user.id}_{app_name}_{permission_name}"
        cached_result = cache.get(cache_key)
        if cached_result is not None:
            return cached_result

        # 获取用户角色
        user_role = PermissionManager.get_user_role(user)
        if not user_role:
            return False

        # 检查页面权限
        result = PermissionManager._check_specific_page_permission(user, user_role, app_name, permission_name)

        # 缓存结果
        cache.set(cache_key, result, 300)  # 缓存5分钟
        return result

    @staticmethod
    def check_custom_permission(user, permission_key):
        """检查用户是否有自定义权限"""
        if user.is_superuser:
            return True

        # 首先检查缓存
        cache_key = f"custom_perm_{user.id}_{permission_key}"
        cached_result = cache.get(cache_key)
        if cached_result is not None:
            return cached_result

        # 检查自定义权限
        from .models import CustomPermission

        try:
            custom_perm = CustomPermission.objects.get(
                user=user,
                permission_key=permission_key,
                is_granted__isnull=False
            )

            if custom_perm.is_valid():
                result = custom_perm.is_granted
            else:
                result = False
        except CustomPermission.DoesNotExist:
            result = False

        # 缓存结果
        cache.set(cache_key, result, 300)  # 缓存5分钟
        return result

    @staticmethod
    def _check_page_permission(user, user_role, url_path, url_name=None):
        """内部方法：检查页面权限"""
        from .models import PagePermission, RolePagePermission, CustomPermission

        # 1. 检查用户自定义权限（最高优先级）
        custom_permissions = CustomPermission.objects.filter(
            user=user,
            permission_type='page',
            is_granted__isnull=False
        ).order_by('-priority')

        for custom_perm in custom_permissions:
            if custom_perm.is_valid():
                permission_key = custom_perm.permission_key
                if permission_key.startswith('page.') and PermissionManager._matches_permission_key(permission_key, url_path, url_name):
                    return custom_perm.is_granted

        # 2. 检查角色页面权限
        page_permissions = PagePermission.objects.filter(
            is_active=True,
            permission_level__in=['role_based', 'authenticated']
        ).order_by('-priority')

        for page_perm in page_permissions:
            if page_perm.matches_url(url_path) or (url_name and page_perm.url_name == url_name):
                # 检查角色权限
                try:
                    role_perm = RolePagePermission.objects.get(
                        role=user_role,
                        page_permission=page_perm
                    )
                    if role_perm.is_valid():
                        return role_perm.is_granted
                except RolePagePermission.DoesNotExist:
                    # 如果没有明确的角色权限配置，检查权限级别
                    if page_perm.permission_level == 'authenticated':
                        return True  # 登录用户即可访问
                    continue

        # 3. 检查公开权限
        public_permissions = PagePermission.objects.filter(
            is_active=True,
            permission_level='public'
        )

        for page_perm in public_permissions:
            if page_perm.matches_url(url_path) or (url_name and page_perm.url_name == url_name):
                return True

        # 4. 默认拒绝
        return False

    @staticmethod
    def _check_specific_page_permission(user, user_role, app_name, permission_name):
        """内部方法：检查特定页面权限"""
        from .models import PagePermission, RolePagePermission, CustomPermission

        # 1. 检查用户自定义权限（最高优先级）
        permission_key = f"page.{app_name}.{permission_name}"
        custom_permissions = CustomPermission.objects.filter(
            user=user,
            permission_key=permission_key,
            is_granted__isnull=False
        ).order_by('-priority', '-granted_at')

        for custom_perm in custom_permissions:
            if custom_perm.is_valid():
                return custom_perm.is_granted

        # 2. 检查角色页面权限
        try:
            page_permission = PagePermission.objects.get(
                name=permission_name,
                app_name=app_name,
                is_active=True
            )

            role_permission = RolePagePermission.objects.get(
                role=user_role,
                page_permission=page_permission,
                is_granted=True
            )

            if role_permission.is_valid():
                return True
        except (PagePermission.DoesNotExist, RolePagePermission.DoesNotExist):
            pass

        # 3. 默认拒绝
        return False

    @staticmethod
    def _matches_permission_key(permission_key, url_path, url_name=None):
        """检查权限键是否匹配URL"""
        # 简化的匹配逻辑，可以根据需要扩展
        if url_name and permission_key.endswith(url_name):
            return True

        # 基于URL路径的模糊匹配
        parts = permission_key.split('.')
        if len(parts) >= 3:
            app_name = parts[1]
            return app_name in url_path

        return False

    # ==================== 权限缓存管理方法 ====================

    @staticmethod
    def clear_user_permission_cache(user):
        """清除用户权限缓存"""
        # 清除Redis缓存
        cache_pattern = f"*permission_{user.id}_*"
        cache.delete_many(cache.get_many(cache_pattern).keys())

        # 清除数据库缓存
        try:
            from .models import UserProfile
            profile = UserProfile.objects.get(user=user)
            profile.clear_permission_cache()
        except UserProfile.DoesNotExist:
            pass

    @staticmethod
    def refresh_user_permissions(user):
        """刷新用户权限缓存"""
        PermissionManager.clear_user_permission_cache(user)

        # 重新计算并缓存权限
        permissions = PermissionManager.get_user_all_permissions(user)

        try:
            from .models import UserProfile
            profile = UserProfile.objects.get(user=user)
            profile.update_permission_cache(permissions)
        except UserProfile.DoesNotExist:
            pass

        return permissions

    @staticmethod
    def get_user_all_permissions(user):
        """获取用户的所有权限"""
        if user.is_superuser:
            return {'is_superuser': True}

        permissions = {
            'role_permissions': {},
            'page_permissions': {},
            'custom_permissions': {},
            'computed_at': timezone.now().isoformat()
        }

        # 获取角色权限
        user_role = PermissionManager.get_user_role(user)
        if user_role:
            permissions['role'] = user_role
            permissions['role_permissions'] = PermissionManager.get_role_permissions(user_role)

            # 获取角色页面权限
            permissions['page_permissions'] = PermissionManager._get_role_page_permissions(user_role)

        # 获取自定义权限
        permissions['custom_permissions'] = PermissionManager._get_user_custom_permissions(user)

        return permissions

    @staticmethod
    def _get_role_page_permissions(role_code):
        """获取角色的页面权限"""
        from .models import RolePagePermission

        role_permissions = {}
        role_perms = RolePagePermission.objects.filter(
            role=role_code,
            is_granted=True
        ).select_related('page_permission')

        for role_perm in role_perms:
            if role_perm.is_valid():
                page_perm = role_perm.page_permission
                key = page_perm.get_full_permission_key()
                role_permissions[key] = {
                    'granted': role_perm.is_granted,
                    'source': role_perm.grant_source,
                    'url_pattern': page_perm.url_pattern,
                    'app_name': page_perm.app_name,
                    'category': page_perm.category
                }

        return role_permissions

    @staticmethod
    def _get_user_custom_permissions(user):
        """获取用户的自定义权限"""
        from .models import CustomPermission

        custom_permissions = {}
        custom_perms = CustomPermission.objects.filter(
            user=user
        ).order_by('-priority')

        for custom_perm in custom_perms:
            if custom_perm.is_valid():
                custom_permissions[custom_perm.permission_key] = {
                    'granted': custom_perm.is_granted,
                    'type': custom_perm.permission_type,
                    'priority': custom_perm.priority,
                    'value': custom_perm.permission_value,
                    'reason': custom_perm.grant_reason
                }

        return custom_permissions

    # ==================== 批量权限分配方法 ====================

    @staticmethod
    def batch_assign_role_permissions(role_code, permission_configs, granted_by=None):
        """批量分配角色权限"""
        from .models import PagePermission, RolePagePermission

        results = {
            'success': [],
            'failed': [],
            'updated': []
        }

        with transaction.atomic():
            for config in permission_configs:
                try:
                    permission_name = config.get('permission_name')
                    app_name = config.get('app_name')
                    is_granted = config.get('is_granted', True)

                    # 查找页面权限
                    page_permission = PagePermission.objects.get(
                        name=permission_name,
                        app_name=app_name
                    )

                    # 创建或更新角色权限
                    role_perm, created = RolePagePermission.objects.get_or_create(
                        role=role_code,
                        page_permission=page_permission,
                        defaults={
                            'is_granted': is_granted,
                            'grant_source': 'manual',
                            'granted_by': granted_by,
                            'notes': config.get('notes', '')
                        }
                    )

                    if created:
                        results['success'].append({
                            'permission': permission_name,
                            'action': 'created',
                            'granted': is_granted
                        })
                    else:
                        # 更新现有权限
                        role_perm.is_granted = is_granted
                        role_perm.granted_by = granted_by
                        role_perm.notes = config.get('notes', role_perm.notes)
                        role_perm.save()

                        results['updated'].append({
                            'permission': permission_name,
                            'action': 'updated',
                            'granted': is_granted
                        })

                except PagePermission.DoesNotExist:
                    results['failed'].append({
                        'permission': permission_name,
                        'error': 'PagePermission not found'
                    })
                except Exception as e:
                    results['failed'].append({
                        'permission': permission_name,
                        'error': str(e)
                    })

        # 清除相关用户的权限缓存
        PermissionManager._clear_role_users_cache(role_code)

        return results

    @staticmethod
    def batch_revoke_role_permissions(role_code, permission_names, revoked_by=None):
        """批量撤销角色权限"""
        from .models import RolePagePermission, PagePermission

        results = {
            'success': [],
            'failed': []
        }

        with transaction.atomic():
            for permission_name in permission_names:
                try:
                    # 查找并删除角色权限
                    role_perms = RolePagePermission.objects.filter(
                        role=role_code,
                        page_permission__name=permission_name
                    )

                    count = role_perms.count()
                    role_perms.delete()

                    results['success'].append({
                        'permission': permission_name,
                        'count': count
                    })

                except Exception as e:
                    results['failed'].append({
                        'permission': permission_name,
                        'error': str(e)
                    })

        # 清除相关用户的权限缓存
        PermissionManager._clear_role_users_cache(role_code)

        return results

    @staticmethod
    def _clear_role_users_cache(role_code):
        """清除角色下所有用户的权限缓存"""
        from .models import UserProfile

        users = UserProfile.objects.filter(role=role_code).select_related('user')
        for profile in users:
            PermissionManager.clear_user_permission_cache(profile.user)

    # ==================== 权限冲突检测和解决方法 ====================

    @staticmethod
    def detect_permission_conflicts(user):
        """检测用户权限冲突"""
        conflicts = []

        # 获取用户的所有权限
        user_role = PermissionManager.get_user_role(user)
        if not user_role:
            return conflicts

        from .models import CustomPermission, RolePagePermission

        # 获取用户自定义权限
        custom_perms = CustomPermission.objects.filter(user=user)

        for custom_perm in custom_perms:
            if not custom_perm.is_valid():
                continue

            permission_key = custom_perm.permission_key

            # 检查是否与角色权限冲突
            if permission_key.startswith('page.'):
                parts = permission_key.split('.')
                if len(parts) >= 3:
                    app_name = parts[1]
                    permission_name = '.'.join(parts[2:])

                    try:
                        role_perms = RolePagePermission.objects.filter(
                            role=user_role,
                            page_permission__app_name=app_name,
                            page_permission__name=permission_name
                        )

                        for role_perm in role_perms:
                            if role_perm.is_valid() and role_perm.is_granted != custom_perm.is_granted:
                                conflicts.append({
                                    'type': 'role_custom_conflict',
                                    'permission_key': permission_key,
                                    'role_granted': role_perm.is_granted,
                                    'custom_granted': custom_perm.is_granted,
                                    'custom_priority': custom_perm.priority,
                                    'resolution': 'custom_wins' if custom_perm.priority > 0 else 'role_wins'
                                })
                    except Exception:
                        continue

        return conflicts

    @staticmethod
    def resolve_permission_conflicts(user, resolution_strategy='priority_based'):
        """解决权限冲突"""
        conflicts = PermissionManager.detect_permission_conflicts(user)
        resolved = []

        for conflict in conflicts:
            if resolution_strategy == 'priority_based':
                # 基于优先级解决冲突
                if conflict['resolution'] == 'custom_wins':
                    resolved.append({
                        'conflict': conflict,
                        'action': 'keep_custom',
                        'reason': 'Custom permission has higher priority'
                    })
                else:
                    resolved.append({
                        'conflict': conflict,
                        'action': 'remove_custom',
                        'reason': 'Role permission takes precedence'
                    })
            elif resolution_strategy == 'explicit_deny':
                # 明确拒绝策略：拒绝权限优先
                if not conflict['role_granted'] or not conflict['custom_granted']:
                    resolved.append({
                        'conflict': conflict,
                        'action': 'deny_access',
                        'reason': 'Explicit deny takes precedence'
                    })

        return resolved

    # ==================== 权限预览和测试方法 ====================

    @staticmethod
    def preview_role_permissions(role_code):
        """预览角色权限"""
        from .models import RolePagePermission

        preview = {
            'role': role_code,
            'role_name': UserRoles.get_display_name(role_code),
            'traditional_permissions': PermissionManager.get_role_permissions(role_code),
            'page_permissions': {},
            'total_permissions': 0
        }

        # 获取页面权限
        role_perms = RolePagePermission.objects.filter(
            role=role_code,
            is_granted=True
        ).select_related('page_permission')

        for role_perm in role_perms:
            if role_perm.is_valid():
                page_perm = role_perm.page_permission
                category = page_perm.category

                if category not in preview['page_permissions']:
                    preview['page_permissions'][category] = []

                preview['page_permissions'][category].append({
                    'name': page_perm.name,
                    'description': page_perm.description,
                    'url_pattern': page_perm.url_pattern,
                    'app_name': page_perm.app_name,
                    'permission_level': page_perm.permission_level,
                    'grant_source': role_perm.grant_source
                })

                preview['total_permissions'] += 1

        return preview

    @staticmethod
    def test_user_access(user, test_urls):
        """测试用户对指定URL的访问权限"""
        results = {}

        for url_path in test_urls:
            try:
                has_access = PermissionManager.user_has_page_permission(user, url_path)
                results[url_path] = {
                    'access': has_access,
                    'method': 'page_permission_check'
                }
            except Exception as e:
                results[url_path] = {
                    'access': False,
                    'error': str(e),
                    'method': 'error'
                }

        return results

    # ==================== 审计日志集成方法 ====================

    @staticmethod
    def log_permission_change(user, action, permission_data, operator=None, request=None):
        """记录权限变更的审计日志"""
        try:
            from .services import AuditService

            extra_data = {
                'permission_action': action,
                'permission_data': permission_data,
                'operator': operator.username if operator else 'system'
            }

            AuditService.log_custom_action(
                action=f'PERMISSION_{action.upper()}',
                instance=user,
                user=operator or user,
                request=request,
                changes=permission_data,
                extra_data=extra_data
            )
        except Exception as e:
            logger.error(f"Failed to log permission change: {str(e)}")

    @staticmethod
    def log_role_assignment(user, old_role, new_role, operator=None, request=None):
        """记录角色分配的审计日志"""
        changes = {
            'old_role': old_role,
            'new_role': new_role,
            'role_change': True
        }

        PermissionManager.log_permission_change(
            user=user,
            action='role_assignment',
            permission_data=changes,
            operator=operator,
            request=request
        )

    @staticmethod
    def log_permission_grant(user, permission_key, granted, operator=None, request=None):
        """记录权限授予/撤销的审计日志"""
        changes = {
            'permission_key': permission_key,
            'granted': granted,
            'action': 'grant' if granted else 'revoke'
        }

        PermissionManager.log_permission_change(
            user=user,
            action='permission_grant',
            permission_data=changes,
            operator=operator,
            request=request
        )

    # ==================== 权限系统统计和监控方法 ====================

    @staticmethod
    def get_permission_statistics():
        """获取权限系统统计信息"""
        from .models import PagePermission, RolePagePermission, CustomPermission, UserProfile

        stats = {
            'total_page_permissions': PagePermission.objects.filter(is_active=True).count(),
            'total_role_permissions': RolePagePermission.objects.filter(is_granted=True).count(),
            'total_custom_permissions': CustomPermission.objects.filter(is_granted=True).count(),
            'users_by_role': {},
            'permissions_by_category': {},
            'recent_changes': []
        }

        # 按角色统计用户数
        for role_code, role_name in UserRoles.CHOICES:
            count = UserProfile.objects.filter(role=role_code).count()
            stats['users_by_role'][role_name] = count

        # 按分类统计权限数
        from django.db.models import Count
        categories = PagePermission.objects.filter(is_active=True).values('category').annotate(
            count=Count('id')
        )
        for category in categories:
            stats['permissions_by_category'][category['category']] = category['count']

        return stats

    @staticmethod
    def validate_permission_integrity():
        """验证权限系统完整性"""
        from .models import PagePermission, RolePagePermission

        issues = []

        # 检查孤立的角色权限
        orphaned_role_perms = RolePagePermission.objects.filter(
            page_permission__isnull=True
        )
        if orphaned_role_perms.exists():
            issues.append({
                'type': 'orphaned_role_permissions',
                'count': orphaned_role_perms.count(),
                'description': 'Role permissions without valid page permissions'
            })

        # 检查无效的URL模式
        invalid_patterns = []
        for page_perm in PagePermission.objects.filter(is_active=True):
            try:
                re.compile(page_perm.url_pattern)
            except re.error:
                invalid_patterns.append(page_perm.name)

        if invalid_patterns:
            issues.append({
                'type': 'invalid_url_patterns',
                'permissions': invalid_patterns,
                'description': 'Page permissions with invalid regex patterns'
            })

        return {
            'is_valid': len(issues) == 0,
            'issues': issues,
            'checked_at': timezone.now().isoformat()
        }


def init_permissions():
    """初始化权限系统"""
    print("开始初始化权限系统...")
    PermissionManager.create_roles_and_permissions()
    print("权限系统初始化完成！")
