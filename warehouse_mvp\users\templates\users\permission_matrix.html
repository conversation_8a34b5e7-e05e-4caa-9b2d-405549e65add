{% extends "products/base.html" %}
{% load static %}

{% block title %}权限矩阵 - 仓库管理系统{% endblock %}

{% block extra_css %}
<style>
.matrix-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.matrix-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9rem;
}

.matrix-table th,
.matrix-table td {
    padding: 0.5rem;
    text-align: center;
    border: 1px solid #dee2e6;
}

.matrix-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    position: sticky;
    top: 0;
    z-index: 10;
}

.matrix-table th.permission-header {
    background-color: #e3f2fd;
    writing-mode: vertical-rl;
    text-orientation: mixed;
    min-width: 120px;
    max-width: 120px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.matrix-table th.role-header {
    background-color: #f3e5f5;
    min-width: 150px;
}

.matrix-table td.permission-cell {
    cursor: pointer;
    /* 移除transition动画效果 */
    user-select: none;
}

.permission-granted {
    background-color: #e8f5e8;
    color: #2e7d32;
}

.permission-denied {
    background-color: #ffebee;
    color: #c62828;
}

.permission-cell:hover {
    /* 移除transform动画效果 */
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    z-index: 5;
    position: relative;
}

.category-header {
    background-color: #fff3e0 !important;
    color: #e65100;
    font-weight: bold;
    text-align: left !important;
    padding-left: 1rem !important;
}

.category-divider {
    height: 2px;
    background-color: #e0e0e0;
}

.matrix-controls {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 1rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.legend {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.legend-color {
    width: 20px;
    height: 20px;
    border-radius: 4px;
    border: 1px solid #dee2e6;
}

.table-wrapper {
    overflow: auto;
    max-height: 70vh;
    border: 1px solid #dee2e6;
    border-radius: 4px;
}

.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.permission-tooltip {
    position: absolute;
    background: #333;
    color: white;
    padding: 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    z-index: 1000;
    pointer-events: none;
    max-width: 200px;
}

@media (max-width: 768px) {
    .matrix-controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .filter-group {
        justify-content: space-between;
    }
    
    .matrix-table th.permission-header {
        min-width: 80px;
        max-width: 80px;
        font-size: 0.8rem;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">权限矩阵</h1>
        <div class="btn-group">
            <a href="{% url 'users:permission_dashboard' %}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> 返回仪表板
            </a>
            <button type="button" class="btn btn-primary" onclick="saveChanges()">
                <i class="fas fa-save"></i> 保存更改
            </button>
        </div>
    </div>

    <div class="matrix-container">
        <div class="matrix-controls">
            <div class="filter-group">
                <label for="categoryFilter">分类筛选:</label>
                <select id="categoryFilter" class="form-select form-select-sm" onchange="filterByCategory()">
                    <option value="">全部分类</option>
                    {% for category in categories %}
                    <option value="{{ category }}">{{ category|capfirst }}</option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="filter-group">
                <label for="roleFilter">角色筛选:</label>
                <select id="roleFilter" class="form-select form-select-sm" onchange="filterByRole()">
                    <option value="">全部角色</option>
                    {% for role_code, role_name in roles %}
                    <option value="{{ role_code }}">{{ role_name }}</option>
                    {% endfor %}
                </select>
            </div>

            <div class="legend">
                <div class="legend-item">
                    <div class="legend-color permission-granted"></div>
                    <span>已授权</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color permission-denied"></div>
                    <span>未授权</span>
                </div>
            </div>
        </div>

        <div class="table-wrapper" id="matrixWrapper">
            <table class="matrix-table" id="permissionMatrix">
                <thead>
                    <tr>
                        <th class="role-header">权限 / 角色</th>
                        {% for role_code, role_name in roles %}
                        <th class="permission-header role-{{ role_code }}">{{ role_name }}</th>
                        {% endfor %}
                    </tr>
                </thead>
                <tbody>
                    {% regroup permissions by category as permission_groups %}
                    {% for group in permission_groups %}
                    <tr class="category-row">
                        <td class="category-header" colspan="{{ roles|length|add:1 }}">
                            <i class="fas fa-folder"></i> {{ group.grouper|capfirst|default:"未分类" }}
                        </td>
                    </tr>
                    {% for permission in group.list %}
                    <tr class="permission-row" data-category="{{ permission.category }}" data-permission-id="{{ permission.id }}">
                        <td class="permission-name" title="{{ permission.description }}">
                            <strong>{{ permission.name }}</strong>
                            <br><small class="text-muted">{{ permission.app_name }}</small>
                        </td>
                        {% for role_code, role_name in roles %}
                        <td class="permission-cell {% if matrix|lookup:permission.id|lookup:role_code %}permission-granted{% else %}permission-denied{% endif %}"
                            data-role="{{ role_code }}"
                            data-permission="{{ permission.id }}"
                            onclick="togglePermission(this)"
                            title="点击切换 {{ role_name }} 对 {{ permission.name }} 的权限">
                            {% if matrix|lookup:permission.id|lookup:role_code %}
                                <i class="fas fa-check"></i>
                            {% else %}
                                <i class="fas fa-times"></i>
                            {% endif %}
                        </td>
                        {% endfor %}
                    </tr>
                    {% endfor %}
                    {% if not forloop.last %}
                    <tr class="category-divider">
                        <td colspan="{{ roles|length|add:1 }}"></td>
                    </tr>
                    {% endif %}
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <!-- 加载遮罩 -->
    <div class="loading-overlay" id="loadingOverlay" style="display: none;">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">加载中...</span>
        </div>
    </div>

    <!-- 权限提示框 -->
    <div class="permission-tooltip" id="permissionTooltip" style="display: none;"></div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let changedPermissions = new Set();

// 切换权限状态
function togglePermission(cell) {
    const role = cell.dataset.role;
    const permissionId = cell.dataset.permission;
    const isGranted = cell.classList.contains('permission-granted');
    
    // 切换视觉状态
    if (isGranted) {
        cell.classList.remove('permission-granted');
        cell.classList.add('permission-denied');
        cell.innerHTML = '<i class="fas fa-times"></i>';
    } else {
        cell.classList.remove('permission-denied');
        cell.classList.add('permission-granted');
        cell.innerHTML = '<i class="fas fa-check"></i>';
    }
    
    // 记录更改
    const changeKey = `${role}_${permissionId}`;
    changedPermissions.add(changeKey);
    
    // 更新保存按钮状态
    updateSaveButton();
}

// 按分类筛选
function filterByCategory() {
    const category = document.getElementById('categoryFilter').value;
    const rows = document.querySelectorAll('.permission-row');
    
    rows.forEach(row => {
        if (!category || row.dataset.category === category) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
    
    // 处理分类标题行
    const categoryRows = document.querySelectorAll('.category-row');
    categoryRows.forEach(row => {
        const categoryName = row.querySelector('.category-header').textContent.trim().toLowerCase();
        if (!category || categoryName.includes(category.toLowerCase())) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

// 按角色筛选
function filterByRole() {
    const role = document.getElementById('roleFilter').value;
    const headers = document.querySelectorAll('.permission-header');
    const cells = document.querySelectorAll('.permission-cell');
    
    if (!role) {
        // 显示所有角色
        headers.forEach(header => header.style.display = '');
        cells.forEach(cell => cell.style.display = '');
    } else {
        // 只显示选中的角色
        headers.forEach(header => {
            if (header.classList.contains(`role-${role}`)) {
                header.style.display = '';
            } else {
                header.style.display = 'none';
            }
        });
        
        cells.forEach(cell => {
            if (cell.dataset.role === role) {
                cell.style.display = '';
            } else {
                cell.style.display = 'none';
            }
        });
    }
}

// 保存更改
function saveChanges() {
    if (changedPermissions.size === 0) {
        showMessage('没有需要保存的更改', 'info');
        return;
    }
    
    showLoading(true);
    
    const promises = Array.from(changedPermissions).map(changeKey => {
        const [role, permissionId] = changeKey.split('_');
        
        return fetch('{% url "users:ajax_toggle_role_permission" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            body: JSON.stringify({
                role: role,
                permission_id: permissionId
            })
        }).then(response => response.json());
    });
    
    Promise.all(promises)
        .then(results => {
            const failures = results.filter(result => !result.success);
            
            if (failures.length === 0) {
                showMessage('所有权限更改已保存', 'success');
                changedPermissions.clear();
                updateSaveButton();
            } else {
                showMessage(`保存完成，但有 ${failures.length} 个更改失败`, 'warning');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showMessage('保存失败，请重试', 'error');
        })
        .finally(() => {
            showLoading(false);
        });
}

// 更新保存按钮状态
function updateSaveButton() {
    const saveBtn = document.querySelector('button[onclick="saveChanges()"]');
    if (changedPermissions.size > 0) {
        saveBtn.classList.remove('btn-primary');
        saveBtn.classList.add('btn-warning');
        saveBtn.innerHTML = `<i class="fas fa-save"></i> 保存更改 (${changedPermissions.size})`;
    } else {
        saveBtn.classList.remove('btn-warning');
        saveBtn.classList.add('btn-primary');
        saveBtn.innerHTML = '<i class="fas fa-save"></i> 保存更改';
    }
}

// 显示加载状态
function showLoading(show) {
    document.getElementById('loadingOverlay').style.display = show ? 'flex' : 'none';
}

// 显示消息
function showMessage(message, type) {
    // 这里可以集成现有的消息系统
    const alertClass = {
        'success': 'alert-success',
        'error': 'alert-danger',
        'warning': 'alert-warning',
        'info': 'alert-info'
    }[type] || 'alert-info';
    
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert ${alertClass} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.querySelector('.container-fluid').insertBefore(alertDiv, document.querySelector('.matrix-container'));
    
    // 自动消失
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}

// 获取CSRF Token
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

// 键盘快捷键
document.addEventListener('keydown', function(e) {
    if (e.ctrlKey && e.key === 's') {
        e.preventDefault();
        saveChanges();
    }
});

// 页面离开提醒
window.addEventListener('beforeunload', function(e) {
    if (changedPermissions.size > 0) {
        e.preventDefault();
        e.returnValue = '您有未保存的更改，确定要离开吗？';
    }
});
</script>
{% endblock %}
