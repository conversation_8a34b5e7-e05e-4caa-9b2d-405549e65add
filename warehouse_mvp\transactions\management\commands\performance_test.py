"""
批量交易性能测试管理命令
用于在生产环境中运行性能测试和基准测试
"""
import time
import json
from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.db import connection
from django.conf import settings
from products.models import Product
from inventory.models import Inventory
from transactions.models import Transaction
from transactions.services import BatchTransactionService
from transactions.views import BatchUploadView
from users.models import AuditLog


class Command(BaseCommand):
    help = '运行批量交易性能测试'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--sizes',
            type=str,
            default='10,50,100',
            help='测试批量大小，用逗号分隔 (默认: 10,50,100)'
        )
        parser.add_argument(
            '--iterations',
            type=int,
            default=3,
            help='每个测试的迭代次数 (默认: 3)'
        )
        parser.add_argument(
            '--output',
            type=str,
            help='输出结果到JSON文件'
        )
        parser.add_argument(
            '--cleanup',
            action='store_true',
            help='测试后清理数据'
        )
    
    def handle(self, *args, **options):
        """执行性能测试"""
        self.stdout.write(self.style.SUCCESS('开始批量交易性能测试...'))
        
        # 解析测试大小
        sizes = [int(s.strip()) for s in options['sizes'].split(',')]
        iterations = options['iterations']
        
        # 准备测试环境
        self.setup_test_environment()
        
        # 运行测试
        results = []
        for size in sizes:
            self.stdout.write(f'\n测试批量大小: {size}')
            size_results = []
            
            for i in range(iterations):
                self.stdout.write(f'  迭代 {i+1}/{iterations}')
                result = self.run_single_test(size)
                size_results.append(result)
                
                # 清理数据准备下次测试
                self.cleanup_test_data()
            
            # 计算平均结果
            avg_result = self.calculate_average_results(size_results, size)
            results.append(avg_result)
            
            # 显示结果
            self.display_results(avg_result)
        
        # 输出汇总报告
        self.display_summary_report(results)
        
        # 保存结果到文件
        if options['output']:
            self.save_results_to_file(results, options['output'])
        
        # 清理测试数据
        if options['cleanup']:
            self.cleanup_all_test_data()
        
        self.stdout.write(self.style.SUCCESS('性能测试完成!'))
    
    def setup_test_environment(self):
        """设置测试环境"""
        # 创建测试用户
        self.test_user, created = User.objects.get_or_create(
            username='performance_test_user',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'Performance',
                'last_name': 'Test'
            }
        )
        
        # 确保有足够的测试商品
        existing_products = Product.objects.count()
        needed_products = max(500 - existing_products, 0)
        
        if needed_products > 0:
            self.stdout.write(f'创建 {needed_products} 个测试商品...')
            products_to_create = []
            for i in range(needed_products):
                product = Product(
                    name=f'性能测试商品{existing_products + i + 1}',
                    sku=f'PERF{existing_products + i + 1:04d}',
                    description=f'性能测试商品{existing_products + i + 1}',
                    price=100.00
                )
                products_to_create.append(product)
            
            Product.objects.bulk_create(products_to_create)
            
            # 创建对应的库存记录
            inventories_to_create = []
            for product in products_to_create:
                inventory = Inventory(
                    product=product,
                    quantity=10000,  # 大量初始库存
                    min_stock=100,
                    max_stock=20000
                )
                inventories_to_create.append(inventory)
            
            Inventory.objects.bulk_create(inventories_to_create)
    
    def run_single_test(self, batch_size):
        """运行单次测试"""
        # 准备测试数据
        test_data = self.create_test_data(batch_size)
        
        # 测试原版本
        legacy_result = self.test_legacy_version(test_data)
        
        # 清理数据
        self.cleanup_test_data()
        
        # 测试优化版本
        optimized_result = self.test_optimized_version(test_data)
        
        return {
            'batch_size': batch_size,
            'legacy': legacy_result,
            'optimized': optimized_result
        }
    
    def create_test_data(self, count):
        """创建测试数据"""
        products = list(Product.objects.all()[:count])
        test_data = []
        
        for i, product in enumerate(products):
            test_data.append({
                'product': product,
                'transaction_type': '入库' if i % 2 == 0 else '出库',
                'quantity': 10,
                'reason': '性能测试',
                'notes': f'性能测试数据{i+1}',
                'sku': product.sku
            })
        
        return test_data
    
    def test_legacy_version(self, test_data):
        """测试原版本"""
        initial_queries = len(connection.queries)
        initial_transactions = Transaction.objects.count()
        initial_audit_logs = AuditLog.objects.count()
        
        view = BatchUploadView()
        start_time = time.time()
        result = view.create_transactions_batch_legacy(test_data, self.test_user)
        end_time = time.time()
        
        final_queries = len(connection.queries)
        final_transactions = Transaction.objects.count()
        final_audit_logs = AuditLog.objects.count()
        
        return {
            'processing_time': end_time - start_time,
            'query_count': final_queries - initial_queries,
            'created_count': result['created_count'],
            'skipped_count': result['skipped_count'],
            'transactions_created': final_transactions - initial_transactions,
            'audit_logs_created': final_audit_logs - initial_audit_logs
        }
    
    def test_optimized_version(self, test_data):
        """测试优化版本"""
        initial_queries = len(connection.queries)
        initial_transactions = Transaction.objects.count()
        initial_audit_logs = AuditLog.objects.count()
        
        start_time = time.time()
        result = BatchTransactionService.create_batch_optimized(test_data, self.test_user)
        end_time = time.time()
        
        final_queries = len(connection.queries)
        final_transactions = Transaction.objects.count()
        final_audit_logs = AuditLog.objects.count()
        
        return {
            'processing_time': end_time - start_time,
            'query_count': final_queries - initial_queries,
            'created_count': result['created_count'],
            'skipped_count': result['skipped_count'],
            'transactions_created': final_transactions - initial_transactions,
            'audit_logs_created': final_audit_logs - initial_audit_logs
        }
    
    def calculate_average_results(self, results, batch_size):
        """计算平均结果"""
        legacy_times = [r['legacy']['processing_time'] for r in results]
        optimized_times = [r['optimized']['processing_time'] for r in results]
        legacy_queries = [r['legacy']['query_count'] for r in results]
        optimized_queries = [r['optimized']['query_count'] for r in results]
        legacy_audits = [r['legacy']['audit_logs_created'] for r in results]
        optimized_audits = [r['optimized']['audit_logs_created'] for r in results]
        
        return {
            'batch_size': batch_size,
            'legacy': {
                'avg_processing_time': sum(legacy_times) / len(legacy_times),
                'avg_query_count': sum(legacy_queries) / len(legacy_queries),
                'avg_audit_logs': sum(legacy_audits) / len(legacy_audits)
            },
            'optimized': {
                'avg_processing_time': sum(optimized_times) / len(optimized_times),
                'avg_query_count': sum(optimized_queries) / len(optimized_queries),
                'avg_audit_logs': sum(optimized_audits) / len(optimized_audits)
            },
            'improvements': {
                'time_improvement': ((sum(legacy_times) - sum(optimized_times)) / sum(legacy_times) * 100),
                'query_reduction': ((sum(legacy_queries) - sum(optimized_queries)) / sum(legacy_queries) * 100),
                'audit_reduction': ((sum(legacy_audits) - sum(optimized_audits)) / sum(legacy_audits) * 100)
            }
        }
    
    def display_results(self, result):
        """显示测试结果"""
        batch_size = result['batch_size']
        legacy = result['legacy']
        optimized = result['optimized']
        improvements = result['improvements']
        
        self.stdout.write(f'\n批量大小: {batch_size}')
        self.stdout.write(f'  原版本平均时间: {legacy["avg_processing_time"]:.3f}秒')
        self.stdout.write(f'  优化版本平均时间: {optimized["avg_processing_time"]:.3f}秒')
        self.stdout.write(f'  时间改善: {improvements["time_improvement"]:.1f}%')
        self.stdout.write(f'  查询减少: {improvements["query_reduction"]:.1f}%')
        self.stdout.write(f'  审计日志减少: {improvements["audit_reduction"]:.1f}%')
    
    def display_summary_report(self, results):
        """显示汇总报告"""
        self.stdout.write(self.style.SUCCESS('\n=== 性能测试汇总报告 ==='))
        
        for result in results:
            improvements = result['improvements']
            self.stdout.write(f'\n批量大小 {result["batch_size"]}:')
            self.stdout.write(f'  时间改善: {improvements["time_improvement"]:.1f}%')
            self.stdout.write(f'  查询减少: {improvements["query_reduction"]:.1f}%')
            self.stdout.write(f'  审计减少: {improvements["audit_reduction"]:.1f}%')
        
        # 计算总体改善
        avg_time_improvement = sum(r['improvements']['time_improvement'] for r in results) / len(results)
        avg_query_reduction = sum(r['improvements']['query_reduction'] for r in results) / len(results)
        avg_audit_reduction = sum(r['improvements']['audit_reduction'] for r in results) / len(results)
        
        self.stdout.write(f'\n总体平均改善:')
        self.stdout.write(f'  平均时间改善: {avg_time_improvement:.1f}%')
        self.stdout.write(f'  平均查询减少: {avg_query_reduction:.1f}%')
        self.stdout.write(f'  平均审计减少: {avg_audit_reduction:.1f}%')
    
    def save_results_to_file(self, results, filename):
        """保存结果到文件"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        self.stdout.write(f'结果已保存到: {filename}')
    
    def cleanup_test_data(self):
        """清理测试数据"""
        Transaction.objects.filter(operator=self.test_user).delete()
        AuditLog.objects.filter(user=self.test_user).delete()
    
    def cleanup_all_test_data(self):
        """清理所有测试数据"""
        self.cleanup_test_data()
        # 可以选择删除测试商品，但通常保留以便后续测试
        # Product.objects.filter(sku__startswith='PERF').delete()
