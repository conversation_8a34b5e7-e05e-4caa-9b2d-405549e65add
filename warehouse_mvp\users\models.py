from django.db import models
from django.contrib.auth.models import User
from django.contrib.contenttypes.models import ContentType
from django.contrib.contenttypes.fields import GenericForeignKey
from django.core.cache import cache
from django.utils import timezone
import json
import re
from .permissions import UserRoles

# Create your models here.

class UserProfile(models.Model):
    """用户扩展模型，添加角色字段"""

    user = models.OneToOneField(User, on_delete=models.CASCADE, verbose_name='用户')
    role = models.CharField(
        max_length=20,
        choices=UserRoles.CHOICES,
        default=UserRoles.VIEWER,
        verbose_name='角色'
    )

    # 权限缓存字段
    permission_cache = models.JSONField(
        default=dict,
        blank=True,
        verbose_name='权限缓存',
        help_text='缓存用户权限信息，提升性能'
    )
    permission_cache_updated = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name='权限缓存更新时间'
    )

    # 自定义权限设置
    custom_permissions = models.JSONField(
        default=dict,
        blank=True,
        verbose_name='自定义权限',
        help_text='用户特定的权限设置，覆盖角色默认权限'
    )

    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        verbose_name = '用户资料'
        verbose_name_plural = '用户资料'

    def __str__(self):
        return f"{self.user.username} ({self.get_role_display()})"

    def get_role_display_name(self):
        """获取角色显示名称"""
        return UserRoles.get_display_name(self.role)

    def has_permission(self, app_name, action):
        """检查用户是否有特定权限"""
        from .permissions import PermissionManager
        return PermissionManager.user_has_permission(self.user, app_name, action)

    def clear_permission_cache(self):
        """清除权限缓存"""
        self.permission_cache = {}
        self.permission_cache_updated = None
        self.save(update_fields=['permission_cache', 'permission_cache_updated'])

        # 同时清除Redis缓存
        cache_key = f"user_permissions_{self.user.id}"
        cache.delete(cache_key)

    def update_permission_cache(self, permissions_dict):
        """更新权限缓存"""
        self.permission_cache = permissions_dict
        self.permission_cache_updated = timezone.now()
        self.save(update_fields=['permission_cache', 'permission_cache_updated'])

    def get_cached_permissions(self):
        """获取缓存的权限"""
        # 检查缓存是否过期（30分钟）
        if self.permission_cache_updated:
            cache_age = timezone.now() - self.permission_cache_updated
            if cache_age.total_seconds() < 1800:  # 30分钟
                return self.permission_cache
        return None


class AuditLog(models.Model):
    """操作审计日志模型"""

    ACTION_CHOICES = [
        ('CREATE', '创建'),
        ('UPDATE', '更新'),
        ('DELETE', '删除'),
        ('VIEW', '查看'),
    ]

    # 操作用户
    user = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name='操作用户'
    )

    # 操作时间
    timestamp = models.DateTimeField(auto_now_add=True, verbose_name='操作时间')

    # 操作类型
    action = models.CharField(
        max_length=50,  # 增加长度以支持更长的操作类型
        choices=ACTION_CHOICES,
        verbose_name='操作类型'
    )

    # 操作对象信息（使用GenericForeignKey支持任意模型）
    content_type = models.ForeignKey(
        ContentType,
        on_delete=models.CASCADE,
        verbose_name='对象类型'
    )
    object_id = models.PositiveIntegerField(verbose_name='对象ID')
    content_object = GenericForeignKey('content_type', 'object_id')

    # 对象描述（用于删除后的记录）
    object_repr = models.CharField(max_length=200, verbose_name='对象描述')

    # 变更详情（JSON格式存储字段变更前后的值）
    changes = models.JSONField(default=dict, blank=True, verbose_name='变更详情')

    # IP地址
    ip_address = models.GenericIPAddressField(
        null=True,
        blank=True,
        verbose_name='IP地址'
    )

    # 用户代理
    user_agent = models.TextField(blank=True, verbose_name='用户代理')

    # 额外信息
    extra_data = models.JSONField(default=dict, blank=True, verbose_name='额外信息')

    class Meta:
        verbose_name = '操作审计日志'
        verbose_name_plural = '操作审计日志'
        ordering = ['-timestamp']
        indexes = [
            models.Index(fields=['user', 'timestamp']),
            models.Index(fields=['content_type', 'object_id']),
            models.Index(fields=['action', 'timestamp']),
        ]

    def __str__(self):
        user_name = self.user.username if self.user else '系统'
        return f"{user_name} {self.get_action_display()} {self.object_repr} ({self.timestamp.strftime('%Y-%m-%d %H:%M:%S')})"

    def get_changes_display(self):
        """获取变更详情的可读格式"""
        if not self.changes:
            return "无变更"

        changes_list = []
        for field, change_data in self.changes.items():
            if isinstance(change_data, dict) and 'old' in change_data and 'new' in change_data:
                old_val = change_data['old']
                new_val = change_data['new']
                changes_list.append(f"{field}: {old_val} → {new_val}")
            else:
                changes_list.append(f"{field}: {change_data}")

        return "; ".join(changes_list)

    @classmethod
    def log_action(cls, user, action, obj, changes=None, ip_address=None, user_agent=None, extra_data=None):
        """记录操作日志的便捷方法"""
        return cls.objects.create(
            user=user,
            action=action,
            content_type=ContentType.objects.get_for_model(obj),
            object_id=obj.pk,
            object_repr=str(obj),
            changes=changes or {},
            ip_address=ip_address,
            user_agent=user_agent,
            extra_data=extra_data or {}
        )


class PagePermission(models.Model):
    """页面权限模型 - 管理页面级访问权限"""

    # 权限基本信息
    name = models.CharField(max_length=100, unique=True, verbose_name='权限名称')
    description = models.TextField(blank=True, verbose_name='权限描述')

    # URL相关信息
    url_pattern = models.CharField(
        max_length=200,
        verbose_name='URL模式',
        help_text='支持正则表达式，如：^/products/.*$'
    )
    url_name = models.CharField(
        max_length=100,
        blank=True,
        verbose_name='URL名称',
        help_text='Django URL名称，如：products:list'
    )

    # 应用和视图信息
    app_name = models.CharField(
        max_length=50,
        verbose_name='应用名称',
        help_text='Django应用名称，如：products'
    )
    view_name = models.CharField(
        max_length=100,
        blank=True,
        verbose_name='视图名称',
        help_text='视图类或函数名称'
    )

    # 权限分类
    category = models.CharField(
        max_length=50,
        default='general',
        verbose_name='权限分类',
        help_text='权限分类，用于组织管理'
    )

    # 权限级别
    PERMISSION_LEVELS = [
        ('public', '公开访问'),
        ('authenticated', '登录用户'),
        ('role_based', '基于角色'),
        ('custom', '自定义权限'),
    ]
    permission_level = models.CharField(
        max_length=20,
        choices=PERMISSION_LEVELS,
        default='role_based',
        verbose_name='权限级别'
    )

    # 状态控制
    is_active = models.BooleanField(default=True, verbose_name='是否启用')
    is_system = models.BooleanField(
        default=False,
        verbose_name='系统权限',
        help_text='系统权限不可删除'
    )

    # 排序和优先级
    sort_order = models.IntegerField(default=0, verbose_name='排序顺序')
    priority = models.IntegerField(
        default=0,
        verbose_name='优先级',
        help_text='数值越大优先级越高'
    )

    # 审计字段
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='created_page_permissions',
        verbose_name='创建人'
    )
    updated_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='updated_page_permissions',
        verbose_name='最后修改人'
    )

    class Meta:
        verbose_name = '页面权限'
        verbose_name_plural = '页面权限'
        ordering = ['category', 'sort_order', 'name']
        indexes = [
            models.Index(fields=['app_name', 'is_active']),
            models.Index(fields=['url_pattern']),
            models.Index(fields=['category', 'sort_order']),
            models.Index(fields=['permission_level', 'is_active']),
        ]

    def __str__(self):
        return f"{self.name} ({self.app_name})"

    def matches_url(self, url_path):
        """检查URL是否匹配此权限"""
        try:
            pattern = re.compile(self.url_pattern)
            return bool(pattern.match(url_path))
        except re.error:
            return False

    def get_full_permission_key(self):
        """获取完整的权限键"""
        return f"page.{self.app_name}.{self.name}"


class RolePagePermission(models.Model):
    """角色页面权限关联模型"""

    # 角色信息
    role = models.CharField(
        max_length=20,
        choices=UserRoles.CHOICES,
        verbose_name='角色'
    )

    # 页面权限
    page_permission = models.ForeignKey(
        PagePermission,
        on_delete=models.CASCADE,
        verbose_name='页面权限'
    )

    # 权限状态
    is_granted = models.BooleanField(default=True, verbose_name='是否授权')

    # 权限来源
    GRANT_SOURCES = [
        ('default', '默认权限'),
        ('manual', '手动分配'),
        ('inherited', '继承权限'),
        ('template', '模板权限'),
    ]
    grant_source = models.CharField(
        max_length=20,
        choices=GRANT_SOURCES,
        default='manual',
        verbose_name='授权来源'
    )

    # 权限有效期
    valid_from = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name='生效时间'
    )
    valid_until = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name='失效时间'
    )

    # 审计字段
    granted_at = models.DateTimeField(auto_now_add=True, verbose_name='授权时间')
    granted_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='granted_role_permissions',
        verbose_name='授权人'
    )

    # 备注信息
    notes = models.TextField(blank=True, verbose_name='备注')

    class Meta:
        verbose_name = '角色页面权限'
        verbose_name_plural = '角色页面权限'
        unique_together = ['role', 'page_permission']
        ordering = ['role', 'page_permission__category', 'page_permission__sort_order']
        indexes = [
            models.Index(fields=['role', 'is_granted']),
            models.Index(fields=['page_permission', 'is_granted']),
            models.Index(fields=['valid_from', 'valid_until']),
        ]

    def __str__(self):
        status = "授权" if self.is_granted else "拒绝"
        role_name = UserRoles.get_display_name(self.role)
        return f"{role_name} - {self.page_permission.name} ({status})"

    def is_valid(self):
        """检查权限是否在有效期内"""
        now = timezone.now()

        if self.valid_from and now < self.valid_from:
            return False

        if self.valid_until and now > self.valid_until:
            return False

        return True

    def get_permission_key(self):
        """获取权限键"""
        return f"role.{self.role}.{self.page_permission.get_full_permission_key()}"


class CustomPermission(models.Model):
    """用户自定义权限模型"""

    # 用户信息
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        verbose_name='用户'
    )

    # 权限类型
    PERMISSION_TYPES = [
        ('page', '页面权限'),
        ('operation', '操作权限'),
        ('data', '数据权限'),
        ('field', '字段权限'),
        ('custom', '自定义权限'),
    ]
    permission_type = models.CharField(
        max_length=20,
        choices=PERMISSION_TYPES,
        verbose_name='权限类型'
    )

    # 权限键和值
    permission_key = models.CharField(
        max_length=200,
        verbose_name='权限键',
        help_text='权限的唯一标识符'
    )
    permission_value = models.JSONField(
        default=dict,
        verbose_name='权限值',
        help_text='权限的具体配置信息'
    )

    # 权限状态
    is_granted = models.BooleanField(default=True, verbose_name='是否授权')

    # 权限优先级
    priority = models.IntegerField(
        default=0,
        verbose_name='优先级',
        help_text='数值越大优先级越高，用于解决权限冲突'
    )

    # 权限有效期
    valid_from = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name='生效时间'
    )
    valid_until = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name='失效时间'
    )

    # 权限来源和原因
    GRANT_REASONS = [
        ('manual', '手动分配'),
        ('temporary', '临时授权'),
        ('exception', '异常处理'),
        ('delegation', '权限委托'),
        ('system', '系统分配'),
    ]
    grant_reason = models.CharField(
        max_length=20,
        choices=GRANT_REASONS,
        default='manual',
        verbose_name='授权原因'
    )

    # 审计字段
    granted_at = models.DateTimeField(auto_now_add=True, verbose_name='授权时间')
    granted_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='granted_custom_permissions',
        verbose_name='授权人'
    )

    # 备注和描述
    description = models.TextField(blank=True, verbose_name='权限描述')
    notes = models.TextField(blank=True, verbose_name='备注')

    class Meta:
        verbose_name = '自定义权限'
        verbose_name_plural = '自定义权限'
        unique_together = ['user', 'permission_key']
        ordering = ['-priority', '-granted_at']
        indexes = [
            models.Index(fields=['user', 'permission_type', 'is_granted']),
            models.Index(fields=['permission_key', 'is_granted']),
            models.Index(fields=['valid_from', 'valid_until']),
            models.Index(fields=['priority', 'granted_at']),
        ]

    def __str__(self):
        status = "授权" if self.is_granted else "拒绝"
        return f"{self.user.username} - {self.permission_key} ({status})"

    def is_valid(self):
        """检查权限是否在有效期内"""
        now = timezone.now()

        if self.valid_from and now < self.valid_from:
            return False

        if self.valid_until and now > self.valid_until:
            return False

        return True

    def get_effective_permission(self):
        """获取有效的权限配置"""
        if not self.is_valid():
            return None

        return {
            'key': self.permission_key,
            'type': self.permission_type,
            'granted': self.is_granted,
            'value': self.permission_value,
            'priority': self.priority,
        }


class PermissionTemplate(models.Model):
    """权限模板模型 - 用于快速分配权限组合"""

    # 模板基本信息
    name = models.CharField(max_length=100, unique=True, verbose_name='模板名称')
    description = models.TextField(blank=True, verbose_name='模板描述')

    # 模板分类
    category = models.CharField(
        max_length=50,
        default='general',
        verbose_name='模板分类'
    )

    # 适用角色
    target_roles = models.JSONField(
        default=list,
        verbose_name='适用角色',
        help_text='此模板适用的角色列表'
    )

    # 权限配置
    permissions_config = models.JSONField(
        default=dict,
        verbose_name='权限配置',
        help_text='模板包含的权限配置'
    )

    # 模板状态
    is_active = models.BooleanField(default=True, verbose_name='是否启用')
    is_system = models.BooleanField(
        default=False,
        verbose_name='系统模板',
        help_text='系统模板不可删除'
    )

    # 使用统计
    usage_count = models.IntegerField(default=0, verbose_name='使用次数')
    last_used = models.DateTimeField(null=True, blank=True, verbose_name='最后使用时间')

    # 审计字段
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='created_permission_templates',
        verbose_name='创建人'
    )

    class Meta:
        verbose_name = '权限模板'
        verbose_name_plural = '权限模板'
        ordering = ['category', 'name']
        indexes = [
            models.Index(fields=['category', 'is_active']),
            models.Index(fields=['is_system', 'is_active']),
        ]

    def __str__(self):
        return f"{self.name} ({self.category})"

    def apply_to_role(self, role_code, granted_by=None):
        """将模板应用到指定角色"""
        applied_permissions = []

        for permission_key, config in self.permissions_config.items():
            try:
                # 解析权限键
                if permission_key.startswith('page.'):
                    # 页面权限
                    parts = permission_key.split('.')
                    if len(parts) >= 3:
                        app_name = parts[1]
                        permission_name = '.'.join(parts[2:])

                        try:
                            page_permission = PagePermission.objects.get(
                                app_name=app_name,
                                name=permission_name
                            )

                            role_permission, created = RolePagePermission.objects.get_or_create(
                                role=role_code,
                                page_permission=page_permission,
                                defaults={
                                    'is_granted': config.get('granted', True),
                                    'grant_source': 'template',
                                    'granted_by': granted_by,
                                    'notes': f'从模板 {self.name} 应用'
                                }
                            )

                            if not created:
                                role_permission.is_granted = config.get('granted', True)
                                role_permission.grant_source = 'template'
                                role_permission.granted_by = granted_by
                                role_permission.notes = f'从模板 {self.name} 更新'
                                role_permission.save()

                            applied_permissions.append(role_permission)

                        except PagePermission.DoesNotExist:
                            continue

            except Exception:
                continue

        # 更新使用统计
        self.usage_count += 1
        self.last_used = timezone.now()
        self.save(update_fields=['usage_count', 'last_used'])

        return applied_permissions


class MenuConfiguration(models.Model):
    """菜单配置模型"""
    name = models.CharField(max_length=100, verbose_name='菜单名称')
    url_name = models.CharField(max_length=200, verbose_name='URL名称')
    icon_class = models.CharField(max_length=100, verbose_name='图标类名')
    display_text = models.CharField(max_length=100, verbose_name='显示文本')
    parent = models.ForeignKey('self', null=True, blank=True, on_delete=models.CASCADE, verbose_name='父菜单')
    sort_order = models.IntegerField(default=0, verbose_name='排序')
    permission_key = models.CharField(max_length=200, blank=True, verbose_name='权限键')
    app_name = models.CharField(max_length=100, blank=True, verbose_name='应用名称')
    is_active = models.BooleanField(default=True, verbose_name='是否启用')
    is_visible = models.BooleanField(default=True, verbose_name='是否可见')
    css_class = models.CharField(max_length=200, blank=True, verbose_name='CSS类名')
    target = models.CharField(max_length=20, choices=[
        ('_self', '当前窗口'),
        ('_blank', '新窗口'),
        ('_parent', '父窗口'),
        ('_top', '顶层窗口')
    ], default='_self', verbose_name='打开方式')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        verbose_name = '菜单配置'
        verbose_name_plural = '菜单配置'
        ordering = ['sort_order', 'name']

    def __str__(self):
        return self.display_text

    def get_children(self):
        """获取子菜单"""
        return self.menuConfiguration_set.filter(is_active=True).order_by('sort_order')

    def has_permission(self, user):
        """检查用户是否有访问权限"""
        if not self.permission_key:
            return True

        from .permissions import PermissionManager

        if self.app_name:
            return PermissionManager.user_has_permission(user, self.app_name, self.permission_key)
        else:
            return PermissionManager.user_has_permission(user, self.permission_key)

    def is_accessible_by(self, user):
        """检查菜单是否对用户可访问"""
        if not self.is_active or not self.is_visible:
            return False

        if not user.is_authenticated:
            return False

        return self.has_permission(user)


class MenuRolePermission(models.Model):
    """菜单角色权限关联"""
    menu = models.ForeignKey(MenuConfiguration, on_delete=models.CASCADE, verbose_name='菜单')
    role = models.CharField(max_length=50, choices=UserRoles.CHOICES, verbose_name='角色')
    is_granted = models.BooleanField(default=True, verbose_name='是否授权')
    granted_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='授权人')
    granted_at = models.DateTimeField(auto_now_add=True, verbose_name='授权时间')

    class Meta:
        verbose_name = '菜单角色权限'
        verbose_name_plural = '菜单角色权限'
        unique_together = ['menu', 'role']

    def __str__(self):
        return f"{self.menu.display_text} - {self.get_role_display()}"
