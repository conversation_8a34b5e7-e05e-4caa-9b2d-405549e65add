from django.contrib import admin
from .models import Transaction

# Register your models here.

@admin.register(Transaction)
class TransactionAdmin(admin.ModelAdmin):
    list_display = ['product', 'transaction_type', 'quantity', 'reason', 'operator', 'created_at']
    list_filter = ['transaction_type', 'created_at', 'operator']
    search_fields = ['product__name', 'product__sku', 'reason']
    readonly_fields = ['created_at']
    date_hierarchy = 'created_at'

    def delete_model(self, request, obj):
        """重写删除方法，记录审计日志"""
        from users.services import AuditService

        # 手动记录审计日志，传入当前用户
        AuditService.log_delete(
            instance=obj,
            user=request.user,
            request=request,
            extra_data={
                'model': 'Transaction',
                'operation': 'admin_delete_transaction',
                'admin_action': True
            }
        )

        # 执行实际删除
        super().delete_model(request, obj)

    def delete_queryset(self, request, queryset):
        """重写批量删除方法，记录审计日志"""
        from users.services import AuditService

        # 为每个对象记录审计日志
        for obj in queryset:
            AuditService.log_delete(
                instance=obj,
                user=request.user,
                request=request,
                extra_data={
                    'model': 'Transaction',
                    'operation': 'admin_batch_delete_transaction',
                    'admin_action': True
                }
            )

        # 执行实际批量删除
        super().delete_queryset(request, queryset)
