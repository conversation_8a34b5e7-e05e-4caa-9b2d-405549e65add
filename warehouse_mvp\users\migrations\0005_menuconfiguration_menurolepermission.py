# Generated by Django 4.1.2 on 2025-07-30 07:52

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("users", "0004_pagepermission_userprofile_custom_permissions_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="MenuConfiguration",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100, verbose_name="菜单名称")),
                ("url_name", models.CharField(max_length=200, verbose_name="URL名称")),
                (
                    "icon_class",
                    models.CharField(max_length=100, verbose_name="图标类名"),
                ),
                (
                    "display_text",
                    models.CharField(max_length=100, verbose_name="显示文本"),
                ),
                ("sort_order", models.IntegerField(default=0, verbose_name="排序")),
                (
                    "permission_key",
                    models.Char<PERSON>ield(blank=True, max_length=200, verbose_name="权限键"),
                ),
                (
                    "app_name",
                    models.CharField(
                        blank=True, max_length=100, verbose_name="应用名称"
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="是否启用"),
                ),
                (
                    "is_visible",
                    models.BooleanField(default=True, verbose_name="是否可见"),
                ),
                (
                    "css_class",
                    models.CharField(
                        blank=True, max_length=200, verbose_name="CSS类名"
                    ),
                ),
                (
                    "target",
                    models.CharField(
                        choices=[
                            ("_self", "当前窗口"),
                            ("_blank", "新窗口"),
                            ("_parent", "父窗口"),
                            ("_top", "顶层窗口"),
                        ],
                        default="_self",
                        max_length=20,
                        verbose_name="打开方式",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "parent",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="users.menuconfiguration",
                        verbose_name="父菜单",
                    ),
                ),
            ],
            options={
                "verbose_name": "菜单配置",
                "verbose_name_plural": "菜单配置",
                "ordering": ["sort_order", "name"],
            },
        ),
        migrations.CreateModel(
            name="MenuRolePermission",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "role",
                    models.CharField(
                        choices=[
                            ("super_admin", "超级管理员"),
                            ("warehouse_manager", "仓库管理员"),
                            ("warehouse_operator", "仓库操作员"),
                            ("viewer", "查看员"),
                        ],
                        max_length=50,
                        verbose_name="角色",
                    ),
                ),
                (
                    "is_granted",
                    models.BooleanField(default=True, verbose_name="是否授权"),
                ),
                (
                    "granted_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="授权时间"),
                ),
                (
                    "granted_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="授权人",
                    ),
                ),
                (
                    "menu",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="users.menuconfiguration",
                        verbose_name="菜单",
                    ),
                ),
            ],
            options={
                "verbose_name": "菜单角色权限",
                "verbose_name_plural": "菜单角色权限",
                "unique_together": {("menu", "role")},
            },
        ),
    ]
