# Generated by Django 4.1.2 on 2025-07-29 09:44

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("products", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="Inventory",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "quantity",
                    models.PositiveIntegerField(default=0, verbose_name="库存数量"),
                ),
                (
                    "min_stock",
                    models.PositiveIntegerField(default=10, verbose_name="最低库存"),
                ),
                (
                    "max_stock",
                    models.PositiveIntegerField(default=1000, verbose_name="最高库存"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "product",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="products.product",
                        verbose_name="商品",
                    ),
                ),
            ],
            options={
                "verbose_name": "库存",
                "verbose_name_plural": "库存",
            },
        ),
    ]
